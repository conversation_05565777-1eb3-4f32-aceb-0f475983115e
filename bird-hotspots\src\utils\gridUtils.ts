import type { ObservationPoint, GridCell } from '../types';

/**
 * 根据缩放级别获取网格大小
 * 缩放级别越高，网格越小，显示越详细
 */
export function getGridSize(zoomLevel: number): number {
  if (zoomLevel <= 4) return 2.0;      // 2度网格 - 国家级别
  if (zoomLevel <= 6) return 1.0;      // 1度网格 - 省级别
  if (zoomLevel <= 8) return 0.5;      // 0.5度网格 - 市级别
  if (zoomLevel <= 10) return 0.2;     // 0.2度网格 - 区县级别
  if (zoomLevel <= 12) return 0.1;     // 0.1度网格 - 街道级别
  if (zoomLevel <= 14) return 0.05;    // 0.05度网格 - 社区级别
  return 0.02;                         // 0.02度网格 - 详细级别
}

/**
 * 缩放级别配置
 */
export const ZOOM_CONFIG = {
  // 显示具体数据点的最小缩放级别（降低2-3级，对应地图比例尺约1:50000）
  POINTS_THRESHOLD: 12,
  // 可选的更高精度阈值（相应调整）
  HIGH_DETAIL_THRESHOLD: 15,
  // 超高精度阈值（显示所有细节）
  ULTRA_DETAIL_THRESHOLD: 18,
  // 最大显示点数量（基础模式，适当减少以保持性能）
  MAX_POINTS_DISPLAY: 1200,
  // 最大显示点数量（高精度模式）
  MAX_POINTS_HIGH_DETAIL: 2500,
  // 最大显示点数量（超高精度模式）
  MAX_POINTS_ULTRA_DETAIL: 4500,
  // 视窗内优先加载的点数量（适当增加以改善用户体验）
  VIEWPORT_PRIORITY_POINTS: 600
};

/**
 * 计算地图比例尺（近似值）
 * @param zoomLevel 缩放级别
 * @param latitude 纬度（用于修正）
 * @returns 比例尺分母（例如：10000 表示 1:10000）
 */
export function calculateMapScale(zoomLevel: number, latitude: number = 0): number {
  // 基础比例尺计算公式（Web墨卡托投影）
  // 在赤道处，缩放级别0对应约1:500,000,000的比例尺
  const baseScale = 500000000;
  const scale = baseScale / Math.pow(2, zoomLevel);

  // 根据纬度修正（高纬度地区比例尺会变小）
  const latitudeCorrection = Math.cos(latitude * Math.PI / 180);

  return Math.round(scale * latitudeCorrection);
}

/**
 * 获取缩放阈值，决定何时显示网格vs具体点
 * @param zoomLevel 当前缩放级别
 * @param latitude 当前纬度（用于比例尺计算）
 * @param customThreshold 自定义阈值（可选）
 */
export function getDisplayThreshold(
  zoomLevel: number,
  latitude?: number,
  customThreshold?: number
): 'grid' | 'points' {
  const threshold = customThreshold || ZOOM_CONFIG.POINTS_THRESHOLD;

  // 主要基于缩放级别判断
  if (zoomLevel >= threshold) {
    return 'points';
  }

  // 辅助判断：如果提供了纬度，也检查比例尺
  // 调整比例尺阈值，使其在更低缩放级别时就切换到坐标点模式
  if (latitude !== undefined) {
    const scale = calculateMapScale(zoomLevel, latitude);
    // 将比例尺阈值从10000提高到50000，使其更容易切换到点模式
    if (scale <= 50000) {
      return 'points';
    }
  }

  // 额外的渐进式切换逻辑：在接近阈值时提前切换
  if (zoomLevel >= threshold - 1) {
    return 'points';
  }

  return 'grid';
}

/**
 * 检查是否为高精度显示模式
 */
export function isHighDetailMode(zoomLevel: number): boolean {
  return zoomLevel >= ZOOM_CONFIG.HIGH_DETAIL_THRESHOLD;
}

/**
 * 检查是否为超高精度显示模式
 */
export function isUltraDetailMode(zoomLevel: number): boolean {
  return zoomLevel >= ZOOM_CONFIG.ULTRA_DETAIL_THRESHOLD;
}

/**
 * 获取详细程度级别
 */
export function getDetailLevel(zoomLevel: number): 'grid' | 'points' | 'high' | 'ultra' {
  if (zoomLevel >= ZOOM_CONFIG.ULTRA_DETAIL_THRESHOLD) return 'ultra';
  if (zoomLevel >= ZOOM_CONFIG.HIGH_DETAIL_THRESHOLD) return 'high';
  if (zoomLevel >= ZOOM_CONFIG.POINTS_THRESHOLD) return 'points';
  return 'grid';
}

/**
 * 将经纬度坐标转换为网格坐标
 */
export function getGridCoordinates(lat: number, lng: number, gridSize: number): { gridLat: number; gridLng: number } {
  return {
    gridLat: Math.floor(lat / gridSize) * gridSize,
    gridLng: Math.floor(lng / gridSize) * gridSize
  };
}

/**
 * 根据网格坐标和大小计算网格边界
 */
export function getGridBounds(gridLat: number, gridLng: number, gridSize: number) {
  return {
    north: gridLat + gridSize,
    south: gridLat,
    east: gridLng + gridSize,
    west: gridLng
  };
}

/**
 * 创建网格单元的唯一键
 */
export function getGridKey(gridLat: number, gridLng: number): string {
  return `${gridLat.toFixed(6)},${gridLng.toFixed(6)}`;
}

/**
 * 将观察数据聚合到网格中
 */
export function aggregateObservationsToGrid(
  observations: ObservationPoint[],
  gridSize: number,
  speciesFilter?: string[]
): Map<string, GridCell> {
  const gridMap = new Map<string, GridCell>();

  // 过滤观察数据
  const filteredObservations = speciesFilter 
    ? observations.filter(obs => speciesFilter.includes(obs.speciesId))
    : observations;

  filteredObservations.forEach(obs => {
    const { gridLat, gridLng } = getGridCoordinates(obs.lat, obs.lng, gridSize);
    const gridKey = getGridKey(gridLat, gridLng);
    
    if (!gridMap.has(gridKey)) {
      // 创建新的网格单元
      gridMap.set(gridKey, {
        gridLat,
        gridLng,
        bounds: getGridBounds(gridLat, gridLng, gridSize),
        totalCount: 0,
        observationCount: 0,
        averageIntensity: 0,
        speciesIds: [],
        observations: []
      });
    }

    const gridCell = gridMap.get(gridKey)!;
    
    // 更新网格单元数据
    gridCell.totalCount += obs.count;
    gridCell.observationCount += 1;
    gridCell.observations.push(obs);
    
    // 更新物种列表
    if (!gridCell.speciesIds.includes(obs.speciesId)) {
      gridCell.speciesIds.push(obs.speciesId);
    }
  });

  // 计算平均强度
  gridMap.forEach(gridCell => {
    if (gridCell.observationCount > 0) {
      gridCell.averageIntensity = gridCell.observations.reduce((sum, obs) => sum + obs.intensity, 0) / gridCell.observationCount;
    }
  });

  return gridMap;
}

/**
 * 根据地图边界过滤网格单元
 */
export function filterGridByBounds(
  gridMap: Map<string, GridCell>,
  bounds: { north: number; south: number; east: number; west: number }
): Map<string, GridCell> {
  const filteredGrid = new Map<string, GridCell>();

  gridMap.forEach((gridCell, key) => {
    // 检查网格是否与地图边界相交
    if (gridCell.bounds.north >= bounds.south &&
        gridCell.bounds.south <= bounds.north &&
        gridCell.bounds.east >= bounds.west &&
        gridCell.bounds.west <= bounds.east) {
      filteredGrid.set(key, gridCell);
    }
  });

  return filteredGrid;
}

/**
 * 计算网格单元的显示优先级（用于性能优化）
 */
export function calculateGridPriority(gridCell: GridCell, mapCenter: [number, number]): number {
  const centerLat = mapCenter[0];
  const centerLng = mapCenter[1];
  const gridCenterLat = gridCell.gridLat + (gridCell.bounds.north - gridCell.bounds.south) / 2;
  const gridCenterLng = gridCell.gridLng + (gridCell.bounds.east - gridCell.bounds.west) / 2;
  
  // 计算距离地图中心的距离
  const distance = Math.sqrt(
    Math.pow(gridCenterLat - centerLat, 2) + 
    Math.pow(gridCenterLng - centerLng, 2)
  );
  
  // 优先级 = 观察数量 / 距离，距离越近、观察数量越多优先级越高
  return gridCell.totalCount / (distance + 0.1);
}

/**
 * 获取网格单元的统计信息文本
 */
export function getGridStatsText(gridCell: GridCell): string {
  const speciesCount = gridCell.speciesIds.length;
  const avgIntensity = (gridCell.averageIntensity * 100).toFixed(1);
  
  return `
    <div class="text-sm p-2">
      <div class="font-bold text-gray-800 mb-2">网格区域统计</div>
      <div class="space-y-1">
        <div class="flex justify-between">
          <span class="text-gray-700">观察总数:</span>
          <span class="font-bold text-blue-600">${gridCell.totalCount}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-700">观察点数:</span>
          <span class="font-medium text-green-600">${gridCell.observationCount}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-700">物种数量:</span>
          <span class="font-medium text-purple-600">${speciesCount}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-700">平均强度:</span>
          <span class="font-medium text-orange-600">${avgIntensity}%</span>
        </div>
      </div>
    </div>
  `;
}

/**
 * 性能优化：限制显示的网格数量
 */
export function limitGridCells(
  gridMap: Map<string, GridCell>,
  maxCells: number = 1000,
  mapCenter: [number, number]
): Map<string, GridCell> {
  if (gridMap.size <= maxCells) {
    return gridMap;
  }

  // 按优先级排序并限制数量
  const sortedCells = Array.from(gridMap.entries())
    .map(([key, cell]) => ({
      key,
      cell,
      priority: calculateGridPriority(cell, mapCenter)
    }))
    .sort((a, b) => b.priority - a.priority)
    .slice(0, maxCells);

  const limitedGrid = new Map<string, GridCell>();
  sortedCells.forEach(({ key, cell }) => {
    limitedGrid.set(key, cell);
  });

  return limitedGrid;
}

/**
 * 检查观察点是否在视窗内
 */
export function isPointInViewport(
  point: ObservationPoint,
  mapBounds: { north: number; south: number; east: number; west: number }
): boolean {
  return point.lat >= mapBounds.south &&
         point.lat <= mapBounds.north &&
         point.lng >= mapBounds.west &&
         point.lng <= mapBounds.east;
}

/**
 * 按视窗优先级过滤观察点
 */
export function filterObservationsByViewport(
  observations: ObservationPoint[],
  mapBounds: { north: number; south: number; east: number; west: number },
  mapCenter: [number, number],
  priorityPoints: number = ZOOM_CONFIG.VIEWPORT_PRIORITY_POINTS
): { inViewport: ObservationPoint[]; nearViewport: ObservationPoint[] } {
  const inViewport: ObservationPoint[] = [];
  const nearViewport: ObservationPoint[] = [];

  // 计算视窗扩展边界（用于预加载附近的点）
  const latRange = mapBounds.north - mapBounds.south;
  const lngRange = mapBounds.east - mapBounds.west;
  const expandedBounds = {
    north: mapBounds.north + latRange * 0.5,
    south: mapBounds.south - latRange * 0.5,
    east: mapBounds.east + lngRange * 0.5,
    west: mapBounds.west - lngRange * 0.5
  };

  observations.forEach(obs => {
    if (isPointInViewport(obs, mapBounds)) {
      inViewport.push(obs);
    } else if (isPointInViewport(obs, expandedBounds)) {
      nearViewport.push(obs);
    }
  });

  // 对附近的点按优先级排序
  nearViewport.sort((a, b) => {
    const priorityA = calculateObservationPriority(a, mapCenter);
    const priorityB = calculateObservationPriority(b, mapCenter);
    return priorityB - priorityA;
  });

  return {
    inViewport,
    nearViewport: nearViewport.slice(0, priorityPoints)
  };
}

/**
 * 性能优化：限制显示的观察点数量
 */
export function limitObservationPoints(
  observations: ObservationPoint[],
  maxPoints: number,
  mapCenter: [number, number],
  mapBounds?: { north: number; south: number; east: number; west: number }
): ObservationPoint[] {
  // 如果提供了地图边界，优先使用视窗过滤
  if (mapBounds) {
    const { inViewport, nearViewport } = filterObservationsByViewport(
      observations,
      mapBounds,
      mapCenter
    );

    // 优先显示视窗内的点，然后是附近的点
    const prioritizedPoints = [...inViewport, ...nearViewport];

    if (prioritizedPoints.length <= maxPoints) {
      return prioritizedPoints;
    }

    // 如果还是太多，按优先级进一步筛选
    return prioritizedPoints
      .map(obs => ({
        observation: obs,
        priority: calculateObservationPriority(obs, mapCenter)
      }))
      .sort((a, b) => b.priority - a.priority)
      .slice(0, maxPoints)
      .map(item => item.observation);
  }

  // 原有的逻辑作为后备
  if (observations.length <= maxPoints) {
    return observations;
  }

  // 按优先级排序：距离中心近的和观察数量多的优先显示
  const sortedObservations = observations
    .map(obs => ({
      observation: obs,
      priority: calculateObservationPriority(obs, mapCenter)
    }))
    .sort((a, b) => b.priority - a.priority)
    .slice(0, maxPoints)
    .map(item => item.observation);

  return sortedObservations;
}

/**
 * 计算观察点的显示优先级
 */
function calculateObservationPriority(
  observation: ObservationPoint,
  mapCenter: [number, number]
): number {
  // 计算距离地图中心的距离
  const distance = Math.sqrt(
    Math.pow(observation.lat - mapCenter[0], 2) +
    Math.pow(observation.lng - mapCenter[1], 2)
  );

  // 综合考虑距离和观察数量
  // 距离越近优先级越高，观察数量越多优先级越高
  const distanceScore = 1 / (1 + distance);
  const countScore = Math.log10(Math.max(1, observation.count));
  const intensityScore = observation.intensity;

  return distanceScore * 0.4 + countScore * 0.4 + intensityScore * 0.2;
}
