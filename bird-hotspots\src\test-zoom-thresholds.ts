// 测试新的缩放阈值配置
import { 
  ZOOM_CONFIG, 
  getDisplayThreshold, 
  getDetailLevel,
  calculateMapScale 
} from './utils/gridUtils';

console.log('=== 缩放阈值配置测试 ===');
console.log('新的ZOOM_CONFIG配置：', ZOOM_CONFIG);

console.log('\n=== 不同缩放级别的显示模式测试 ===');
const testZoomLevels = [8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20];
const testLatitude = 39.9; // 北京纬度

testZoomLevels.forEach(zoom => {
  const displayMode = getDisplayThreshold(zoom, testLatitude);
  const detailLevel = getDetailLevel(zoom);
  const mapScale = calculateMapScale(zoom, testLatitude);
  
  console.log(`缩放级别 ${zoom}:`);
  console.log(`  - 显示模式: ${displayMode}`);
  console.log(`  - 详细级别: ${detailLevel}`);
  console.log(`  - 地图比例尺: 1:${mapScale.toLocaleString()}`);
  console.log(`  - 是否达到坐标点阈值: ${zoom >= ZOOM_CONFIG.POINTS_THRESHOLD ? '是' : '否'}`);
  console.log('');
});

console.log('=== 性能限制配置 ===');
console.log(`基础模式最大点数: ${ZOOM_CONFIG.MAX_POINTS_DISPLAY.toLocaleString()}`);
console.log(`高精度模式最大点数: ${ZOOM_CONFIG.MAX_POINTS_HIGH_DETAIL.toLocaleString()}`);
console.log(`超高精度模式最大点数: ${ZOOM_CONFIG.MAX_POINTS_ULTRA_DETAIL.toLocaleString()}`);
console.log(`视窗优先点数: ${ZOOM_CONFIG.VIEWPORT_PRIORITY_POINTS.toLocaleString()}`);

console.log('\n=== 关键阈值对比 ===');
console.log('旧配置 -> 新配置：');
console.log('坐标点阈值: 15 -> 12 (降低3级)');
console.log('高精度阈值: 18 -> 15 (降低3级)');
console.log('超高精度阈值: 20 -> 18 (降低2级)');
console.log('基础模式点数: 1500 -> 1200 (减少300点)');

export { testZoomLevels, ZOOM_CONFIG };
