import { useEffect, useRef } from 'react';
import L from 'leaflet';
import { useAppStore } from '../store/useAppStore';
import {
  getColorByCount,
  getSquareSize,
  getSquareOpacity,
  getSquareClassName
} from '../utils/colorUtils';

// Import heat layer plugin
import 'leaflet.heat';

interface HeatmapLayerProps {
  map: L.Map | null;
}

const HeatmapLayer = ({ map }: HeatmapLayerProps) => {
  const layerGroupsRef = useRef<{ [key: string]: L.LayerGroup }>({});
  const { species, observations, searchQuery } = useAppStore();

  useEffect(() => {
    if (!map) return;

    // Clear existing layer groups
    Object.values(layerGroupsRef.current).forEach(layerGroup => {
      if (map.hasLayer(layerGroup)) {
        map.removeLayer(layerGroup);
      }
    });
    layerGroupsRef.current = {};

    // Filter species based on search query
    const filteredSpecies = species.filter(speciesItem => {
      if (!speciesItem.isVisible) return false;

      // If there's a search query, filter by species name or scientific name
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        return speciesItem.name.toLowerCase().includes(query) ||
               speciesItem.scientificName.toLowerCase().includes(query);
      }

      return true;
    });

    // Create layer groups for each filtered species
    filteredSpecies.forEach(speciesItem => {

      // Filter observations for this species
      const speciesObservations = observations.filter(
        obs => obs.speciesId === speciesItem.id
      );

      if (speciesObservations.length === 0) return;

      // Create layer group for this species
      const layerGroup = L.layerGroup();

      // Add square markers for each observation
      speciesObservations.forEach(obs => {
        // Calculate square properties based on count
        const squareSize = getSquareSize(obs.count);
        const squareColor = getColorByCount(obs.count);
        const squareOpacity = getSquareOpacity(obs.count);
        const squareClassName = getSquareClassName(obs.count);

        // Create custom div icon for square hotspot
        const squareIcon = L.divIcon({
          className: squareClassName,
          html: `<div style="
            width: ${squareSize}px;
            height: ${squareSize}px;
            background-color: ${squareColor};
            opacity: ${squareOpacity};
            border-radius: 2px;
            border: 1px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          "></div>`,
          iconSize: [squareSize, squareSize],
          iconAnchor: [squareSize / 2, squareSize / 2],
          popupAnchor: [0, -squareSize / 2]
        });

        // Create marker with square icon
        const marker = L.marker([obs.lat, obs.lng], {
          icon: squareIcon,
          riseOnHover: true
        });

        // Add popup with observation details
        marker.bindPopup(`
          <div class="text-sm p-2">
            <div class="font-bold text-gray-800 mb-2">${speciesItem.name}</div>
            <div class="text-gray-600 italic mb-2">${speciesItem.scientificName}</div>
            <div class="flex items-center justify-between">
              <span class="text-gray-700">观察数量:</span>
              <span class="font-bold text-blue-600">${obs.count}</span>
            </div>
            <div class="flex items-center justify-between mt-1">
              <span class="text-gray-700">强度:</span>
              <span class="font-medium text-green-600">${(obs.intensity * 100).toFixed(1)}%</span>
            </div>
          </div>
        `, {
          maxWidth: 200,
          className: 'hotspot-popup'
        });

        layerGroup.addLayer(marker);
      });

      // Add to map and store reference
      layerGroup.addTo(map);
      layerGroupsRef.current[speciesItem.id] = layerGroup;
    });

    // Cleanup function
    return () => {
      Object.values(layerGroupsRef.current).forEach(layerGroup => {
        if (map.hasLayer(layerGroup)) {
          map.removeLayer(layerGroup);
        }
      });
    };
  }, [map, species, observations, searchQuery]);

  return null; // This component doesn't render anything
};

export default HeatmapLayer;
