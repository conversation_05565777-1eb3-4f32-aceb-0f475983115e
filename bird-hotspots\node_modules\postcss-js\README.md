# PostCSS JS

<img align="right" width="135" height="95"
     title="Philoso<PERSON>’s stone, logo of PostCSS"
     src="https://postcss.org/logo-leftp.svg">

[PostCSS] for CSS-in-JS and styles in JS objects.

For example, to use [Stylelint] or [RTLCSS] plugins in your workflow.

<a href="https://evilmartians.com/?utm_source=postcss-js">
  <img src="https://evilmartians.com/badges/sponsored-by-evil-martians.svg"
       alt="Sponsored by Evil Martians" width="236" height="54">
</a>

[Stylelint]: https://github.com/stylelint/stylelint
[PostCSS]:   https://github.com/postcss/postcss
[RTLCSS]:    https://github.com/MohammadYounes/rtlcss


## Docs
Read full docs **[here](https://github.com/postcss/postcss-js#readme)**.
