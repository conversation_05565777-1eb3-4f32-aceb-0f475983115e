{"version": 3, "sources": ["../../leaflet.heat/dist/leaflet-heat.js"], "sourcesContent": ["/*\n (c) 2014, <PERSON>\n simpleheat, a tiny JavaScript library for drawing heatmaps with Canvas\n https://github.com/mourner/simpleheat\n*/\n!function(){\"use strict\";function t(i){return this instanceof t?(this._canvas=i=\"string\"==typeof i?document.getElementById(i):i,this._ctx=i.getContext(\"2d\"),this._width=i.width,this._height=i.height,this._max=1,void this.clear()):new t(i)}t.prototype={defaultRadius:25,defaultGradient:{.4:\"blue\",.6:\"cyan\",.7:\"lime\",.8:\"yellow\",1:\"red\"},data:function(t,i){return this._data=t,this},max:function(t){return this._max=t,this},add:function(t){return this._data.push(t),this},clear:function(){return this._data=[],this},radius:function(t,i){i=i||15;var a=this._circle=document.createElement(\"canvas\"),s=a.getContext(\"2d\"),e=this._r=t+i;return a.width=a.height=2*e,s.shadowOffsetX=s.shadowOffsetY=200,s.shadowBlur=i,s.shadowColor=\"black\",s.beginPath(),s.arc(e-200,e-200,t,0,2*Math.PI,!0),s.closePath(),s.fill(),this},gradient:function(t){var i=document.createElement(\"canvas\"),a=i.getContext(\"2d\"),s=a.createLinearGradient(0,0,0,256);i.width=1,i.height=256;for(var e in t)s.addColorStop(e,t[e]);return a.fillStyle=s,a.fillRect(0,0,1,256),this._grad=a.getImageData(0,0,1,256).data,this},draw:function(t){this._circle||this.radius(this.defaultRadius),this._grad||this.gradient(this.defaultGradient);var i=this._ctx;i.clearRect(0,0,this._width,this._height);for(var a,s=0,e=this._data.length;e>s;s++)a=this._data[s],i.globalAlpha=Math.max(a[2]/this._max,t||.05),i.drawImage(this._circle,a[0]-this._r,a[1]-this._r);var n=i.getImageData(0,0,this._width,this._height);return this._colorize(n.data,this._grad),i.putImageData(n,0,0),this},_colorize:function(t,i){for(var a,s=3,e=t.length;e>s;s+=4)a=4*t[s],a&&(t[s-3]=i[a],t[s-2]=i[a+1],t[s-1]=i[a+2])}},window.simpleheat=t}(),/*\n (c) 2014, Vladimir Agafonkin\n Leaflet.heat, a tiny and fast heatmap plugin for Leaflet.\n https://github.com/Leaflet/Leaflet.heat\n*/\nL.HeatLayer=(L.Layer?L.Layer:L.Class).extend({initialize:function(t,i){this._latlngs=t,L.setOptions(this,i)},setLatLngs:function(t){return this._latlngs=t,this.redraw()},addLatLng:function(t){return this._latlngs.push(t),this.redraw()},setOptions:function(t){return L.setOptions(this,t),this._heat&&this._updateOptions(),this.redraw()},redraw:function(){return!this._heat||this._frame||this._map._animating||(this._frame=L.Util.requestAnimFrame(this._redraw,this)),this},onAdd:function(t){this._map=t,this._canvas||this._initCanvas(),t._panes.overlayPane.appendChild(this._canvas),t.on(\"moveend\",this._reset,this),t.options.zoomAnimation&&L.Browser.any3d&&t.on(\"zoomanim\",this._animateZoom,this),this._reset()},onRemove:function(t){t.getPanes().overlayPane.removeChild(this._canvas),t.off(\"moveend\",this._reset,this),t.options.zoomAnimation&&t.off(\"zoomanim\",this._animateZoom,this)},addTo:function(t){return t.addLayer(this),this},_initCanvas:function(){var t=this._canvas=L.DomUtil.create(\"canvas\",\"leaflet-heatmap-layer leaflet-layer\"),i=L.DomUtil.testProp([\"transformOrigin\",\"WebkitTransformOrigin\",\"msTransformOrigin\"]);t.style[i]=\"50% 50%\";var a=this._map.getSize();t.width=a.x,t.height=a.y;var s=this._map.options.zoomAnimation&&L.Browser.any3d;L.DomUtil.addClass(t,\"leaflet-zoom-\"+(s?\"animated\":\"hide\")),this._heat=simpleheat(t),this._updateOptions()},_updateOptions:function(){this._heat.radius(this.options.radius||this._heat.defaultRadius,this.options.blur),this.options.gradient&&this._heat.gradient(this.options.gradient),this.options.max&&this._heat.max(this.options.max)},_reset:function(){var t=this._map.containerPointToLayerPoint([0,0]);L.DomUtil.setPosition(this._canvas,t);var i=this._map.getSize();this._heat._width!==i.x&&(this._canvas.width=this._heat._width=i.x),this._heat._height!==i.y&&(this._canvas.height=this._heat._height=i.y),this._redraw()},_redraw:function(){var t,i,a,s,e,n,h,o,r,d=[],_=this._heat._r,l=this._map.getSize(),m=new L.Bounds(L.point([-_,-_]),l.add([_,_])),c=void 0===this.options.max?1:this.options.max,u=void 0===this.options.maxZoom?this._map.getMaxZoom():this.options.maxZoom,f=1/Math.pow(2,Math.max(0,Math.min(u-this._map.getZoom(),12))),g=_/2,p=[],v=this._map._getMapPanePos(),w=v.x%g,y=v.y%g;for(t=0,i=this._latlngs.length;i>t;t++)if(a=this._map.latLngToContainerPoint(this._latlngs[t]),m.contains(a)){e=Math.floor((a.x-w)/g)+2,n=Math.floor((a.y-y)/g)+2;var x=void 0!==this._latlngs[t].alt?this._latlngs[t].alt:void 0!==this._latlngs[t][2]?+this._latlngs[t][2]:1;r=x*f,p[n]=p[n]||[],s=p[n][e],s?(s[0]=(s[0]*s[2]+a.x*r)/(s[2]+r),s[1]=(s[1]*s[2]+a.y*r)/(s[2]+r),s[2]+=r):p[n][e]=[a.x,a.y,r]}for(t=0,i=p.length;i>t;t++)if(p[t])for(h=0,o=p[t].length;o>h;h++)s=p[t][h],s&&d.push([Math.round(s[0]),Math.round(s[1]),Math.min(s[2],c)]);this._heat.data(d).draw(this.options.minOpacity),this._frame=null},_animateZoom:function(t){var i=this._map.getZoomScale(t.zoom),a=this._map._getCenterOffset(t.center)._multiplyBy(-i).subtract(this._map._getMapPanePos());L.DomUtil.setTransform?L.DomUtil.setTransform(this._canvas,a,i):this._canvas.style[L.DomUtil.TRANSFORM]=L.DomUtil.getTranslateString(a)+\" scale(\"+i+\")\"}}),L.heatLayer=function(t,i){return new L.HeatLayer(t,i)};"], "mappings": ";AAKA,CAAC,WAAU;AAAC;AAAa,WAAS,EAAE,GAAE;AAAC,WAAO,gBAAgB,KAAG,KAAK,UAAQ,IAAE,YAAU,OAAO,IAAE,SAAS,eAAe,CAAC,IAAE,GAAE,KAAK,OAAK,EAAE,WAAW,IAAI,GAAE,KAAK,SAAO,EAAE,OAAM,KAAK,UAAQ,EAAE,QAAO,KAAK,OAAK,GAAE,KAAK,KAAK,MAAM,KAAG,IAAI,EAAE,CAAC;AAAA,EAAC;AAAC,IAAE,YAAU,EAAC,eAAc,IAAG,iBAAgB,EAAC,KAAG,QAAO,KAAG,QAAO,KAAG,QAAO,KAAG,UAAS,GAAE,MAAK,GAAE,MAAK,SAASA,IAAE,GAAE;AAAC,WAAO,KAAK,QAAMA,IAAE;AAAA,EAAI,GAAE,KAAI,SAASA,IAAE;AAAC,WAAO,KAAK,OAAKA,IAAE;AAAA,EAAI,GAAE,KAAI,SAASA,IAAE;AAAC,WAAO,KAAK,MAAM,KAAKA,EAAC,GAAE;AAAA,EAAI,GAAE,OAAM,WAAU;AAAC,WAAO,KAAK,QAAM,CAAC,GAAE;AAAA,EAAI,GAAE,QAAO,SAASA,IAAE,GAAE;AAAC,QAAE,KAAG;AAAG,QAAI,IAAE,KAAK,UAAQ,SAAS,cAAc,QAAQ,GAAE,IAAE,EAAE,WAAW,IAAI,GAAE,IAAE,KAAK,KAAGA,KAAE;AAAE,WAAO,EAAE,QAAM,EAAE,SAAO,IAAE,GAAE,EAAE,gBAAc,EAAE,gBAAc,KAAI,EAAE,aAAW,GAAE,EAAE,cAAY,SAAQ,EAAE,UAAU,GAAE,EAAE,IAAI,IAAE,KAAI,IAAE,KAAIA,IAAE,GAAE,IAAE,KAAK,IAAG,IAAE,GAAE,EAAE,UAAU,GAAE,EAAE,KAAK,GAAE;AAAA,EAAI,GAAE,UAAS,SAASA,IAAE;AAAC,QAAI,IAAE,SAAS,cAAc,QAAQ,GAAE,IAAE,EAAE,WAAW,IAAI,GAAE,IAAE,EAAE,qBAAqB,GAAE,GAAE,GAAE,GAAG;AAAE,MAAE,QAAM,GAAE,EAAE,SAAO;AAAI,aAAQ,KAAKA,GAAE,GAAE,aAAa,GAAEA,GAAE,CAAC,CAAC;AAAE,WAAO,EAAE,YAAU,GAAE,EAAE,SAAS,GAAE,GAAE,GAAE,GAAG,GAAE,KAAK,QAAM,EAAE,aAAa,GAAE,GAAE,GAAE,GAAG,EAAE,MAAK;AAAA,EAAI,GAAE,MAAK,SAASA,IAAE;AAAC,SAAK,WAAS,KAAK,OAAO,KAAK,aAAa,GAAE,KAAK,SAAO,KAAK,SAAS,KAAK,eAAe;AAAE,QAAI,IAAE,KAAK;AAAK,MAAE,UAAU,GAAE,GAAE,KAAK,QAAO,KAAK,OAAO;AAAE,aAAQ,GAAE,IAAE,GAAE,IAAE,KAAK,MAAM,QAAO,IAAE,GAAE,IAAI,KAAE,KAAK,MAAM,CAAC,GAAE,EAAE,cAAY,KAAK,IAAI,EAAE,CAAC,IAAE,KAAK,MAAKA,MAAG,IAAG,GAAE,EAAE,UAAU,KAAK,SAAQ,EAAE,CAAC,IAAE,KAAK,IAAG,EAAE,CAAC,IAAE,KAAK,EAAE;AAAE,QAAI,IAAE,EAAE,aAAa,GAAE,GAAE,KAAK,QAAO,KAAK,OAAO;AAAE,WAAO,KAAK,UAAU,EAAE,MAAK,KAAK,KAAK,GAAE,EAAE,aAAa,GAAE,GAAE,CAAC,GAAE;AAAA,EAAI,GAAE,WAAU,SAASA,IAAE,GAAE;AAAC,aAAQ,GAAE,IAAE,GAAE,IAAEA,GAAE,QAAO,IAAE,GAAE,KAAG,EAAE,KAAE,IAAEA,GAAE,CAAC,GAAE,MAAIA,GAAE,IAAE,CAAC,IAAE,EAAE,CAAC,GAAEA,GAAE,IAAE,CAAC,IAAE,EAAE,IAAE,CAAC,GAAEA,GAAE,IAAE,CAAC,IAAE,EAAE,IAAE,CAAC;AAAA,EAAE,EAAC,GAAE,OAAO,aAAW;AAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAK5nD,EAAE,aAAW,EAAE,QAAM,EAAE,QAAM,EAAE,OAAO,OAAO,EAAC,YAAW,SAAS,GAAE,GAAE;AAAC,OAAK,WAAS,GAAE,EAAE,WAAW,MAAK,CAAC;AAAC,GAAE,YAAW,SAAS,GAAE;AAAC,SAAO,KAAK,WAAS,GAAE,KAAK,OAAO;AAAC,GAAE,WAAU,SAAS,GAAE;AAAC,SAAO,KAAK,SAAS,KAAK,CAAC,GAAE,KAAK,OAAO;AAAC,GAAE,YAAW,SAAS,GAAE;AAAC,SAAO,EAAE,WAAW,MAAK,CAAC,GAAE,KAAK,SAAO,KAAK,eAAe,GAAE,KAAK,OAAO;AAAC,GAAE,QAAO,WAAU;AAAC,SAAM,CAAC,KAAK,SAAO,KAAK,UAAQ,KAAK,KAAK,eAAa,KAAK,SAAO,EAAE,KAAK,iBAAiB,KAAK,SAAQ,IAAI,IAAG;AAAI,GAAE,OAAM,SAAS,GAAE;AAAC,OAAK,OAAK,GAAE,KAAK,WAAS,KAAK,YAAY,GAAE,EAAE,OAAO,YAAY,YAAY,KAAK,OAAO,GAAE,EAAE,GAAG,WAAU,KAAK,QAAO,IAAI,GAAE,EAAE,QAAQ,iBAAe,EAAE,QAAQ,SAAO,EAAE,GAAG,YAAW,KAAK,cAAa,IAAI,GAAE,KAAK,OAAO;AAAC,GAAE,UAAS,SAAS,GAAE;AAAC,IAAE,SAAS,EAAE,YAAY,YAAY,KAAK,OAAO,GAAE,EAAE,IAAI,WAAU,KAAK,QAAO,IAAI,GAAE,EAAE,QAAQ,iBAAe,EAAE,IAAI,YAAW,KAAK,cAAa,IAAI;AAAC,GAAE,OAAM,SAAS,GAAE;AAAC,SAAO,EAAE,SAAS,IAAI,GAAE;AAAI,GAAE,aAAY,WAAU;AAAC,MAAI,IAAE,KAAK,UAAQ,EAAE,QAAQ,OAAO,UAAS,qCAAqC,GAAE,IAAE,EAAE,QAAQ,SAAS,CAAC,mBAAkB,yBAAwB,mBAAmB,CAAC;AAAE,IAAE,MAAM,CAAC,IAAE;AAAU,MAAI,IAAE,KAAK,KAAK,QAAQ;AAAE,IAAE,QAAM,EAAE,GAAE,EAAE,SAAO,EAAE;AAAE,MAAI,IAAE,KAAK,KAAK,QAAQ,iBAAe,EAAE,QAAQ;AAAM,IAAE,QAAQ,SAAS,GAAE,mBAAiB,IAAE,aAAW,OAAO,GAAE,KAAK,QAAM,WAAW,CAAC,GAAE,KAAK,eAAe;AAAC,GAAE,gBAAe,WAAU;AAAC,OAAK,MAAM,OAAO,KAAK,QAAQ,UAAQ,KAAK,MAAM,eAAc,KAAK,QAAQ,IAAI,GAAE,KAAK,QAAQ,YAAU,KAAK,MAAM,SAAS,KAAK,QAAQ,QAAQ,GAAE,KAAK,QAAQ,OAAK,KAAK,MAAM,IAAI,KAAK,QAAQ,GAAG;AAAC,GAAE,QAAO,WAAU;AAAC,MAAI,IAAE,KAAK,KAAK,2BAA2B,CAAC,GAAE,CAAC,CAAC;AAAE,IAAE,QAAQ,YAAY,KAAK,SAAQ,CAAC;AAAE,MAAI,IAAE,KAAK,KAAK,QAAQ;AAAE,OAAK,MAAM,WAAS,EAAE,MAAI,KAAK,QAAQ,QAAM,KAAK,MAAM,SAAO,EAAE,IAAG,KAAK,MAAM,YAAU,EAAE,MAAI,KAAK,QAAQ,SAAO,KAAK,MAAM,UAAQ,EAAE,IAAG,KAAK,QAAQ;AAAC,GAAE,SAAQ,WAAU;AAAC,MAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,CAAC,GAAE,IAAE,KAAK,MAAM,IAAG,IAAE,KAAK,KAAK,QAAQ,GAAE,IAAE,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,GAAE,CAAC,CAAC,CAAC,GAAE,EAAE,IAAI,CAAC,GAAE,CAAC,CAAC,CAAC,GAAE,IAAE,WAAS,KAAK,QAAQ,MAAI,IAAE,KAAK,QAAQ,KAAI,IAAE,WAAS,KAAK,QAAQ,UAAQ,KAAK,KAAK,WAAW,IAAE,KAAK,QAAQ,SAAQ,IAAE,IAAE,KAAK,IAAI,GAAE,KAAK,IAAI,GAAE,KAAK,IAAI,IAAE,KAAK,KAAK,QAAQ,GAAE,EAAE,CAAC,CAAC,GAAE,IAAE,IAAE,GAAE,IAAE,CAAC,GAAE,IAAE,KAAK,KAAK,eAAe,GAAE,IAAE,EAAE,IAAE,GAAE,IAAE,EAAE,IAAE;AAAE,OAAI,IAAE,GAAE,IAAE,KAAK,SAAS,QAAO,IAAE,GAAE,IAAI,KAAG,IAAE,KAAK,KAAK,uBAAuB,KAAK,SAAS,CAAC,CAAC,GAAE,EAAE,SAAS,CAAC,GAAE;AAAC,QAAE,KAAK,OAAO,EAAE,IAAE,KAAG,CAAC,IAAE,GAAE,IAAE,KAAK,OAAO,EAAE,IAAE,KAAG,CAAC,IAAE;AAAE,QAAI,IAAE,WAAS,KAAK,SAAS,CAAC,EAAE,MAAI,KAAK,SAAS,CAAC,EAAE,MAAI,WAAS,KAAK,SAAS,CAAC,EAAE,CAAC,IAAE,CAAC,KAAK,SAAS,CAAC,EAAE,CAAC,IAAE;AAAE,QAAE,IAAE,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC,KAAG,CAAC,GAAE,IAAE,EAAE,CAAC,EAAE,CAAC,GAAE,KAAG,EAAE,CAAC,KAAG,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,IAAE,MAAI,EAAE,CAAC,IAAE,IAAG,EAAE,CAAC,KAAG,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,IAAE,MAAI,EAAE,CAAC,IAAE,IAAG,EAAE,CAAC,KAAG,KAAG,EAAE,CAAC,EAAE,CAAC,IAAE,CAAC,EAAE,GAAE,EAAE,GAAE,CAAC;AAAA,EAAC;AAAC,OAAI,IAAE,GAAE,IAAE,EAAE,QAAO,IAAE,GAAE,IAAI,KAAG,EAAE,CAAC,EAAE,MAAI,IAAE,GAAE,IAAE,EAAE,CAAC,EAAE,QAAO,IAAE,GAAE,IAAI,KAAE,EAAE,CAAC,EAAE,CAAC,GAAE,KAAG,EAAE,KAAK,CAAC,KAAK,MAAM,EAAE,CAAC,CAAC,GAAE,KAAK,MAAM,EAAE,CAAC,CAAC,GAAE,KAAK,IAAI,EAAE,CAAC,GAAE,CAAC,CAAC,CAAC;AAAE,OAAK,MAAM,KAAK,CAAC,EAAE,KAAK,KAAK,QAAQ,UAAU,GAAE,KAAK,SAAO;AAAI,GAAE,cAAa,SAAS,GAAE;AAAC,MAAI,IAAE,KAAK,KAAK,aAAa,EAAE,IAAI,GAAE,IAAE,KAAK,KAAK,iBAAiB,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC,EAAE,SAAS,KAAK,KAAK,eAAe,CAAC;AAAE,IAAE,QAAQ,eAAa,EAAE,QAAQ,aAAa,KAAK,SAAQ,GAAE,CAAC,IAAE,KAAK,QAAQ,MAAM,EAAE,QAAQ,SAAS,IAAE,EAAE,QAAQ,mBAAmB,CAAC,IAAE,YAAU,IAAE;AAAG,EAAC,CAAC,GAAE,EAAE,YAAU,SAAS,GAAE,GAAE;AAAC,SAAO,IAAI,EAAE,UAAU,GAAE,CAAC;AAAC;", "names": ["t"]}