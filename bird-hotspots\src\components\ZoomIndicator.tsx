import React from 'react';
import { useAppStore } from '../store/useAppStore';
import { 
  calculateMapScale, 
  getDetailLevel, 
  getDisplayThreshold,
  ZOOM_CONFIG 
} from '../utils/gridUtils';

interface ZoomIndicatorProps {
  className?: string;
}

const ZoomIndicator: React.FC<ZoomIndicatorProps> = ({ className = '' }) => {
  const { mapZoom, mapCenter } = useAppStore();
  
  const detailLevel = getDetailLevel(mapZoom);
  const displayMode = getDisplayThreshold(mapZoom, mapCenter[0]);
  const mapScale = calculateMapScale(mapZoom, mapCenter[0]);
  
  // 获取显示模式的中文描述
  const getDisplayModeText = (level: string, mode: string) => {
    if (mode === 'grid') {
      return '网格聚合模式';
    }
    
    switch (level) {
      case 'ultra':
        return '超高精度模式';
      case 'high':
        return '高精度模式';
      case 'points':
        return '坐标点模式';
      default:
        return '网格聚合模式';
    }
  };
  
  // 获取显示模式的颜色
  const getModeColor = (level: string, mode: string) => {
    if (mode === 'grid') {
      return 'bg-blue-100 text-blue-800 border-blue-200';
    }
    
    switch (level) {
      case 'ultra':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'high':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'points':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };
  
  // 获取阈值信息
  const getThresholdInfo = () => {
    const thresholds = [
      { level: ZOOM_CONFIG.POINTS_THRESHOLD, name: '坐标点显示' },
      { level: ZOOM_CONFIG.HIGH_DETAIL_THRESHOLD, name: '高精度模式' },
      { level: ZOOM_CONFIG.ULTRA_DETAIL_THRESHOLD, name: '超高精度模式' }
    ];
    
    return thresholds.map(threshold => ({
      ...threshold,
      active: mapZoom >= threshold.level,
      distance: Math.abs(mapZoom - threshold.level)
    }));
  };
  
  const modeText = getDisplayModeText(detailLevel, displayMode);
  const modeColor = getModeColor(detailLevel, displayMode);
  const thresholdInfo = getThresholdInfo();
  
  return (
    <div className={`bg-white rounded-lg shadow-md border p-3 ${className}`}>
      <div className="space-y-2">
        {/* 当前缩放级别 */}
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-600">缩放级别:</span>
          <span className="font-mono text-sm font-bold text-gray-800">
            {mapZoom.toFixed(1)}
          </span>
        </div>
        
        {/* 地图比例尺 */}
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-600">比例尺:</span>
          <span className="font-mono text-xs text-gray-800">
            1:{mapScale.toLocaleString()}
          </span>
        </div>
        
        {/* 显示模式 */}
        <div className="pt-2 border-t">
          <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${modeColor}`}>
            <div className="w-2 h-2 rounded-full bg-current mr-1.5 opacity-60"></div>
            {modeText}
          </div>
        </div>
        
        {/* 阈值指示器 */}
        <div className="pt-2 border-t">
          <div className="text-xs text-gray-500 mb-1">缩放阈值:</div>
          <div className="space-y-1">
            {thresholdInfo.map((threshold, index) => (
              <div key={index} className="flex items-center justify-between text-xs">
                <span className={`${threshold.active ? 'text-green-600 font-medium' : 'text-gray-400'}`}>
                  {threshold.name}
                </span>
                <div className="flex items-center">
                  <span className="font-mono text-xs mr-1">
                    {threshold.level}
                  </span>
                  <div className={`w-2 h-2 rounded-full ${
                    threshold.active ? 'bg-green-500' : 'bg-gray-300'
                  }`}></div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* 性能信息 */}
        <div className="pt-2 border-t">
          <div className="text-xs text-gray-500 mb-1">性能限制:</div>
          <div className="text-xs text-gray-600">
            {displayMode === 'points' ? (
              <>
                最大点数: {detailLevel === 'ultra' 
                  ? ZOOM_CONFIG.MAX_POINTS_ULTRA_DETAIL.toLocaleString()
                  : detailLevel === 'high' 
                    ? ZOOM_CONFIG.MAX_POINTS_HIGH_DETAIL.toLocaleString()
                    : ZOOM_CONFIG.MAX_POINTS_DISPLAY.toLocaleString()
                }
              </>
            ) : (
              '网格聚合显示'
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ZoomIndicator;
