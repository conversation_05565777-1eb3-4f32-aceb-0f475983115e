import { useState } from 'react';
import { Settings, Info, Eye, EyeOff } from 'lucide-react';
import L from 'leaflet';
import { useAppStore } from '../store/useAppStore';

interface MapControlsProps {
  map: L.Map | null;
}

const MapControls = ({ map }: MapControlsProps) => {
  const [isOpen, setIsOpen] = useState(true);
  const {
    selectedMapType,
    setSelectedMapType,
    species,
    toggleSpeciesVisibility
  } = useAppStore();

  const mapTypes = [
    { id: 'terrain', name: '地形图', url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png' },
    { id: 'streets', name: '街道图', url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png' },
    { id: 'satellite', name: '卫星图', url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}' },
    { id: 'hybrid', name: '混合图', url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}' },
  ];

  // 注意：颜色范围现在由 ColorLegend 组件显示，这里保留用于其他用途
  const speciesRanges = [
    { range: '600+', color: 'bg-red-700', count: '600+' },
    { range: '500-600', color: 'bg-red-600', count: '500 - 600' },
    { range: '400-500', color: 'bg-red-500', count: '400 - 500' },
    { range: '300-400', color: 'bg-orange-500', count: '300 - 400' },
    { range: '250-300', color: 'bg-orange-400', count: '250 - 300' },
    { range: '200-250', color: 'bg-yellow-500', count: '200 - 250' },
    { range: '150-200', color: 'bg-yellow-400', count: '150 - 200' },
    { range: '100-150', color: 'bg-yellow-300', count: '100 - 150' },
    { range: '50-100', color: 'bg-green-300', count: '50 - 100' },
    { range: '15-50', color: 'bg-green-200', count: '15 - 50' },
    { range: '0-15', color: 'bg-green-100', count: '0 - 15' },
  ];

  const handleMapTypeChange = (mapType: typeof mapTypes[0]) => {
    if (!map) return;

    setSelectedMapType(mapType.id);

    // Remove existing tile layers
    map.eachLayer((layer) => {
      if (layer instanceof L.TileLayer) {
        map.removeLayer(layer);
      }
    });

    // Add new tile layer
    L.tileLayer(mapType.url, {
      attribution: mapType.id === 'satellite' || mapType.id === 'hybrid'
        ? '© Esri'
        : '© OpenStreetMap contributors',
      maxZoom: 18,
    }).addTo(map);
  };

  return (
    <div className="absolute top-4 right-4 z-[1000]">
      <div className="map-controls">
        {/* Toggle Button */}
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center justify-center w-8 h-8 bg-white rounded-md shadow-md hover:bg-gray-50 mb-2"
        >
          <Settings className="h-4 w-4 text-gray-600" />
        </button>

        {isOpen && (
          <div className="space-y-4">
            {/* Map Type Controls */}
            <div>
              <h6 className="text-sm font-semibold text-gray-900 mb-2">地图类型</h6>
              <div className="space-y-1">
                {mapTypes.map((mapType) => (
                  <label key={mapType.id} className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      name="mapType"
                      value={mapType.id}
                      checked={selectedMapType === mapType.id}
                      onChange={() => handleMapTypeChange(mapType)}
                      className="mr-2 text-blue-600"
                    />
                    <span className="text-sm text-gray-700">{mapType.name}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Species Controls */}
            <div>
              <div className="flex items-center mb-2">
                <h5 className="text-sm font-semibold text-gray-900">物种图层</h5>
              </div>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {species.map((speciesItem) => (
                  <div key={speciesItem.id} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div
                        className="w-3 h-3 rounded-full mr-2 border border-gray-300"
                        style={{ backgroundColor: speciesItem.color }}
                      ></div>
                      <span className="text-xs text-gray-700">{speciesItem.name}</span>
                    </div>
                    <button
                      onClick={() => toggleSpeciesVisibility(speciesItem.id)}
                      className="p-1 hover:bg-gray-100 rounded"
                    >
                      {speciesItem.isVisible ? (
                        <Eye className="h-3 w-3 text-gray-600" />
                      ) : (
                        <EyeOff className="h-3 w-3 text-gray-400" />
                      )}
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* Species Legend */}
            <div>
              <div className="flex items-center mb-2">
                <h5 className="text-sm font-semibold text-gray-900">观察到的鸟种</h5>
                <button className="ml-2">
                  <Info className="h-3 w-3 text-gray-500" />
                </button>
              </div>
              <div className="space-y-1">
                {speciesRanges.map((item) => (
                  <div key={item.range} className="flex items-center">
                    <div className={`w-4 h-3 ${item.color} mr-2 border border-gray-300`}></div>
                    <span className="text-xs text-gray-700">{item.count}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MapControls;
