import React from 'react';
import { Palette } from 'lucide-react';
import { COLOR_RANGES } from '../utils/colorUtils';
import type { ColorRange } from '../utils/colorUtils';

interface ColorLegendProps {
  colorRanges?: ColorRange[];
  title?: string;
  className?: string;
}

const ColorLegend: React.FC<ColorLegendProps> = ({
  colorRanges = COLOR_RANGES,
  title = "观察数量图例",
  className = ""
}) => {
  return (
    <div className={`bg-white/95 backdrop-blur-sm rounded-xl shadow-large border border-white/20 p-4 ${className}`}>
      <div className="flex items-center mb-3">
        <div className="flex items-center justify-center w-6 h-6 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg mr-2">
          <Palette className="h-3 w-3 text-white" />
        </div>
        <h3 className="text-sm font-bold text-gray-900">{title}</h3>
      </div>
      
      <div className="space-y-2">
        {colorRanges.map((range: ColorRange, index: number) => (
          <div key={index} className="flex items-center justify-between text-xs">
            <div className="flex items-center">
              <div
                className="w-4 h-4 rounded-sm border border-white/80 shadow-sm mr-2"
                style={{ backgroundColor: range.color }}
              />
              <span className="text-gray-700 font-medium">{range.label}</span>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-3 pt-2 border-t border-gray-200/50">
        <p className="text-xs text-gray-600">
          方块大小和颜色深度反映观察数量
        </p>
      </div>
    </div>
  );
};

export default ColorLegend;
