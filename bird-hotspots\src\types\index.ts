export interface Species {
  id: string;
  name: string;
  scientificName: string;
  color: string;
  description: string;
  isVisible: boolean;
}

export interface ObservationPoint {
  lat: number;
  lng: number;
  intensity: number; // 0-1
  count: number;
  speciesId: string;
}

export interface HotspotData {
  id: string;
  name: string;
  lat: number;
  lng: number;
  speciesCount: number;
  observations: ObservationPoint[];
}

export interface MapType {
  id: string;
  name: string;
  url: string;
  attribution: string;
}

export interface SpeciesRange {
  range: string;
  color: string;
  count: string;
  min: number;
  max: number;
}

/**
 * 网格单元接口定义
 */
export interface GridCell {
  gridLat: number;
  gridLng: number;
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  totalCount: number;
  observationCount: number;
  averageIntensity: number;
  speciesIds: string[];
  observations: ObservationPoint[];
}

/**
 * 网格配置接口
 */
export interface GridConfig {
  size: number;
  minZoom: number;
  maxZoom: number;
}

/**
 * 地图边界接口
 */
export interface MapBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

/**
 * 热力图显示模式
 */
export type HeatmapDisplayMode = 'grid' | 'points';

/**
 * 网格层配置选项
 */
export interface GridLayerOptions {
  maxCells?: number;
  enableClustering?: boolean;
  showBorders?: boolean;
  borderColor?: string;
  borderWeight?: number;
}
