/**
 * 测试多级缩放显示逻辑
 */

import {
  calculateMapScale,
  getDisplayThreshold,
  getDetailLevel,
  isHighDetailMode,
  isUltraDetailMode,
  filterObservationsByViewport,
  ZOOM_CONFIG
} from '../utils/gridUtils';

import {
  getEnhancedColorMapping,
  getColorByDensity,
  getSquareSize,
  interpolateColor
} from '../utils/colorUtils';

// 模拟观察点数据
const mockObservations = [
  { lat: 39.9042, lng: 116.4074, count: 50, intensity: 0.7, speciesId: 'species1' },
  { lat: 39.9050, lng: 116.4080, count: 120, intensity: 0.8, speciesId: 'species1' },
  { lat: 39.9030, lng: 116.4060, count: 25, intensity: 0.5, speciesId: 'species2' },
  { lat: 39.9060, lng: 116.4090, count: 200, intensity: 0.9, speciesId: 'species2' },
];

const mockMapBounds = {
  north: 39.91,
  south: 39.89,
  east: 116.42,
  west: 116.39
};

describe('缩放级别配置测试', () => {
  test('地图比例尺计算', () => {
    // 测试不同缩放级别的比例尺计算
    const scale15 = calculateMapScale(15, 39.9042);
    const scale18 = calculateMapScale(18, 39.9042);
    const scale20 = calculateMapScale(20, 39.9042);
    
    console.log('缩放级别 15 的比例尺:', scale15);
    console.log('缩放级别 18 的比例尺:', scale18);
    console.log('缩放级别 20 的比例尺:', scale20);
    
    // 验证比例尺随缩放级别递减
    expect(scale15).toBeGreaterThan(scale18);
    expect(scale18).toBeGreaterThan(scale20);
    
    // 验证缩放级别15的比例尺约为1:10000左右
    expect(scale15).toBeLessThan(50000);
    expect(scale15).toBeGreaterThan(5000);
  });

  test('显示模式阈值判断', () => {
    // 测试不同缩放级别的显示模式
    expect(getDisplayThreshold(10)).toBe('grid');
    expect(getDisplayThreshold(15)).toBe('points');
    expect(getDisplayThreshold(18)).toBe('points');
    
    // 测试带纬度的判断
    expect(getDisplayThreshold(14, 39.9042)).toBe('grid');
    expect(getDisplayThreshold(15, 39.9042)).toBe('points');
  });

  test('详细程度级别判断', () => {
    expect(getDetailLevel(10)).toBe('grid');
    expect(getDetailLevel(15)).toBe('points');
    expect(getDetailLevel(18)).toBe('high');
    expect(getDetailLevel(20)).toBe('ultra');
    
    expect(isHighDetailMode(17)).toBe(false);
    expect(isHighDetailMode(18)).toBe(true);
    expect(isUltraDetailMode(19)).toBe(false);
    expect(isUltraDetailMode(20)).toBe(true);
  });
});

describe('视窗过滤功能测试', () => {
  test('观察点视窗过滤', () => {
    const result = filterObservationsByViewport(
      mockObservations,
      mockMapBounds,
      [39.9042, 116.4074],
      2
    );
    
    console.log('视窗内观察点:', result.inViewport.length);
    console.log('附近观察点:', result.nearViewport.length);
    
    // 验证所有视窗内的点都在边界内
    result.inViewport.forEach(obs => {
      expect(obs.lat).toBeGreaterThanOrEqual(mockMapBounds.south);
      expect(obs.lat).toBeLessThanOrEqual(mockMapBounds.north);
      expect(obs.lng).toBeGreaterThanOrEqual(mockMapBounds.west);
      expect(obs.lng).toBeLessThanOrEqual(mockMapBounds.east);
    });
  });
});

describe('颜色映射功能测试', () => {
  test('增强颜色映射', () => {
    const mapping1 = getEnhancedColorMapping(50, 0.7);
    const mapping2 = getEnhancedColorMapping(200, 0.9);
    
    console.log('低数量颜色映射:', mapping1);
    console.log('高数量颜色映射:', mapping2);
    
    // 验证颜色映射包含必要属性
    expect(mapping1).toHaveProperty('color');
    expect(mapping1).toHaveProperty('opacity');
    expect(mapping1).toHaveProperty('borderColor');
    
    // 验证高数量的透明度更高
    expect(mapping2.opacity).toBeGreaterThanOrEqual(mapping1.opacity);
  });

  test('密度颜色映射', () => {
    const color1 = getColorByDensity(50, 1000, 'green');
    const color2 = getColorByDensity(500, 1000, 'green');
    const color3 = getColorByDensity(50, 1000, 'blue');
    
    console.log('低密度绿色:', color1);
    console.log('高密度绿色:', color2);
    console.log('低密度蓝色:', color3);
    
    // 验证返回的是有效的十六进制颜色
    expect(color1).toMatch(/^#[0-9a-f]{6}$/i);
    expect(color2).toMatch(/^#[0-9a-f]{6}$/i);
    expect(color3).toMatch(/^#[0-9a-f]{6}$/i);
  });

  test('方块大小计算', () => {
    const size1 = getSquareSize(10, 15);
    const size2 = getSquareSize(100, 15);
    const size3 = getSquareSize(100, 20);
    
    console.log('小数量方块大小:', size1);
    console.log('大数量方块大小:', size2);
    console.log('大数量高缩放方块大小:', size3);
    
    // 验证大数量的方块更大
    expect(size2).toBeGreaterThan(size1);
    // 验证高缩放级别的方块更大
    expect(size3).toBeGreaterThan(size2);
  });

  test('颜色插值', () => {
    const color1 = interpolateColor('#ffffff', '#000000', 0);
    const color2 = interpolateColor('#ffffff', '#000000', 0.5);
    const color3 = interpolateColor('#ffffff', '#000000', 1);
    
    console.log('插值 0:', color1);
    console.log('插值 0.5:', color2);
    console.log('插值 1:', color3);
    
    expect(color1).toBe('#ffffff');
    expect(color3).toBe('#000000');
    expect(color2).toMatch(/^#[0-9a-f]{6}$/i);
  });
});

describe('配置常量测试', () => {
  test('缩放配置常量', () => {
    console.log('缩放配置:', ZOOM_CONFIG);
    
    // 验证阈值的逻辑顺序
    expect(ZOOM_CONFIG.POINTS_THRESHOLD).toBeLessThan(ZOOM_CONFIG.HIGH_DETAIL_THRESHOLD);
    expect(ZOOM_CONFIG.HIGH_DETAIL_THRESHOLD).toBeLessThan(ZOOM_CONFIG.ULTRA_DETAIL_THRESHOLD);
    
    // 验证点数量限制的逻辑顺序
    expect(ZOOM_CONFIG.MAX_POINTS_DISPLAY).toBeLessThan(ZOOM_CONFIG.MAX_POINTS_HIGH_DETAIL);
    expect(ZOOM_CONFIG.MAX_POINTS_HIGH_DETAIL).toBeLessThan(ZOOM_CONFIG.MAX_POINTS_ULTRA_DETAIL);
  });
});

// 性能测试
describe('性能测试', () => {
  test('大量数据点的视窗过滤性能', () => {
    // 生成大量模拟数据
    const largeDataset = Array.from({ length: 10000 }, (_, i) => ({
      lat: 39.9 + (Math.random() - 0.5) * 0.1,
      lng: 116.4 + (Math.random() - 0.5) * 0.1,
      count: Math.floor(Math.random() * 500) + 1,
      intensity: Math.random(),
      speciesId: `species${i % 10}`
    }));
    
    const startTime = performance.now();
    
    const result = filterObservationsByViewport(
      largeDataset,
      mockMapBounds,
      [39.9042, 116.4074],
      500
    );
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.log(`处理 ${largeDataset.length} 个数据点耗时: ${duration.toFixed(2)}ms`);
    console.log(`视窗内点数: ${result.inViewport.length}`);
    console.log(`附近点数: ${result.nearViewport.length}`);
    
    // 验证性能要求（应该在100ms内完成）
    expect(duration).toBeLessThan(100);
    
    // 验证结果合理性
    expect(result.inViewport.length + result.nearViewport.length).toBeGreaterThan(0);
    expect(result.nearViewport.length).toBeLessThanOrEqual(500);
  });
});
