import { Search } from 'lucide-react';
import { useAppStore } from '../store/useAppStore';

const SearchFilters = () => {
  const {
    searchQuery,
    setSearchQuery,
  } = useAppStore();

  return (
    <div className="bg-white/95 backdrop-blur-sm border-b border-gray-200/50 px-4 py-6 shadow-soft">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-center space-y-4 sm:space-y-0 sm:space-x-8">
          {/* Title */}
          <div className="flex items-center space-x-3">
            <div className="w-1 h-8 bg-gradient-to-b from-primary-500 to-primary-600 rounded-full"></div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              观鸟热点
            </h1>
          </div>

          {/* Search */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl blur opacity-20 group-hover:opacity-30 transition-opacity duration-200"></div>
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 group-hover:text-primary-500 transition-colors duration-200" />
              <input
                type="text"
                placeholder="搜索鸟类物种…"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 w-full sm:w-96 bg-white shadow-soft hover:shadow-medium transition-all duration-200 text-gray-900 placeholder-gray-500"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchFilters;
