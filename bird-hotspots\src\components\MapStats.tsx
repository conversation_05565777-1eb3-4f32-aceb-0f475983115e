import { BarChart3, Eye, MapPin } from 'lucide-react';
import { useAppStore } from '../store/useAppStore';

const MapStats = () => {
  const { species, observations, searchQuery } = useAppStore();

  // Filter species based on visibility and search query
  const visibleSpecies = species.filter(s => {
    if (!s.isVisible) return false;

    // If there's a search query, filter by species name or scientific name
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      return s.name.toLowerCase().includes(query) ||
             s.scientificName.toLowerCase().includes(query);
    }

    return true;
  });
  const visibleObservations = observations.filter(obs => 
    visibleSpecies.some(s => s.id === obs.speciesId)
  );

  const totalObservations = visibleObservations.length;
  const totalCount = visibleObservations.reduce((sum, obs) => sum + obs.count, 0);
  const avgCount = totalObservations > 0 ? Math.round(totalCount / totalObservations) : 0;

  return (
    <div className="absolute bottom-4 left-4 z-[1000] max-w-xs animate-slide-up">
      <div className="bg-white/95 backdrop-blur-sm rounded-xl shadow-large border border-white/20 p-4 sm:p-5 hover:shadow-xl transition-all duration-300">
        <div className="flex items-center mb-4">
          <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg mr-3">
            <BarChart3 className="h-4 w-4 text-white" />
          </div>
          <h3 className="text-base font-bold text-gray-900">观察统计</h3>
        </div>
        
        <div className="space-y-3 text-sm">
          <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-blue-100/50 rounded-lg border border-blue-200/50">
            <div className="flex items-center">
              <div className="flex items-center justify-center w-6 h-6 bg-blue-500 rounded-md mr-3">
                <Eye className="h-3 w-3 text-white" />
              </div>
              <span className="text-gray-700 font-medium">显示物种:</span>
            </div>
            <span className="font-bold text-blue-700 text-base">
              {visibleSpecies.length}/{species.length}
            </span>
          </div>

          <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-green-100/50 rounded-lg border border-green-200/50">
            <div className="flex items-center">
              <div className="flex items-center justify-center w-6 h-6 bg-green-500 rounded-md mr-3">
                <MapPin className="h-3 w-3 text-white" />
              </div>
              <span className="text-gray-700 font-medium">观察点:</span>
            </div>
            <span className="font-bold text-green-700 text-base">
              {totalObservations.toLocaleString()}
            </span>
          </div>

          <div className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-purple-100/50 rounded-lg border border-purple-200/50">
            <span className="text-gray-700 font-medium">总观察数:</span>
            <span className="font-bold text-purple-700 text-base">
              {totalCount.toLocaleString()}
            </span>
          </div>

          <div className="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-orange-100/50 rounded-lg border border-orange-200/50">
            <span className="text-gray-700 font-medium">平均数量:</span>
            <span className="font-bold text-orange-700 text-base">
              {avgCount}
            </span>
          </div>
        </div>

        {/* Species breakdown */}
        {visibleSpecies.length > 0 && (
          <div className="mt-5 pt-4 border-t border-gray-200/50">
            <div className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
              <div className="w-1 h-4 bg-gradient-to-b from-primary-500 to-primary-600 rounded-full mr-2"></div>
              物种分布
            </div>
            <div className="space-y-2">
              {visibleSpecies.map(speciesItem => {
                const speciesObs = observations.filter(obs => obs.speciesId === speciesItem.id);
                const speciesCount = speciesObs.reduce((sum, obs) => sum + obs.count, 0);
                return (
                  <div key={speciesItem.id} className="flex items-center justify-between p-2 bg-gray-50/80 rounded-lg hover:bg-gray-100/80 transition-colors duration-200">
                    <div className="flex items-center">
                      <div
                        className="w-3 h-3 rounded-full mr-3 shadow-sm border border-white"
                        style={{ backgroundColor: speciesItem.color }}
                      />
                      <span className="text-gray-700 font-medium text-sm truncate max-w-24">
                        {speciesItem.name}
                      </span>
                    </div>
                    <span className="text-gray-900 font-bold text-sm">
                      {speciesCount.toLocaleString()}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MapStats;
