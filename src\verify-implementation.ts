/**
 * 验证多级缩放显示逻辑实现
 */

import {
  calculateMapScale,
  getDisplayThreshold,
  getDetailLevel,
  isHighDetailMode,
  isUltraDetailMode,
  filterObservationsByViewport,
  ZOOM_CONFIG
} from './utils/gridUtils';

import {
  getEnhancedColorMapping,
  getColorByDensity,
  getSquareSize,
  interpolateColor
} from './utils/colorUtils';

// 验证函数
function verifyImplementation() {
  console.log('🔍 开始验证多级缩放显示逻辑实现...\n');

  // 1. 验证缩放级别配置
  console.log('1. 缩放级别配置验证:');
  console.log('   POINTS_THRESHOLD:', ZOOM_CONFIG.POINTS_THRESHOLD);
  console.log('   HIGH_DETAIL_THRESHOLD:', ZOOM_CONFIG.HIGH_DETAIL_THRESHOLD);
  console.log('   ULTRA_DETAIL_THRESHOLD:', ZOOM_CONFIG.ULTRA_DETAIL_THRESHOLD);
  console.log('   ✅ 配置加载成功\n');

  // 2. 验证地图比例尺计算
  console.log('2. 地图比例尺计算验证:');
  const scale15 = calculateMapScale(15, 39.9042);
  const scale18 = calculateMapScale(18, 39.9042);
  const scale20 = calculateMapScale(20, 39.9042);
  
  console.log(`   缩放级别 15: 1:${scale15.toLocaleString()}`);
  console.log(`   缩放级别 18: 1:${scale18.toLocaleString()}`);
  console.log(`   缩放级别 20: 1:${scale20.toLocaleString()}`);
  
  const scaleValid = scale15 > scale18 && scale18 > scale20 && scale15 < 50000;
  console.log(`   ✅ 比例尺计算${scaleValid ? '正确' : '错误'}\n`);

  // 3. 验证显示模式判断
  console.log('3. 显示模式判断验证:');
  const mode10 = getDisplayThreshold(10);
  const mode15 = getDisplayThreshold(15);
  const mode18 = getDisplayThreshold(18);
  
  console.log(`   缩放级别 10: ${mode10}`);
  console.log(`   缩放级别 15: ${mode15}`);
  console.log(`   缩放级别 18: ${mode18}`);
  
  const modeValid = mode10 === 'grid' && mode15 === 'points' && mode18 === 'points';
  console.log(`   ✅ 显示模式判断${modeValid ? '正确' : '错误'}\n`);

  // 4. 验证详细程度级别
  console.log('4. 详细程度级别验证:');
  const detail10 = getDetailLevel(10);
  const detail15 = getDetailLevel(15);
  const detail18 = getDetailLevel(18);
  const detail20 = getDetailLevel(20);
  
  console.log(`   缩放级别 10: ${detail10}`);
  console.log(`   缩放级别 15: ${detail15}`);
  console.log(`   缩放级别 18: ${detail18}`);
  console.log(`   缩放级别 20: ${detail20}`);
  
  const detailValid = detail10 === 'grid' && detail15 === 'points' && 
                     detail18 === 'high' && detail20 === 'ultra';
  console.log(`   ✅ 详细程度判断${detailValid ? '正确' : '错误'}\n`);

  // 5. 验证颜色映射功能
  console.log('5. 颜色映射功能验证:');
  const colorMapping1 = getEnhancedColorMapping(50, 0.7);
  const colorMapping2 = getEnhancedColorMapping(200, 0.9);
  
  console.log('   低数量映射:', colorMapping1);
  console.log('   高数量映射:', colorMapping2);
  
  const colorValid = colorMapping1.color && colorMapping1.opacity && colorMapping1.borderColor &&
                    colorMapping2.opacity >= colorMapping1.opacity;
  console.log(`   ✅ 颜色映射${colorValid ? '正确' : '错误'}\n`);

  // 6. 验证方块大小计算
  console.log('6. 方块大小计算验证:');
  const size1 = getSquareSize(10, 15);
  const size2 = getSquareSize(100, 15);
  const size3 = getSquareSize(100, 20);
  
  console.log(`   小数量(10, zoom15): ${size1}px`);
  console.log(`   大数量(100, zoom15): ${size2}px`);
  console.log(`   大数量(100, zoom20): ${size3}px`);
  
  const sizeValid = size2 > size1 && size3 > size2;
  console.log(`   ✅ 方块大小计算${sizeValid ? '正确' : '错误'}\n`);

  // 7. 验证颜色插值
  console.log('7. 颜色插值验证:');
  const color1 = interpolateColor('#ffffff', '#000000', 0);
  const color2 = interpolateColor('#ffffff', '#000000', 0.5);
  const color3 = interpolateColor('#ffffff', '#000000', 1);
  
  console.log(`   插值 0: ${color1}`);
  console.log(`   插值 0.5: ${color2}`);
  console.log(`   插值 1: ${color3}`);
  
  const interpolateValid = color1 === '#ffffff' && color3 === '#000000' && 
                          color2.match(/^#[0-9a-f]{6}$/i);
  console.log(`   ✅ 颜色插值${interpolateValid ? '正确' : '错误'}\n`);

  // 8. 验证视窗过滤功能
  console.log('8. 视窗过滤功能验证:');
  const mockObservations = [
    { lat: 39.9042, lng: 116.4074, count: 50, intensity: 0.7, speciesId: 'species1' },
    { lat: 39.9050, lng: 116.4080, count: 120, intensity: 0.8, speciesId: 'species1' },
    { lat: 39.9030, lng: 116.4060, count: 25, intensity: 0.5, speciesId: 'species2' },
    { lat: 40.0000, lng: 117.0000, count: 200, intensity: 0.9, speciesId: 'species2' }, // 视窗外
  ];

  const mockMapBounds = {
    north: 39.91,
    south: 39.89,
    east: 116.42,
    west: 116.39
  };

  const result = filterObservationsByViewport(
    mockObservations,
    mockMapBounds,
    [39.9042, 116.4074],
    2
  );
  
  console.log(`   视窗内观察点: ${result.inViewport.length}`);
  console.log(`   附近观察点: ${result.nearViewport.length}`);
  
  const viewportValid = result.inViewport.length === 3 && result.nearViewport.length <= 2;
  console.log(`   ✅ 视窗过滤${viewportValid ? '正确' : '错误'}\n`);

  // 总结
  console.log('🎉 验证完成！');
  const allValid = scaleValid && modeValid && detailValid && colorValid && 
                  sizeValid && interpolateValid && viewportValid;
  
  if (allValid) {
    console.log('✅ 所有功能验证通过，多级缩放显示逻辑实现正确！');
  } else {
    console.log('❌ 部分功能验证失败，请检查实现。');
  }

  return allValid;
}

// 性能测试
function performanceTest() {
  console.log('\n🚀 开始性能测试...\n');

  // 生成大量测试数据
  const largeDataset = Array.from({ length: 10000 }, (_, i) => ({
    lat: 39.9 + (Math.random() - 0.5) * 0.1,
    lng: 116.4 + (Math.random() - 0.5) * 0.1,
    count: Math.floor(Math.random() * 500) + 1,
    intensity: Math.random(),
    speciesId: `species${i % 10}`
  }));

  const mockMapBounds = {
    north: 39.95,
    south: 39.85,
    east: 116.45,
    west: 116.35
  };

  // 测试视窗过滤性能
  const startTime = performance.now();
  
  const result = filterObservationsByViewport(
    largeDataset,
    mockMapBounds,
    [39.9042, 116.4074],
    500
  );
  
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  console.log(`处理 ${largeDataset.length} 个数据点:`);
  console.log(`   耗时: ${duration.toFixed(2)}ms`);
  console.log(`   视窗内点数: ${result.inViewport.length}`);
  console.log(`   附近点数: ${result.nearViewport.length}`);
  
  const performanceGood = duration < 100;
  console.log(`   ✅ 性能测试${performanceGood ? '通过' : '未通过'} (要求 < 100ms)\n`);

  return performanceGood;
}

// 运行验证
if (typeof window === 'undefined') {
  // Node.js 环境
  const implementationValid = verifyImplementation();
  const performanceGood = performanceTest();
  
  if (implementationValid && performanceGood) {
    console.log('🎊 所有测试通过！多级缩放显示逻辑已成功实现。');
    process.exit(0);
  } else {
    console.log('💥 测试失败，请检查实现。');
    process.exit(1);
  }
} else {
  // 浏览器环境
  console.log('在浏览器环境中运行验证...');
  verifyImplementation();
  performanceTest();
}

export { verifyImplementation, performanceTest };
