import { useEffect, useRef, useState } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import MapControls from './MapControls';
import GridHeatmapLayer from './GridHeatmapLayer';
import SpeciesFilter from './SpeciesFilter';
import MapStats from './MapStats';
import LoadingSpinner from './LoadingSpinner';
import HelpPanel from './HelpPanel';
import ColorLegend from './ColorLegend';
import ZoomIndicator from './ZoomIndicator';
import { useAppStore } from '../store/useAppStore';

// Fix for default markers
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

const MapContainer = () => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { mapCenter, mapZoom, setMapCenter, setMapZoom } = useAppStore();

  useEffect(() => {
    if (!mapRef.current || mapInstanceRef.current) return;

    // Initialize map
    const map = L.map(mapRef.current, {
      center: mapCenter,
      zoom: mapZoom,
      zoomControl: false, // We'll add custom controls
    });

    // Add tile layer
    const tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors',
      maxZoom: 18,
    });

    tileLayer.on('load', () => {
      setIsLoading(false);
    });

    tileLayer.addTo(map);

    // Add zoom control to bottom right
    L.control.zoom({
      position: 'bottomright'
    }).addTo(map);

    // Add event listeners to sync state
    map.on('moveend', () => {
      const center = map.getCenter();
      setMapCenter([center.lat, center.lng]);
    });

    map.on('zoomend', () => {
      setMapZoom(map.getZoom());
    });

    mapInstanceRef.current = map;

    // Cleanup function
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [setMapCenter, setMapZoom]);

  return (
    <div className="relative w-full h-full">
      <div ref={mapRef} className="w-full h-full" />
      {isLoading && <LoadingSpinner message="正在加载地图..." />}
      <GridHeatmapLayer map={mapInstanceRef.current} />
      <SpeciesFilter />
      <MapStats />
      <HelpPanel />
      <ColorLegend className="absolute bottom-4 right-4 z-[1000] max-w-xs animate-slide-up" />
      <ZoomIndicator className="absolute bottom-4 left-4 z-[1000] max-w-xs animate-slide-up" />
      <MapControls map={mapInstanceRef.current} />
    </div>
  );
};

export default MapContainer;
