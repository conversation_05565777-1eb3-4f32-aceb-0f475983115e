import { create } from 'zustand';
import type { Species, ObservationPoint, HotspotData } from '../types';

interface AppState {
  // Species data
  species: Species[];
  selectedSpecies: string[];

  // Search and filters
  searchQuery: string;

  // Map state
  mapCenter: [number, number];
  mapZoom: number;
  selectedMapType: string;

  // Hotspot data
  hotspots: HotspotData[];
  observations: ObservationPoint[];

  // Actions
  setSearchQuery: (query: string) => void;
  toggleSpeciesVisibility: (speciesId: string) => void;
  setSelectedSpecies: (speciesIds: string[]) => void;
  setMapCenter: (center: [number, number]) => void;
  setMapZoom: (zoom: number) => void;
  setSelectedMapType: (mapType: string) => void;
  initializeData: () => void;
}

// Mock data
const mockSpecies: Species[] = [
  {
    id: 'sparrow',
    name: '麻雀',
    scientificName: 'Passer domesticus',
    color: '#FF6B6B',
    description: '常见的城市鸟类',
    isVisible: true,
  },
  {
    id: 'swallow',
    name: '燕子',
    scientificName: 'Hirundo rustica',
    color: '#4ECDC4',
    description: '迁徙性鸟类',
    isVisible: true,
  },
  {
    id: 'magpie',
    name: '喜鹊',
    scientificName: 'Pica pica',
    color: '#45B7D1',
    description: '智慧的鸟类',
    isVisible: true,
  },
  {
    id: 'pigeon',
    name: '鸽子',
    scientificName: 'Columba livia',
    color: '#96CEB4',
    description: '城市适应性强的鸟类',
    isVisible: true,
  },
  {
    id: 'robin',
    name: '知更鸟',
    scientificName: 'Erithacus rubecula',
    color: '#FFEAA7',
    description: '小型鸣禽',
    isVisible: true,
  },
];

const generateMockObservations = (): ObservationPoint[] => {
  const observations: ObservationPoint[] = [];
  const cities = [
    { lat: 39.9042, lng: 116.4074, name: '北京' },
    { lat: 31.2304, lng: 121.4737, name: '上海' },
    { lat: 23.1291, lng: 113.2644, name: '广州' },
    { lat: 30.5728, lng: 104.0668, name: '成都' },
    { lat: 29.5630, lng: 106.5516, name: '重庆' },
    { lat: 38.0428, lng: 114.5149, name: '石家庄' },
    { lat: 36.0611, lng: 103.8343, name: '兰州' },
    { lat: 43.8256, lng: 87.6168, name: '乌鲁木齐' },
  ];

  mockSpecies.forEach(species => {
    cities.forEach(city => {
      // Generate random observations around each city
      for (let i = 0; i < Math.random() * 20 + 5; i++) {
        const latOffset = (Math.random() - 0.5) * 2; // ±1 degree
        const lngOffset = (Math.random() - 0.5) * 2; // ±1 degree
        
        observations.push({
          lat: city.lat + latOffset,
          lng: city.lng + lngOffset,
          intensity: Math.random(),
          count: Math.floor(Math.random() * 100) + 1,
          speciesId: species.id,
        });
      }
    });
  });

  return observations;
};

export const useAppStore = create<AppState>((set) => ({
  // Initial state
  species: [],
  selectedSpecies: [],
  searchQuery: '',
  mapCenter: [39.9042, 116.4074], // Beijing
  mapZoom: 5,
  selectedMapType: 'terrain',
  hotspots: [],
  observations: [],

  // Actions
  setSearchQuery: (query) => set({ searchQuery: query }),
  
  toggleSpeciesVisibility: (speciesId) => set((state) => ({
    species: state.species.map(species =>
      species.id === speciesId
        ? { ...species, isVisible: !species.isVisible }
        : species
    ),
  })),
  
  setSelectedSpecies: (speciesIds) => set({ selectedSpecies: speciesIds }),
  setMapCenter: (center) => set({ mapCenter: center }),
  setMapZoom: (zoom) => set({ mapZoom: zoom }),
  setSelectedMapType: (mapType) => set({ selectedMapType: mapType }),
  
  initializeData: () => {
    const observations = generateMockObservations();
    set({
      species: mockSpecies,
      selectedSpecies: mockSpecies.map(s => s.id),
      observations,
    });
  },
}));
