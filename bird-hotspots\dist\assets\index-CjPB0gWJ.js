(function(){const T=document.createElement("link").relList;if(T&&T.supports&&T.supports("modulepreload"))return;for(const R of document.querySelectorAll('link[rel="modulepreload"]'))p(R);new MutationObserver(R=>{for(const q of R)if(q.type==="childList")for(const P of q.addedNodes)P.tagName==="LINK"&&P.rel==="modulepreload"&&p(P)}).observe(document,{childList:!0,subtree:!0});function x(R){const q={};return R.integrity&&(q.integrity=R.integrity),R.referrerPolicy&&(q.referrerPolicy=R.referrerPolicy),R.crossOrigin==="use-credentials"?q.credentials="include":R.crossOrigin==="anonymous"?q.credentials="omit":q.credentials="same-origin",q}function p(R){if(R.ep)return;R.ep=!0;const q=x(R);fetch(R.href,q)}})();function Dd(_){return _&&_.__esModule&&Object.prototype.hasOwnProperty.call(_,"default")?_.default:_}var _c={exports:{}},qs={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var md;function t_(){if(md)return qs;md=1;var _=Symbol.for("react.transitional.element"),T=Symbol.for("react.fragment");function x(p,R,q){var P=null;if(q!==void 0&&(P=""+q),R.key!==void 0&&(P=""+R.key),"key"in R){q={};for(var at in R)at!=="key"&&(q[at]=R[at])}else q=R;return R=q.ref,{$$typeof:_,type:p,key:P,ref:R!==void 0?R:null,props:q}}return qs.Fragment=T,qs.jsx=x,qs.jsxs=x,qs}var _d;function e_(){return _d||(_d=1,_c.exports=t_()),_c.exports}var y=e_(),pc={exports:{}},pt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var pd;function i_(){if(pd)return pt;pd=1;var _=Symbol.for("react.transitional.element"),T=Symbol.for("react.portal"),x=Symbol.for("react.fragment"),p=Symbol.for("react.strict_mode"),R=Symbol.for("react.profiler"),q=Symbol.for("react.consumer"),P=Symbol.for("react.context"),at=Symbol.for("react.forward_ref"),z=Symbol.for("react.suspense"),A=Symbol.for("react.memo"),k=Symbol.for("react.lazy"),W=Symbol.iterator;function et(m){return m===null||typeof m!="object"?null:(m=W&&m[W]||m["@@iterator"],typeof m=="function"?m:null)}var St={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},st=Object.assign,rt={};function Dt(m,B,V){this.props=m,this.context=B,this.refs=rt,this.updater=V||St}Dt.prototype.isReactComponent={},Dt.prototype.setState=function(m,B){if(typeof m!="object"&&typeof m!="function"&&m!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,m,B,"setState")},Dt.prototype.forceUpdate=function(m){this.updater.enqueueForceUpdate(this,m,"forceUpdate")};function ge(){}ge.prototype=Dt.prototype;function $t(m,B,V){this.props=m,this.context=B,this.refs=rt,this.updater=V||St}var dt=$t.prototype=new ge;dt.constructor=$t,st(dt,Dt.prototype),dt.isPureReactComponent=!0;var Lt=Array.isArray,ut={H:null,A:null,T:null,S:null,V:null},qt=Object.prototype.hasOwnProperty;function Ht(m,B,V,Y,J,F){return V=F.ref,{$$typeof:_,type:m,key:B,ref:V!==void 0?V:null,props:F}}function Qt(m,B){return Ht(m.type,B,void 0,void 0,void 0,m.props)}function Ft(m){return typeof m=="object"&&m!==null&&m.$$typeof===_}function ae(m){var B={"=":"=0",":":"=2"};return"$"+m.replace(/[=:]/g,function(V){return B[V]})}var mt=/\/+/g;function Mt(m,B){return typeof m=="object"&&m!==null&&m.key!=null?ae(""+m.key):B.toString(36)}function pi(){}function fe(m){switch(m.status){case"fulfilled":return m.value;case"rejected":throw m.reason;default:switch(typeof m.status=="string"?m.then(pi,pi):(m.status="pending",m.then(function(B){m.status==="pending"&&(m.status="fulfilled",m.value=B)},function(B){m.status==="pending"&&(m.status="rejected",m.reason=B)})),m.status){case"fulfilled":return m.value;case"rejected":throw m.reason}}throw m}function le(m,B,V,Y,J){var F=typeof m;(F==="undefined"||F==="boolean")&&(m=null);var X=!1;if(m===null)X=!0;else switch(F){case"bigint":case"string":case"number":X=!0;break;case"object":switch(m.$$typeof){case _:case T:X=!0;break;case k:return X=m._init,le(X(m._payload),B,V,Y,J)}}if(X)return J=J(m),X=Y===""?"."+Mt(m,0):Y,Lt(J)?(V="",X!=null&&(V=X.replace(mt,"$&/")+"/"),le(J,B,V,"",function(vi){return vi})):J!=null&&(Ft(J)&&(J=Qt(J,V+(J.key==null||m&&m.key===J.key?"":(""+J.key).replace(mt,"$&/")+"/")+X)),B.push(J)),1;X=0;var Kt=Y===""?".":Y+":";if(Lt(m))for(var Et=0;Et<m.length;Et++)Y=m[Et],F=Kt+Mt(Y,Et),X+=le(Y,B,V,F,J);else if(Et=et(m),typeof Et=="function")for(m=Et.call(m),Et=0;!(Y=m.next()).done;)Y=Y.value,F=Kt+Mt(Y,Et++),X+=le(Y,B,V,F,J);else if(F==="object"){if(typeof m.then=="function")return le(fe(m),B,V,Y,J);throw B=String(m),Error("Objects are not valid as a React child (found: "+(B==="[object Object]"?"object with keys {"+Object.keys(m).join(", ")+"}":B)+"). If you meant to render a collection of children, use an array instead.")}return X}function D(m,B,V){if(m==null)return m;var Y=[],J=0;return le(m,Y,"","",function(F){return B.call(V,F,J++)}),Y}function Q(m){if(m._status===-1){var B=m._result;B=B(),B.then(function(V){(m._status===0||m._status===-1)&&(m._status=1,m._result=V)},function(V){(m._status===0||m._status===-1)&&(m._status=2,m._result=V)}),m._status===-1&&(m._status=0,m._result=B)}if(m._status===1)return m._result.default;throw m._result}var G=typeof reportError=="function"?reportError:function(m){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var B=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof m=="object"&&m!==null&&typeof m.message=="string"?String(m.message):String(m),error:m});if(!window.dispatchEvent(B))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",m);return}console.error(m)};function At(){}return pt.Children={map:D,forEach:function(m,B,V){D(m,function(){B.apply(this,arguments)},V)},count:function(m){var B=0;return D(m,function(){B++}),B},toArray:function(m){return D(m,function(B){return B})||[]},only:function(m){if(!Ft(m))throw Error("React.Children.only expected to receive a single React element child.");return m}},pt.Component=Dt,pt.Fragment=x,pt.Profiler=R,pt.PureComponent=$t,pt.StrictMode=p,pt.Suspense=z,pt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=ut,pt.__COMPILER_RUNTIME={__proto__:null,c:function(m){return ut.H.useMemoCache(m)}},pt.cache=function(m){return function(){return m.apply(null,arguments)}},pt.cloneElement=function(m,B,V){if(m==null)throw Error("The argument must be a React element, but you passed "+m+".");var Y=st({},m.props),J=m.key,F=void 0;if(B!=null)for(X in B.ref!==void 0&&(F=void 0),B.key!==void 0&&(J=""+B.key),B)!qt.call(B,X)||X==="key"||X==="__self"||X==="__source"||X==="ref"&&B.ref===void 0||(Y[X]=B[X]);var X=arguments.length-2;if(X===1)Y.children=V;else if(1<X){for(var Kt=Array(X),Et=0;Et<X;Et++)Kt[Et]=arguments[Et+2];Y.children=Kt}return Ht(m.type,J,void 0,void 0,F,Y)},pt.createContext=function(m){return m={$$typeof:P,_currentValue:m,_currentValue2:m,_threadCount:0,Provider:null,Consumer:null},m.Provider=m,m.Consumer={$$typeof:q,_context:m},m},pt.createElement=function(m,B,V){var Y,J={},F=null;if(B!=null)for(Y in B.key!==void 0&&(F=""+B.key),B)qt.call(B,Y)&&Y!=="key"&&Y!=="__self"&&Y!=="__source"&&(J[Y]=B[Y]);var X=arguments.length-2;if(X===1)J.children=V;else if(1<X){for(var Kt=Array(X),Et=0;Et<X;Et++)Kt[Et]=arguments[Et+2];J.children=Kt}if(m&&m.defaultProps)for(Y in X=m.defaultProps,X)J[Y]===void 0&&(J[Y]=X[Y]);return Ht(m,F,void 0,void 0,null,J)},pt.createRef=function(){return{current:null}},pt.forwardRef=function(m){return{$$typeof:at,render:m}},pt.isValidElement=Ft,pt.lazy=function(m){return{$$typeof:k,_payload:{_status:-1,_result:m},_init:Q}},pt.memo=function(m,B){return{$$typeof:A,type:m,compare:B===void 0?null:B}},pt.startTransition=function(m){var B=ut.T,V={};ut.T=V;try{var Y=m(),J=ut.S;J!==null&&J(V,Y),typeof Y=="object"&&Y!==null&&typeof Y.then=="function"&&Y.then(At,G)}catch(F){G(F)}finally{ut.T=B}},pt.unstable_useCacheRefresh=function(){return ut.H.useCacheRefresh()},pt.use=function(m){return ut.H.use(m)},pt.useActionState=function(m,B,V){return ut.H.useActionState(m,B,V)},pt.useCallback=function(m,B){return ut.H.useCallback(m,B)},pt.useContext=function(m){return ut.H.useContext(m)},pt.useDebugValue=function(){},pt.useDeferredValue=function(m,B){return ut.H.useDeferredValue(m,B)},pt.useEffect=function(m,B,V){var Y=ut.H;if(typeof V=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return Y.useEffect(m,B)},pt.useId=function(){return ut.H.useId()},pt.useImperativeHandle=function(m,B,V){return ut.H.useImperativeHandle(m,B,V)},pt.useInsertionEffect=function(m,B){return ut.H.useInsertionEffect(m,B)},pt.useLayoutEffect=function(m,B){return ut.H.useLayoutEffect(m,B)},pt.useMemo=function(m,B){return ut.H.useMemo(m,B)},pt.useOptimistic=function(m,B){return ut.H.useOptimistic(m,B)},pt.useReducer=function(m,B,V){return ut.H.useReducer(m,B,V)},pt.useRef=function(m){return ut.H.useRef(m)},pt.useState=function(m){return ut.H.useState(m)},pt.useSyncExternalStore=function(m,B,V){return ut.H.useSyncExternalStore(m,B,V)},pt.useTransition=function(){return ut.H.useTransition()},pt.version="19.1.0",pt}var vd;function Tc(){return vd||(vd=1,pc.exports=i_()),pc.exports}var jt=Tc();const gd=Dd(jt);var vc={exports:{}},Ps={},gc={exports:{}},yc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yd;function n_(){return yd||(yd=1,function(_){function T(D,Q){var G=D.length;D.push(Q);t:for(;0<G;){var At=G-1>>>1,m=D[At];if(0<R(m,Q))D[At]=Q,D[G]=m,G=At;else break t}}function x(D){return D.length===0?null:D[0]}function p(D){if(D.length===0)return null;var Q=D[0],G=D.pop();if(G!==Q){D[0]=G;t:for(var At=0,m=D.length,B=m>>>1;At<B;){var V=2*(At+1)-1,Y=D[V],J=V+1,F=D[J];if(0>R(Y,G))J<m&&0>R(F,Y)?(D[At]=F,D[J]=G,At=J):(D[At]=Y,D[V]=G,At=V);else if(J<m&&0>R(F,G))D[At]=F,D[J]=G,At=J;else break t}}return Q}function R(D,Q){var G=D.sortIndex-Q.sortIndex;return G!==0?G:D.id-Q.id}if(_.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var q=performance;_.unstable_now=function(){return q.now()}}else{var P=Date,at=P.now();_.unstable_now=function(){return P.now()-at}}var z=[],A=[],k=1,W=null,et=3,St=!1,st=!1,rt=!1,Dt=!1,ge=typeof setTimeout=="function"?setTimeout:null,$t=typeof clearTimeout=="function"?clearTimeout:null,dt=typeof setImmediate<"u"?setImmediate:null;function Lt(D){for(var Q=x(A);Q!==null;){if(Q.callback===null)p(A);else if(Q.startTime<=D)p(A),Q.sortIndex=Q.expirationTime,T(z,Q);else break;Q=x(A)}}function ut(D){if(rt=!1,Lt(D),!st)if(x(z)!==null)st=!0,qt||(qt=!0,Mt());else{var Q=x(A);Q!==null&&le(ut,Q.startTime-D)}}var qt=!1,Ht=-1,Qt=5,Ft=-1;function ae(){return Dt?!0:!(_.unstable_now()-Ft<Qt)}function mt(){if(Dt=!1,qt){var D=_.unstable_now();Ft=D;var Q=!0;try{t:{st=!1,rt&&(rt=!1,$t(Ht),Ht=-1),St=!0;var G=et;try{e:{for(Lt(D),W=x(z);W!==null&&!(W.expirationTime>D&&ae());){var At=W.callback;if(typeof At=="function"){W.callback=null,et=W.priorityLevel;var m=At(W.expirationTime<=D);if(D=_.unstable_now(),typeof m=="function"){W.callback=m,Lt(D),Q=!0;break e}W===x(z)&&p(z),Lt(D)}else p(z);W=x(z)}if(W!==null)Q=!0;else{var B=x(A);B!==null&&le(ut,B.startTime-D),Q=!1}}break t}finally{W=null,et=G,St=!1}Q=void 0}}finally{Q?Mt():qt=!1}}}var Mt;if(typeof dt=="function")Mt=function(){dt(mt)};else if(typeof MessageChannel<"u"){var pi=new MessageChannel,fe=pi.port2;pi.port1.onmessage=mt,Mt=function(){fe.postMessage(null)}}else Mt=function(){ge(mt,0)};function le(D,Q){Ht=ge(function(){D(_.unstable_now())},Q)}_.unstable_IdlePriority=5,_.unstable_ImmediatePriority=1,_.unstable_LowPriority=4,_.unstable_NormalPriority=3,_.unstable_Profiling=null,_.unstable_UserBlockingPriority=2,_.unstable_cancelCallback=function(D){D.callback=null},_.unstable_forceFrameRate=function(D){0>D||125<D?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Qt=0<D?Math.floor(1e3/D):5},_.unstable_getCurrentPriorityLevel=function(){return et},_.unstable_next=function(D){switch(et){case 1:case 2:case 3:var Q=3;break;default:Q=et}var G=et;et=Q;try{return D()}finally{et=G}},_.unstable_requestPaint=function(){Dt=!0},_.unstable_runWithPriority=function(D,Q){switch(D){case 1:case 2:case 3:case 4:case 5:break;default:D=3}var G=et;et=D;try{return Q()}finally{et=G}},_.unstable_scheduleCallback=function(D,Q,G){var At=_.unstable_now();switch(typeof G=="object"&&G!==null?(G=G.delay,G=typeof G=="number"&&0<G?At+G:At):G=At,D){case 1:var m=-1;break;case 2:m=250;break;case 5:m=1073741823;break;case 4:m=1e4;break;default:m=5e3}return m=G+m,D={id:k++,callback:Q,priorityLevel:D,startTime:G,expirationTime:m,sortIndex:-1},G>At?(D.sortIndex=G,T(A,D),x(z)===null&&D===x(A)&&(rt?($t(Ht),Ht=-1):rt=!0,le(ut,G-At))):(D.sortIndex=m,T(z,D),st||St||(st=!0,qt||(qt=!0,Mt()))),D},_.unstable_shouldYield=ae,_.unstable_wrapCallback=function(D){var Q=et;return function(){var G=et;et=Q;try{return D.apply(this,arguments)}finally{et=G}}}}(yc)),yc}var xd;function a_(){return xd||(xd=1,gc.exports=n_()),gc.exports}var xc={exports:{}},Ce={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bd;function l_(){if(bd)return Ce;bd=1;var _=Tc();function T(z){var A="https://react.dev/errors/"+z;if(1<arguments.length){A+="?args[]="+encodeURIComponent(arguments[1]);for(var k=2;k<arguments.length;k++)A+="&args[]="+encodeURIComponent(arguments[k])}return"Minified React error #"+z+"; visit "+A+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function x(){}var p={d:{f:x,r:function(){throw Error(T(522))},D:x,C:x,L:x,m:x,X:x,S:x,M:x},p:0,findDOMNode:null},R=Symbol.for("react.portal");function q(z,A,k){var W=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:R,key:W==null?null:""+W,children:z,containerInfo:A,implementation:k}}var P=_.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function at(z,A){if(z==="font")return"";if(typeof A=="string")return A==="use-credentials"?A:""}return Ce.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=p,Ce.createPortal=function(z,A){var k=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!A||A.nodeType!==1&&A.nodeType!==9&&A.nodeType!==11)throw Error(T(299));return q(z,A,null,k)},Ce.flushSync=function(z){var A=P.T,k=p.p;try{if(P.T=null,p.p=2,z)return z()}finally{P.T=A,p.p=k,p.d.f()}},Ce.preconnect=function(z,A){typeof z=="string"&&(A?(A=A.crossOrigin,A=typeof A=="string"?A==="use-credentials"?A:"":void 0):A=null,p.d.C(z,A))},Ce.prefetchDNS=function(z){typeof z=="string"&&p.d.D(z)},Ce.preinit=function(z,A){if(typeof z=="string"&&A&&typeof A.as=="string"){var k=A.as,W=at(k,A.crossOrigin),et=typeof A.integrity=="string"?A.integrity:void 0,St=typeof A.fetchPriority=="string"?A.fetchPriority:void 0;k==="style"?p.d.S(z,typeof A.precedence=="string"?A.precedence:void 0,{crossOrigin:W,integrity:et,fetchPriority:St}):k==="script"&&p.d.X(z,{crossOrigin:W,integrity:et,fetchPriority:St,nonce:typeof A.nonce=="string"?A.nonce:void 0})}},Ce.preinitModule=function(z,A){if(typeof z=="string")if(typeof A=="object"&&A!==null){if(A.as==null||A.as==="script"){var k=at(A.as,A.crossOrigin);p.d.M(z,{crossOrigin:k,integrity:typeof A.integrity=="string"?A.integrity:void 0,nonce:typeof A.nonce=="string"?A.nonce:void 0})}}else A==null&&p.d.M(z)},Ce.preload=function(z,A){if(typeof z=="string"&&typeof A=="object"&&A!==null&&typeof A.as=="string"){var k=A.as,W=at(k,A.crossOrigin);p.d.L(z,k,{crossOrigin:W,integrity:typeof A.integrity=="string"?A.integrity:void 0,nonce:typeof A.nonce=="string"?A.nonce:void 0,type:typeof A.type=="string"?A.type:void 0,fetchPriority:typeof A.fetchPriority=="string"?A.fetchPriority:void 0,referrerPolicy:typeof A.referrerPolicy=="string"?A.referrerPolicy:void 0,imageSrcSet:typeof A.imageSrcSet=="string"?A.imageSrcSet:void 0,imageSizes:typeof A.imageSizes=="string"?A.imageSizes:void 0,media:typeof A.media=="string"?A.media:void 0})}},Ce.preloadModule=function(z,A){if(typeof z=="string")if(A){var k=at(A.as,A.crossOrigin);p.d.m(z,{as:typeof A.as=="string"&&A.as!=="script"?A.as:void 0,crossOrigin:k,integrity:typeof A.integrity=="string"?A.integrity:void 0})}else p.d.m(z)},Ce.requestFormReset=function(z){p.d.r(z)},Ce.unstable_batchedUpdates=function(z,A){return z(A)},Ce.useFormState=function(z,A,k){return P.H.useFormState(z,A,k)},Ce.useFormStatus=function(){return P.H.useHostTransitionStatus()},Ce.version="19.1.0",Ce}var Sd;function s_(){if(Sd)return xc.exports;Sd=1;function _(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(_)}catch(T){console.error(T)}}return _(),xc.exports=l_(),xc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Td;function o_(){if(Td)return Ps;Td=1;var _=a_(),T=Tc(),x=s_();function p(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function R(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function q(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function P(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function at(t){if(q(t)!==t)throw Error(p(188))}function z(t){var e=t.alternate;if(!e){if(e=q(t),e===null)throw Error(p(188));return e!==t?null:t}for(var n=t,l=e;;){var o=n.return;if(o===null)break;var r=o.alternate;if(r===null){if(l=o.return,l!==null){n=l;continue}break}if(o.child===r.child){for(r=o.child;r;){if(r===n)return at(o),t;if(r===l)return at(o),e;r=r.sibling}throw Error(p(188))}if(n.return!==l.return)n=o,l=r;else{for(var f=!1,d=o.child;d;){if(d===n){f=!0,n=o,l=r;break}if(d===l){f=!0,l=o,n=r;break}d=d.sibling}if(!f){for(d=r.child;d;){if(d===n){f=!0,n=r,l=o;break}if(d===l){f=!0,l=r,n=o;break}d=d.sibling}if(!f)throw Error(p(189))}}if(n.alternate!==l)throw Error(p(190))}if(n.tag!==3)throw Error(p(188));return n.stateNode.current===n?t:e}function A(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=A(t),e!==null)return e;t=t.sibling}return null}var k=Object.assign,W=Symbol.for("react.element"),et=Symbol.for("react.transitional.element"),St=Symbol.for("react.portal"),st=Symbol.for("react.fragment"),rt=Symbol.for("react.strict_mode"),Dt=Symbol.for("react.profiler"),ge=Symbol.for("react.provider"),$t=Symbol.for("react.consumer"),dt=Symbol.for("react.context"),Lt=Symbol.for("react.forward_ref"),ut=Symbol.for("react.suspense"),qt=Symbol.for("react.suspense_list"),Ht=Symbol.for("react.memo"),Qt=Symbol.for("react.lazy"),Ft=Symbol.for("react.activity"),ae=Symbol.for("react.memo_cache_sentinel"),mt=Symbol.iterator;function Mt(t){return t===null||typeof t!="object"?null:(t=mt&&t[mt]||t["@@iterator"],typeof t=="function"?t:null)}var pi=Symbol.for("react.client.reference");function fe(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===pi?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case st:return"Fragment";case Dt:return"Profiler";case rt:return"StrictMode";case ut:return"Suspense";case qt:return"SuspenseList";case Ft:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case St:return"Portal";case dt:return(t.displayName||"Context")+".Provider";case $t:return(t._context.displayName||"Context")+".Consumer";case Lt:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Ht:return e=t.displayName||null,e!==null?e:fe(t.type)||"Memo";case Qt:e=t._payload,t=t._init;try{return fe(t(e))}catch{}}return null}var le=Array.isArray,D=T.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Q=x.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,G={pending:!1,data:null,method:null,action:null},At=[],m=-1;function B(t){return{current:t}}function V(t){0>m||(t.current=At[m],At[m]=null,m--)}function Y(t,e){m++,At[m]=t.current,t.current=e}var J=B(null),F=B(null),X=B(null),Kt=B(null);function Et(t,e){switch(Y(X,e),Y(F,t),Y(J,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?kh(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=kh(e),t=Gh(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}V(J),Y(J,t)}function vi(){V(J),V(F),V(X)}function _a(t){t.memoizedState!==null&&Y(Kt,t);var e=J.current,n=Gh(e,t.type);e!==n&&(Y(F,t),Y(J,n))}function bn(t){F.current===t&&(V(J),V(F)),Kt.current===t&&(V(Kt),Bs._currentValue=G)}var Gi=Object.prototype.hasOwnProperty,pa=_.unstable_scheduleCallback,Zl=_.unstable_cancelCallback,Gs=_.unstable_shouldYield,Ys=_.unstable_requestPaint,qe=_.unstable_now,Xa=_.unstable_getCurrentPriorityLevel,Vs=_.unstable_ImmediatePriority,jl=_.unstable_UserBlockingPriority,Sn=_.unstable_NormalPriority,Xs=_.unstable_LowPriority,Hl=_.unstable_IdlePriority,xr=_.log,br=_.unstable_setDisableYieldValue,Yi=null,Le=null;function gi(t){if(typeof xr=="function"&&br(t),Le&&typeof Le.setStrictMode=="function")try{Le.setStrictMode(Yi,t)}catch{}}var De=Math.clz32?Math.clz32:Sr,Qs=Math.log,Ks=Math.LN2;function Sr(t){return t>>>=0,t===0?32:31-(Qs(t)/Ks|0)|0}var va=256,Tn=4194304;function Oi(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Qa(t,e,n){var l=t.pendingLanes;if(l===0)return 0;var o=0,r=t.suspendedLanes,f=t.pingedLanes;t=t.warmLanes;var d=l&134217727;return d!==0?(l=d&~r,l!==0?o=Oi(l):(f&=d,f!==0?o=Oi(f):n||(n=d&~t,n!==0&&(o=Oi(n))))):(d=l&~r,d!==0?o=Oi(d):f!==0?o=Oi(f):n||(n=l&~t,n!==0&&(o=Oi(n)))),o===0?0:e!==0&&e!==o&&(e&r)===0&&(r=o&-o,n=e&-e,r>=n||r===32&&(n&4194048)!==0)?e:o}function yi(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function Tr(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Js(){var t=va;return va<<=1,(va&4194048)===0&&(va=256),t}function Ul(){var t=Tn;return Tn<<=1,(Tn&62914560)===0&&(Tn=4194304),t}function Ka(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function wn(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function wr(t,e,n,l,o,r){var f=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var d=t.entanglements,g=t.expirationTimes,E=t.hiddenUpdates;for(n=f&~n;0<n;){var Z=31-De(n),H=1<<Z;d[Z]=0,g[Z]=-1;var O=E[Z];if(O!==null)for(E[Z]=null,Z=0;Z<O.length;Z++){var N=O[Z];N!==null&&(N.lane&=-536870913)}n&=~H}l!==0&&Ws(t,l,0),r!==0&&o===0&&t.tag!==0&&(t.suspendedLanes|=r&~(f&~e))}function Ws(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-De(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|n&4194090}function Is(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var l=31-De(n),o=1<<l;o&e|t[l]&e&&(t[l]|=e),n&=~o}}function ql(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function Pl(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function $s(){var t=Q.p;return t!==0?t:(t=window.event,t===void 0?32:rd(t.type))}function kl(t,e){var n=Q.p;try{return Q.p=t,e()}finally{Q.p=n}}var Ai=Math.random().toString(36).slice(2),he="__reactFiber$"+Ai,ze="__reactProps$"+Ai,Mn="__reactContainer$"+Ai,Pe="__reactEvents$"+Ai,it="__reactListeners$"+Ai,Fs="__reactHandles$"+Ai,Gl="__reactResources$"+Ai,En="__reactMarker$"+Ai;function Ja(t){delete t[he],delete t[ze],delete t[Pe],delete t[it],delete t[Fs]}function Ni(t){var e=t[he];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Mn]||n[he]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=Qh(t);t!==null;){if(n=t[he])return n;t=Qh(t)}return e}t=n,n=t.parentNode}return null}function Vi(t){if(t=t[he]||t[Mn]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function li(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(p(33))}function Xi(t){var e=t[Gl];return e||(e=t[Gl]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function se(t){t[En]=!0}var to=new Set,eo={};function Qi(t,e){Ki(t,e),Ki(t+"Capture",e)}function Ki(t,e){for(eo[t]=e,t=0;t<e.length;t++)to.add(e[t])}var Mr=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),ga={},io={};function Er(t){return Gi.call(io,t)?!0:Gi.call(ga,t)?!1:Mr.test(t)?io[t]=!0:(ga[t]=!0,!1)}function Wa(t,e,n){if(Er(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function Ia(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function xi(t,e,n,l){if(l===null)t.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+l)}}var ya,Ln;function Ji(t){if(ya===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);ya=e&&e[1]||"",Ln=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+ya+t+Ln}var $a=!1;function Wi(t,e){if(!t||$a)return"";$a=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var H=function(){throw Error()};if(Object.defineProperty(H.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(H,[])}catch(N){var O=N}Reflect.construct(t,[],H)}else{try{H.call()}catch(N){O=N}t.call(H.prototype)}}else{try{throw Error()}catch(N){O=N}(H=t())&&typeof H.catch=="function"&&H.catch(function(){})}}catch(N){if(N&&O&&typeof N.stack=="string")return[N.stack,O.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var r=l.DetermineComponentFrameRoot(),f=r[0],d=r[1];if(f&&d){var g=f.split(`
`),E=d.split(`
`);for(o=l=0;l<g.length&&!g[l].includes("DetermineComponentFrameRoot");)l++;for(;o<E.length&&!E[o].includes("DetermineComponentFrameRoot");)o++;if(l===g.length||o===E.length)for(l=g.length-1,o=E.length-1;1<=l&&0<=o&&g[l]!==E[o];)o--;for(;1<=l&&0<=o;l--,o--)if(g[l]!==E[o]){if(l!==1||o!==1)do if(l--,o--,0>o||g[l]!==E[o]){var Z=`
`+g[l].replace(" at new "," at ");return t.displayName&&Z.includes("<anonymous>")&&(Z=Z.replace("<anonymous>",t.displayName)),Z}while(1<=l&&0<=o);break}}}finally{$a=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?Ji(n):""}function Tt(t){switch(t.tag){case 26:case 27:case 5:return Ji(t.type);case 16:return Ji("Lazy");case 13:return Ji("Suspense");case 19:return Ji("SuspenseList");case 0:case 15:return Wi(t.type,!1);case 11:return Wi(t.type.render,!1);case 1:return Wi(t.type,!0);case 31:return Ji("Activity");default:return""}}function Pt(t){try{var e="";do e+=Tt(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function ye(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Ii(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function zn(t){var e=Ii(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,r=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return o.call(this)},set:function(f){l=""+f,r.call(this,f)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(f){l=""+f},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function On(t){t._valueTracker||(t._valueTracker=zn(t))}function _t(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),l="";return t&&(l=Ii(t)?t.checked?"true":"false":t.value),t=l,t!==n?(e.setValue(t),!0):!1}function kt(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var Yl=/[\n"\\]/g;function xe(t){return t.replace(Yl,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Oe(t,e,n,l,o,r,f,d){t.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?t.type=f:t.removeAttribute("type"),e!=null?f==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+ye(e)):t.value!==""+ye(e)&&(t.value=""+ye(e)):f!=="submit"&&f!=="reset"||t.removeAttribute("value"),e!=null?An(t,f,ye(e)):n!=null?An(t,f,ye(n)):l!=null&&t.removeAttribute("value"),o==null&&r!=null&&(t.defaultChecked=!!r),o!=null&&(t.checked=o&&typeof o!="function"&&typeof o!="symbol"),d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?t.name=""+ye(d):t.removeAttribute("name")}function no(t,e,n,l,o,r,f,d){if(r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(t.type=r),e!=null||n!=null){if(!(r!=="submit"&&r!=="reset"||e!=null))return;n=n!=null?""+ye(n):"",e=e!=null?""+ye(e):n,d||e===t.value||(t.value=e),t.defaultValue=e}l=l??o,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=d?t.checked:!!l,t.defaultChecked=!!l,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.name=f)}function An(t,e,n){e==="number"&&kt(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function ke(t,e,n,l){if(t=t.options,e){e={};for(var o=0;o<n.length;o++)e["$"+n[o]]=!0;for(n=0;n<t.length;n++)o=e.hasOwnProperty("$"+t[n].value),t[n].selected!==o&&(t[n].selected=o),o&&l&&(t[n].defaultSelected=!0)}else{for(n=""+ye(n),e=null,o=0;o<t.length;o++){if(t[o].value===n){t[o].selected=!0,l&&(t[o].defaultSelected=!0);return}e!==null||t[o].disabled||(e=t[o])}e!==null&&(e.selected=!0)}}function Jt(t,e,n){if(e!=null&&(e=""+ye(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+ye(n):""}function Ci(t,e,n,l){if(e==null){if(l!=null){if(n!=null)throw Error(p(92));if(le(l)){if(1<l.length)throw Error(p(93));l=l[0]}n=l}n==null&&(n=""),e=n}n=ye(e),t.defaultValue=n,l=t.textContent,l===n&&l!==""&&l!==null&&(t.value=l)}function si(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var xa=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Fa(t,e,n){var l=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,n):typeof n!="number"||n===0||xa.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function Nn(t,e,n){if(e!=null&&typeof e!="object")throw Error(p(62));if(t=t.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var o in e)l=e[o],e.hasOwnProperty(o)&&n[o]!==l&&Fa(t,o,l)}else for(var r in e)e.hasOwnProperty(r)&&Fa(t,r,e[r])}function ba(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Vl=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),tl=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Cn(t){return tl.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Sa=null;function Dn(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var $i=null,Di=null;function ao(t){var e=Vi(t);if(e&&(t=e.stateNode)){var n=t[ze]||null;t:switch(t=e.stateNode,e.type){case"input":if(Oe(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+xe(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var l=n[e];if(l!==t&&l.form===t.form){var o=l[ze]||null;if(!o)throw Error(p(90));Oe(l,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(e=0;e<n.length;e++)l=n[e],l.form===t.form&&_t(l)}break t;case"textarea":Jt(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&ke(t,!!n.multiple,e,!1)}}}var ht=!1;function Qe(t,e,n){if(ht)return t(e,n);ht=!0;try{var l=t(e);return l}finally{if(ht=!1,($i!==null||Di!==null)&&(Fo(),$i&&(e=$i,t=Di,Di=$i=null,ao(e),t)))for(e=0;e<t.length;e++)ao(t[e])}}function zt(t,e){var n=t.stateNode;if(n===null)return null;var l=n[ze]||null;if(l===null)return null;n=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(p(231,e,typeof n));return n}var oi=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ta=!1;if(oi)try{var Fi={};Object.defineProperty(Fi,"passive",{get:function(){Ta=!0}}),window.addEventListener("test",Fi,Fi),window.removeEventListener("test",Fi,Fi)}catch{Ta=!1}var ri=null,bi=null,Rn=null;function Bn(){if(Rn)return Rn;var t,e=bi,n=e.length,l,o="value"in ri?ri.value:ri.textContent,r=o.length;for(t=0;t<n&&e[t]===o[t];t++);var f=n-t;for(l=1;l<=f&&e[n-l]===o[r-l];l++);return Rn=o.slice(t,1<l?1-l:void 0)}function te(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function ui(){return!0}function Xl(){return!1}function be(t){function e(n,l,o,r,f){this._reactName=n,this._targetInst=o,this.type=l,this.nativeEvent=r,this.target=f,this.currentTarget=null;for(var d in t)t.hasOwnProperty(d)&&(n=t[d],this[d]=n?n(r):r[d]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?ui:Xl,this.isPropagationStopped=Xl,this}return k(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ui)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ui)},persist:function(){},isPersistent:ui}),e}var tn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},wa=be(tn),en=k({},tn,{view:0,detail:0}),Lr=be(en),el,gt,Ma,Ae=k({},en,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:il,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Ma&&(Ma&&t.type==="mousemove"?(el=t.screenX-Ma.screenX,gt=t.screenY-Ma.screenY):gt=el=0,Ma=t),el)},movementY:function(t){return"movementY"in t?t.movementY:gt}}),Zn=be(Ae),lo=k({},Ae,{dataTransfer:0}),zr=be(lo),Ql=k({},en,{relatedTarget:0}),Kl=be(Ql),so=k({},tn,{animationName:0,elapsedTime:0,pseudoElement:0}),Or=be(so),Ar=k({},tn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Jl=be(Ar),Nr=k({},tn,{data:0}),Ke=be(Nr),Cr={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},oo={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ri={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ro(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Ri[t])?!!e[t]:!1}function il(){return ro}var Wl=k({},en,{key:function(t){if(t.key){var e=Cr[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=te(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?oo[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:il,charCode:function(t){return t.type==="keypress"?te(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?te(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Dr=be(Wl),uo=k({},Ae,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Il=be(uo),Rr=k({},en,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:il}),Br=be(Rr),$l=k({},tn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Zr=be($l),co=k({},Ae,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),fo=be(co),nl=k({},tn,{newState:0,oldState:0}),nn=be(nl),jr=[9,13,27,32],an=oi&&"CompositionEvent"in window,de=null;oi&&"documentMode"in document&&(de=document.documentMode);var ho=oi&&"TextEvent"in window&&!de,Fl=oi&&(!an||de&&8<de&&11>=de),mo=" ",al=!1;function ll(t,e){switch(t){case"keyup":return jr.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function _o(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var jn=!1;function po(t,e){switch(t){case"compositionend":return _o(e);case"keypress":return e.which!==32?null:(al=!0,mo);case"textInput":return t=e.data,t===mo&&al?null:t;default:return null}}function Hr(t,e){if(jn)return t==="compositionend"||!an&&ll(t,e)?(t=Bn(),Rn=bi=ri=null,jn=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Fl&&e.locale!=="ko"?null:e.data;default:return null}}var Je={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ln(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Je[t.type]:e==="textarea"}function vo(t,e,n,l){$i?Di?Di.push(l):Di=[l]:$i=l,e=lr(e,"onChange"),0<e.length&&(n=new wa("onChange","change",null,n,l),t.push({event:n,listeners:e}))}var Re=null,Ea=null;function Hn(t){jh(t,0)}function sl(t){var e=li(t);if(_t(e))return t}function Un(t,e){if(t==="change")return e}var ts=!1;if(oi){var qn;if(oi){var es="oninput"in document;if(!es){var Si=document.createElement("div");Si.setAttribute("oninput","return;"),es=typeof Si.oninput=="function"}qn=es}else qn=!1;ts=qn&&(!document.documentMode||9<document.documentMode)}function La(){Re&&(Re.detachEvent("onpropertychange",go),Ea=Re=null)}function go(t){if(t.propertyName==="value"&&sl(Ea)){var e=[];vo(e,Ea,t,Dn(t)),Qe(Hn,e)}}function is(t,e,n){t==="focusin"?(La(),Re=e,Ea=n,Re.attachEvent("onpropertychange",go)):t==="focusout"&&La()}function Ur(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return sl(Ea)}function Ti(t,e){if(t==="click")return sl(e)}function qr(t,e){if(t==="input"||t==="change")return sl(e)}function Pn(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Be=typeof Object.is=="function"?Object.is:Pn;function Ze(t,e){if(Be(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),l=Object.keys(e);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var o=n[l];if(!Gi.call(e,o)||!Be(t[o],e[o]))return!1}return!0}function za(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function ns(t,e){var n=za(t);t=0;for(var l;n;){if(n.nodeType===3){if(l=t+n.textContent.length,t<=e&&l>=e)return{node:n,offset:e-t};t=l}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=za(n)}}function ol(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?ol(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Oa(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=kt(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=kt(t.document)}return e}function Aa(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var rl=oi&&"documentMode"in document&&11>=document.documentMode,We=null,kn=null,sn=null,ul=!1;function yo(t,e,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ul||We==null||We!==kt(l)||(l=We,"selectionStart"in l&&Aa(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),sn&&Ze(sn,l)||(sn=l,l=lr(kn,"onSelect"),0<l.length&&(e=new wa("onSelect","select",null,e,n),t.push({event:e,listeners:l}),e.target=We)))}function ci(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Gn={animationend:ci("Animation","AnimationEnd"),animationiteration:ci("Animation","AnimationIteration"),animationstart:ci("Animation","AnimationStart"),transitionrun:ci("Transition","TransitionRun"),transitionstart:ci("Transition","TransitionStart"),transitioncancel:ci("Transition","TransitionCancel"),transitionend:ci("Transition","TransitionEnd")},cl={},xo={};oi&&(xo=document.createElement("div").style,"AnimationEvent"in window||(delete Gn.animationend.animation,delete Gn.animationiteration.animation,delete Gn.animationstart.animation),"TransitionEvent"in window||delete Gn.transitionend.transition);function Bi(t){if(cl[t])return cl[t];if(!Gn[t])return t;var e=Gn[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in xo)return cl[t]=e[n];return t}var bo=Bi("animationend"),Ie=Bi("animationiteration"),Na=Bi("animationstart"),Pr=Bi("transitionrun"),fl=Bi("transitionstart"),kr=Bi("transitioncancel"),as=Bi("transitionend"),So=new Map,on="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");on.push("scrollEnd");function $e(t,e){So.set(t,e),Qi(e,[t])}var rn=new WeakMap;function je(t,e){if(typeof t=="object"&&t!==null){var n=rn.get(t);return n!==void 0?n:(e={value:t,source:e,stack:Pt(e)},rn.set(t,e),e)}return{value:t,source:e,stack:Pt(e)}}var He=[],Yn=0,Fe=0;function Ca(){for(var t=Yn,e=Fe=Yn=0;e<t;){var n=He[e];He[e++]=null;var l=He[e];He[e++]=null;var o=He[e];He[e++]=null;var r=He[e];if(He[e++]=null,l!==null&&o!==null){var f=l.pending;f===null?o.next=o:(o.next=f.next,f.next=o),l.pending=o}r!==0&&Ra(n,o,r)}}function Da(t,e,n,l){He[Yn++]=t,He[Yn++]=e,He[Yn++]=n,He[Yn++]=l,Fe|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function un(t,e,n,l){return Da(t,e,n,l),Zi(t)}function Vn(t,e){return Da(t,null,null,e),Zi(t)}function Ra(t,e,n){t.lanes|=n;var l=t.alternate;l!==null&&(l.lanes|=n);for(var o=!1,r=t.return;r!==null;)r.childLanes|=n,l=r.alternate,l!==null&&(l.childLanes|=n),r.tag===22&&(t=r.stateNode,t===null||t._visibility&1||(o=!0)),t=r,r=r.return;return t.tag===3?(r=t.stateNode,o&&e!==null&&(o=31-De(n),t=r.hiddenUpdates,l=t[o],l===null?t[o]=[e]:l.push(e),e.lane=n|536870912),r):null}function Zi(t){if(50<Ls)throw Ls=0,Uu=null,Error(p(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var cn={};function To(t,e,n,l){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ue(t,e,n,l){return new To(t,e,n,l)}function hl(t){return t=t.prototype,!(!t||!t.isReactComponent)}function fi(t,e){var n=t.alternate;return n===null?(n=Ue(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function ls(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Ba(t,e,n,l,o,r){var f=0;if(l=t,typeof t=="function")hl(t)&&(f=1);else if(typeof t=="string")f=Pm(t,n,J.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Ft:return t=Ue(31,n,e,o),t.elementType=Ft,t.lanes=r,t;case st:return ji(n.children,o,r,e);case rt:f=8,o|=24;break;case Dt:return t=Ue(12,n,e,o|2),t.elementType=Dt,t.lanes=r,t;case ut:return t=Ue(13,n,e,o),t.elementType=ut,t.lanes=r,t;case qt:return t=Ue(19,n,e,o),t.elementType=qt,t.lanes=r,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case ge:case dt:f=10;break t;case $t:f=9;break t;case Lt:f=11;break t;case Ht:f=14;break t;case Qt:f=16,l=null;break t}f=29,n=Error(p(130,t===null?"null":typeof t,"")),l=null}return e=Ue(f,n,e,o),e.elementType=t,e.type=l,e.lanes=r,e}function ji(t,e,n,l){return t=Ue(7,t,l,e),t.lanes=n,t}function ss(t,e,n){return t=Ue(6,t,null,e),t.lanes=n,t}function dl(t,e,n){return e=Ue(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var fn=[],Xn=0,i=null,a=0,s=[],u=0,c=null,h=1,v="";function w(t,e){fn[Xn++]=a,fn[Xn++]=i,i=t,a=e}function C(t,e,n){s[u++]=h,s[u++]=v,s[u++]=c,c=t;var l=h;t=v;var o=32-De(l)-1;l&=~(1<<o),n+=1;var r=32-De(e)+o;if(30<r){var f=o-o%5;r=(l&(1<<f)-1).toString(32),l>>=f,o-=f,h=1<<32-De(e)+o|n<<o|l,v=r+t}else h=1<<r|n<<o|l,v=t}function U(t){t.return!==null&&(w(t,1),C(t,1,0))}function K(t){for(;t===i;)i=fn[--Xn],fn[Xn]=null,a=fn[--Xn],fn[Xn]=null;for(;t===c;)c=s[--u],s[u]=null,v=s[--u],s[u]=null,h=s[--u],s[u]=null}var I=null,tt=null,ft=!1,Gt=null,Wt=!1,me=Error(p(519));function Ge(t){var e=Error(p(418,""));throw Kn(je(e,t)),me}function wo(t){var e=t.stateNode,n=t.type,l=t.memoizedProps;switch(e[he]=t,e[ze]=l,n){case"dialog":bt("cancel",e),bt("close",e);break;case"iframe":case"object":case"embed":bt("load",e);break;case"video":case"audio":for(n=0;n<Os.length;n++)bt(Os[n],e);break;case"source":bt("error",e);break;case"img":case"image":case"link":bt("error",e),bt("load",e);break;case"details":bt("toggle",e);break;case"input":bt("invalid",e),no(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),On(e);break;case"select":bt("invalid",e);break;case"textarea":bt("invalid",e),Ci(e,l.value,l.defaultValue,l.children),On(e)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||l.suppressHydrationWarning===!0||Ph(e.textContent,n)?(l.popover!=null&&(bt("beforetoggle",e),bt("toggle",e)),l.onScroll!=null&&bt("scroll",e),l.onScrollEnd!=null&&bt("scrollend",e),l.onClick!=null&&(e.onclick=sr),e=!0):e=!1,e||Ge(t)}function Mo(t){for(I=t.return;I;)switch(I.tag){case 5:case 13:Wt=!1;return;case 27:case 3:Wt=!0;return;default:I=I.return}}function Za(t){if(t!==I)return!1;if(!ft)return Mo(t),ft=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||ec(t.type,t.memoizedProps)),n=!n),n&&tt&&Ge(t),Mo(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(p(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){tt=Li(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}tt=null}}else e===27?(e=tt,ua(t.type)?(t=lc,lc=null,tt=t):tt=e):tt=I?Li(t.stateNode.nextSibling):null;return!0}function Qn(){tt=I=null,ft=!1}function Eo(){var t=Gt;return t!==null&&(Xe===null?Xe=t:Xe.push.apply(Xe,t),Gt=null),t}function Kn(t){Gt===null?Gt=[t]:Gt.push(t)}var Vt=B(null),hi=null,wi=null;function Hi(t,e,n){Y(Vt,e._currentValue),e._currentValue=n}function Mi(t){t._currentValue=Vt.current,V(Vt)}function ja(t,e,n){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===n)break;t=t.return}}function ml(t,e,n,l){var o=t.child;for(o!==null&&(o.return=t);o!==null;){var r=o.dependencies;if(r!==null){var f=o.child;r=r.firstContext;t:for(;r!==null;){var d=r;r=o;for(var g=0;g<e.length;g++)if(d.context===e[g]){r.lanes|=n,d=r.alternate,d!==null&&(d.lanes|=n),ja(r.return,n,t),l||(f=null);break t}r=d.next}}else if(o.tag===18){if(f=o.return,f===null)throw Error(p(341));f.lanes|=n,r=f.alternate,r!==null&&(r.lanes|=n),ja(f,n,t),f=null}else f=o.child;if(f!==null)f.return=o;else for(f=o;f!==null;){if(f===t){f=null;break}if(o=f.sibling,o!==null){o.return=f.return,f=o;break}f=f.return}o=f}}function Ha(t,e,n,l){t=null;for(var o=e,r=!1;o!==null;){if(!r){if((o.flags&524288)!==0)r=!0;else if((o.flags&262144)!==0)break}if(o.tag===10){var f=o.alternate;if(f===null)throw Error(p(387));if(f=f.memoizedProps,f!==null){var d=o.type;Be(o.pendingProps.value,f.value)||(t!==null?t.push(d):t=[d])}}else if(o===Kt.current){if(f=o.alternate,f===null)throw Error(p(387));f.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(t!==null?t.push(Bs):t=[Bs])}o=o.return}t!==null&&ml(e,t,n,l),e.flags|=262144}function Lo(t){for(t=t.firstContext;t!==null;){if(!Be(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Ua(t){hi=t,wi=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Ne(t){return Ec(hi,t)}function zo(t,e){return hi===null&&Ua(t),Ec(t,e)}function Ec(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},wi===null){if(t===null)throw Error(p(308));wi=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else wi=wi.next=e;return n}var qd=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},Pd=_.unstable_scheduleCallback,kd=_.unstable_NormalPriority,ue={$$typeof:dt,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Gr(){return{controller:new qd,data:new Map,refCount:0}}function os(t){t.refCount--,t.refCount===0&&Pd(kd,function(){t.controller.abort()})}var rs=null,Yr=0,_l=0,pl=null;function Gd(t,e){if(rs===null){var n=rs=[];Yr=0,_l=Xu(),pl={status:"pending",value:void 0,then:function(l){n.push(l)}}}return Yr++,e.then(Lc,Lc),e}function Lc(){if(--Yr===0&&rs!==null){pl!==null&&(pl.status="fulfilled");var t=rs;rs=null,_l=0,pl=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Yd(t,e){var n=[],l={status:"pending",value:null,reason:null,then:function(o){n.push(o)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var o=0;o<n.length;o++)(0,n[o])(e)},function(o){for(l.status="rejected",l.reason=o,o=0;o<n.length;o++)(0,n[o])(void 0)}),l}var zc=D.S;D.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Gd(t,e),zc!==null&&zc(t,e)};var qa=B(null);function Vr(){var t=qa.current;return t!==null?t:Yt.pooledCache}function Oo(t,e){e===null?Y(qa,qa.current):Y(qa,e.pool)}function Oc(){var t=Vr();return t===null?null:{parent:ue._currentValue,pool:t}}var us=Error(p(460)),Ac=Error(p(474)),Ao=Error(p(542)),Xr={then:function(){}};function Nc(t){return t=t.status,t==="fulfilled"||t==="rejected"}function No(){}function Cc(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(No,No),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Rc(t),t;default:if(typeof e.status=="string")e.then(No,No);else{if(t=Yt,t!==null&&100<t.shellSuspendCounter)throw Error(p(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var o=e;o.status="fulfilled",o.value=l}},function(l){if(e.status==="pending"){var o=e;o.status="rejected",o.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Rc(t),t}throw cs=e,us}}var cs=null;function Dc(){if(cs===null)throw Error(p(459));var t=cs;return cs=null,t}function Rc(t){if(t===us||t===Ao)throw Error(p(483))}var Jn=!1;function Qr(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Kr(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Wn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function In(t,e,n){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,(Nt&2)!==0){var o=l.pending;return o===null?e.next=e:(e.next=o.next,o.next=e),l.pending=e,e=Zi(t),Ra(t,null,n),e}return Da(t,l,e,n),Zi(t)}function fs(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,Is(t,n)}}function Jr(t,e){var n=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var o=null,r=null;if(n=n.firstBaseUpdate,n!==null){do{var f={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};r===null?o=r=f:r=r.next=f,n=n.next}while(n!==null);r===null?o=r=e:r=r.next=e}else o=r=e;n={baseState:l.baseState,firstBaseUpdate:o,lastBaseUpdate:r,shared:l.shared,callbacks:l.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var Wr=!1;function hs(){if(Wr){var t=pl;if(t!==null)throw t}}function ds(t,e,n,l){Wr=!1;var o=t.updateQueue;Jn=!1;var r=o.firstBaseUpdate,f=o.lastBaseUpdate,d=o.shared.pending;if(d!==null){o.shared.pending=null;var g=d,E=g.next;g.next=null,f===null?r=E:f.next=E,f=g;var Z=t.alternate;Z!==null&&(Z=Z.updateQueue,d=Z.lastBaseUpdate,d!==f&&(d===null?Z.firstBaseUpdate=E:d.next=E,Z.lastBaseUpdate=g))}if(r!==null){var H=o.baseState;f=0,Z=E=g=null,d=r;do{var O=d.lane&-536870913,N=O!==d.lane;if(N?(wt&O)===O:(l&O)===O){O!==0&&O===_l&&(Wr=!0),Z!==null&&(Z=Z.next={lane:0,tag:d.tag,payload:d.payload,callback:null,next:null});t:{var ct=t,lt=d;O=e;var Zt=n;switch(lt.tag){case 1:if(ct=lt.payload,typeof ct=="function"){H=ct.call(Zt,H,O);break t}H=ct;break t;case 3:ct.flags=ct.flags&-65537|128;case 0:if(ct=lt.payload,O=typeof ct=="function"?ct.call(Zt,H,O):ct,O==null)break t;H=k({},H,O);break t;case 2:Jn=!0}}O=d.callback,O!==null&&(t.flags|=64,N&&(t.flags|=8192),N=o.callbacks,N===null?o.callbacks=[O]:N.push(O))}else N={lane:O,tag:d.tag,payload:d.payload,callback:d.callback,next:null},Z===null?(E=Z=N,g=H):Z=Z.next=N,f|=O;if(d=d.next,d===null){if(d=o.shared.pending,d===null)break;N=d,d=N.next,N.next=null,o.lastBaseUpdate=N,o.shared.pending=null}}while(!0);Z===null&&(g=H),o.baseState=g,o.firstBaseUpdate=E,o.lastBaseUpdate=Z,r===null&&(o.shared.lanes=0),la|=f,t.lanes=f,t.memoizedState=H}}function Bc(t,e){if(typeof t!="function")throw Error(p(191,t));t.call(e)}function Zc(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)Bc(n[t],e)}var vl=B(null),Co=B(0);function jc(t,e){t=gn,Y(Co,t),Y(vl,e),gn=t|e.baseLanes}function Ir(){Y(Co,gn),Y(vl,vl.current)}function $r(){gn=Co.current,V(vl),V(Co)}var $n=0,vt=null,Rt=null,oe=null,Do=!1,gl=!1,Pa=!1,Ro=0,ms=0,yl=null,Vd=0;function ie(){throw Error(p(321))}function Fr(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!Be(t[n],e[n]))return!1;return!0}function tu(t,e,n,l,o,r){return $n=r,vt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,D.H=t===null||t.memoizedState===null?bf:Sf,Pa=!1,r=n(l,o),Pa=!1,gl&&(r=Uc(e,n,l,o)),Hc(t),r}function Hc(t){D.H=qo;var e=Rt!==null&&Rt.next!==null;if($n=0,oe=Rt=vt=null,Do=!1,ms=0,yl=null,e)throw Error(p(300));t===null||_e||(t=t.dependencies,t!==null&&Lo(t)&&(_e=!0))}function Uc(t,e,n,l){vt=t;var o=0;do{if(gl&&(yl=null),ms=0,gl=!1,25<=o)throw Error(p(301));if(o+=1,oe=Rt=null,t.updateQueue!=null){var r=t.updateQueue;r.lastEffect=null,r.events=null,r.stores=null,r.memoCache!=null&&(r.memoCache.index=0)}D.H=$d,r=e(n,l)}while(gl);return r}function Xd(){var t=D.H,e=t.useState()[0];return e=typeof e.then=="function"?_s(e):e,t=t.useState()[0],(Rt!==null?Rt.memoizedState:null)!==t&&(vt.flags|=1024),e}function eu(){var t=Ro!==0;return Ro=0,t}function iu(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function nu(t){if(Do){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Do=!1}$n=0,oe=Rt=vt=null,gl=!1,ms=Ro=0,yl=null}function Ye(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return oe===null?vt.memoizedState=oe=t:oe=oe.next=t,oe}function re(){if(Rt===null){var t=vt.alternate;t=t!==null?t.memoizedState:null}else t=Rt.next;var e=oe===null?vt.memoizedState:oe.next;if(e!==null)oe=e,Rt=t;else{if(t===null)throw vt.alternate===null?Error(p(467)):Error(p(310));Rt=t,t={memoizedState:Rt.memoizedState,baseState:Rt.baseState,baseQueue:Rt.baseQueue,queue:Rt.queue,next:null},oe===null?vt.memoizedState=oe=t:oe=oe.next=t}return oe}function au(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function _s(t){var e=ms;return ms+=1,yl===null&&(yl=[]),t=Cc(yl,t,e),e=vt,(oe===null?e.memoizedState:oe.next)===null&&(e=e.alternate,D.H=e===null||e.memoizedState===null?bf:Sf),t}function Bo(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return _s(t);if(t.$$typeof===dt)return Ne(t)}throw Error(p(438,String(t)))}function lu(t){var e=null,n=vt.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var l=vt.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(o){return o.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=au(),vt.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),l=0;l<t;l++)n[l]=ae;return e.index++,n}function hn(t,e){return typeof e=="function"?e(t):e}function Zo(t){var e=re();return su(e,Rt,t)}function su(t,e,n){var l=t.queue;if(l===null)throw Error(p(311));l.lastRenderedReducer=n;var o=t.baseQueue,r=l.pending;if(r!==null){if(o!==null){var f=o.next;o.next=r.next,r.next=f}e.baseQueue=o=r,l.pending=null}if(r=t.baseState,o===null)t.memoizedState=r;else{e=o.next;var d=f=null,g=null,E=e,Z=!1;do{var H=E.lane&-536870913;if(H!==E.lane?(wt&H)===H:($n&H)===H){var O=E.revertLane;if(O===0)g!==null&&(g=g.next={lane:0,revertLane:0,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null}),H===_l&&(Z=!0);else if(($n&O)===O){E=E.next,O===_l&&(Z=!0);continue}else H={lane:0,revertLane:E.revertLane,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null},g===null?(d=g=H,f=r):g=g.next=H,vt.lanes|=O,la|=O;H=E.action,Pa&&n(r,H),r=E.hasEagerState?E.eagerState:n(r,H)}else O={lane:H,revertLane:E.revertLane,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null},g===null?(d=g=O,f=r):g=g.next=O,vt.lanes|=H,la|=H;E=E.next}while(E!==null&&E!==e);if(g===null?f=r:g.next=d,!Be(r,t.memoizedState)&&(_e=!0,Z&&(n=pl,n!==null)))throw n;t.memoizedState=r,t.baseState=f,t.baseQueue=g,l.lastRenderedState=r}return o===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function ou(t){var e=re(),n=e.queue;if(n===null)throw Error(p(311));n.lastRenderedReducer=t;var l=n.dispatch,o=n.pending,r=e.memoizedState;if(o!==null){n.pending=null;var f=o=o.next;do r=t(r,f.action),f=f.next;while(f!==o);Be(r,e.memoizedState)||(_e=!0),e.memoizedState=r,e.baseQueue===null&&(e.baseState=r),n.lastRenderedState=r}return[r,l]}function qc(t,e,n){var l=vt,o=re(),r=ft;if(r){if(n===void 0)throw Error(p(407));n=n()}else n=e();var f=!Be((Rt||o).memoizedState,n);f&&(o.memoizedState=n,_e=!0),o=o.queue;var d=Gc.bind(null,l,o,t);if(ps(2048,8,d,[t]),o.getSnapshot!==e||f||oe!==null&&oe.memoizedState.tag&1){if(l.flags|=2048,xl(9,jo(),kc.bind(null,l,o,n,e),null),Yt===null)throw Error(p(349));r||($n&124)!==0||Pc(l,e,n)}return n}function Pc(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=vt.updateQueue,e===null?(e=au(),vt.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function kc(t,e,n,l){e.value=n,e.getSnapshot=l,Yc(e)&&Vc(t)}function Gc(t,e,n){return n(function(){Yc(e)&&Vc(t)})}function Yc(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!Be(t,n)}catch{return!0}}function Vc(t){var e=Vn(t,2);e!==null&&ai(e,t,2)}function ru(t){var e=Ye();if(typeof t=="function"){var n=t;if(t=n(),Pa){gi(!0);try{n()}finally{gi(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:hn,lastRenderedState:t},e}function Xc(t,e,n,l){return t.baseState=n,su(t,Rt,typeof l=="function"?l:hn)}function Qd(t,e,n,l,o){if(Uo(t))throw Error(p(485));if(t=e.action,t!==null){var r={payload:o,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){r.listeners.push(f)}};D.T!==null?n(!0):r.isTransition=!1,l(r),n=e.pending,n===null?(r.next=e.pending=r,Qc(e,r)):(r.next=n.next,e.pending=n.next=r)}}function Qc(t,e){var n=e.action,l=e.payload,o=t.state;if(e.isTransition){var r=D.T,f={};D.T=f;try{var d=n(o,l),g=D.S;g!==null&&g(f,d),Kc(t,e,d)}catch(E){uu(t,e,E)}finally{D.T=r}}else try{r=n(o,l),Kc(t,e,r)}catch(E){uu(t,e,E)}}function Kc(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){Jc(t,e,l)},function(l){return uu(t,e,l)}):Jc(t,e,n)}function Jc(t,e,n){e.status="fulfilled",e.value=n,Wc(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,Qc(t,n)))}function uu(t,e,n){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=n,Wc(e),e=e.next;while(e!==l)}t.action=null}function Wc(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Ic(t,e){return e}function $c(t,e){if(ft){var n=Yt.formState;if(n!==null){t:{var l=vt;if(ft){if(tt){e:{for(var o=tt,r=Wt;o.nodeType!==8;){if(!r){o=null;break e}if(o=Li(o.nextSibling),o===null){o=null;break e}}r=o.data,o=r==="F!"||r==="F"?o:null}if(o){tt=Li(o.nextSibling),l=o.data==="F!";break t}}Ge(l)}l=!1}l&&(e=n[0])}}return n=Ye(),n.memoizedState=n.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ic,lastRenderedState:e},n.queue=l,n=gf.bind(null,vt,l),l.dispatch=n,l=ru(!1),r=mu.bind(null,vt,!1,l.queue),l=Ye(),o={state:e,dispatch:null,action:t,pending:null},l.queue=o,n=Qd.bind(null,vt,o,r,n),o.dispatch=n,l.memoizedState=t,[e,n,!1]}function Fc(t){var e=re();return tf(e,Rt,t)}function tf(t,e,n){if(e=su(t,e,Ic)[0],t=Zo(hn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=_s(e)}catch(f){throw f===us?Ao:f}else l=e;e=re();var o=e.queue,r=o.dispatch;return n!==e.memoizedState&&(vt.flags|=2048,xl(9,jo(),Kd.bind(null,o,n),null)),[l,r,t]}function Kd(t,e){t.action=e}function ef(t){var e=re(),n=Rt;if(n!==null)return tf(e,n,t);re(),e=e.memoizedState,n=re();var l=n.queue.dispatch;return n.memoizedState=t,[e,l,!1]}function xl(t,e,n,l){return t={tag:t,create:n,deps:l,inst:e,next:null},e=vt.updateQueue,e===null&&(e=au(),vt.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(l=n.next,n.next=t,t.next=l,e.lastEffect=t),t}function jo(){return{destroy:void 0,resource:void 0}}function nf(){return re().memoizedState}function Ho(t,e,n,l){var o=Ye();l=l===void 0?null:l,vt.flags|=t,o.memoizedState=xl(1|e,jo(),n,l)}function ps(t,e,n,l){var o=re();l=l===void 0?null:l;var r=o.memoizedState.inst;Rt!==null&&l!==null&&Fr(l,Rt.memoizedState.deps)?o.memoizedState=xl(e,r,n,l):(vt.flags|=t,o.memoizedState=xl(1|e,r,n,l))}function af(t,e){Ho(8390656,8,t,e)}function lf(t,e){ps(2048,8,t,e)}function sf(t,e){return ps(4,2,t,e)}function of(t,e){return ps(4,4,t,e)}function rf(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function uf(t,e,n){n=n!=null?n.concat([t]):null,ps(4,4,rf.bind(null,e,t),n)}function cu(){}function cf(t,e){var n=re();e=e===void 0?null:e;var l=n.memoizedState;return e!==null&&Fr(e,l[1])?l[0]:(n.memoizedState=[t,e],t)}function ff(t,e){var n=re();e=e===void 0?null:e;var l=n.memoizedState;if(e!==null&&Fr(e,l[1]))return l[0];if(l=t(),Pa){gi(!0);try{t()}finally{gi(!1)}}return n.memoizedState=[l,e],l}function fu(t,e,n){return n===void 0||($n&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=mh(),vt.lanes|=t,la|=t,n)}function hf(t,e,n,l){return Be(n,e)?n:vl.current!==null?(t=fu(t,n,l),Be(t,e)||(_e=!0),t):($n&42)===0?(_e=!0,t.memoizedState=n):(t=mh(),vt.lanes|=t,la|=t,e)}function df(t,e,n,l,o){var r=Q.p;Q.p=r!==0&&8>r?r:8;var f=D.T,d={};D.T=d,mu(t,!1,e,n);try{var g=o(),E=D.S;if(E!==null&&E(d,g),g!==null&&typeof g=="object"&&typeof g.then=="function"){var Z=Yd(g,l);vs(t,e,Z,ni(t))}else vs(t,e,l,ni(t))}catch(H){vs(t,e,{then:function(){},status:"rejected",reason:H},ni())}finally{Q.p=r,D.T=f}}function Jd(){}function hu(t,e,n,l){if(t.tag!==5)throw Error(p(476));var o=mf(t).queue;df(t,o,e,G,n===null?Jd:function(){return _f(t),n(l)})}function mf(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:G,baseState:G,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:hn,lastRenderedState:G},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:hn,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function _f(t){var e=mf(t).next.queue;vs(t,e,{},ni())}function du(){return Ne(Bs)}function pf(){return re().memoizedState}function vf(){return re().memoizedState}function Wd(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=ni();t=Wn(n);var l=In(e,t,n);l!==null&&(ai(l,e,n),fs(l,e,n)),e={cache:Gr()},t.payload=e;return}e=e.return}}function Id(t,e,n){var l=ni();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Uo(t)?yf(e,n):(n=un(t,e,n,l),n!==null&&(ai(n,t,l),xf(n,e,l)))}function gf(t,e,n){var l=ni();vs(t,e,n,l)}function vs(t,e,n,l){var o={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Uo(t))yf(e,o);else{var r=t.alternate;if(t.lanes===0&&(r===null||r.lanes===0)&&(r=e.lastRenderedReducer,r!==null))try{var f=e.lastRenderedState,d=r(f,n);if(o.hasEagerState=!0,o.eagerState=d,Be(d,f))return Da(t,e,o,0),Yt===null&&Ca(),!1}catch{}finally{}if(n=un(t,e,o,l),n!==null)return ai(n,t,l),xf(n,e,l),!0}return!1}function mu(t,e,n,l){if(l={lane:2,revertLane:Xu(),action:l,hasEagerState:!1,eagerState:null,next:null},Uo(t)){if(e)throw Error(p(479))}else e=un(t,n,l,2),e!==null&&ai(e,t,2)}function Uo(t){var e=t.alternate;return t===vt||e!==null&&e===vt}function yf(t,e){gl=Do=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function xf(t,e,n){if((n&4194048)!==0){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,Is(t,n)}}var qo={readContext:Ne,use:Bo,useCallback:ie,useContext:ie,useEffect:ie,useImperativeHandle:ie,useLayoutEffect:ie,useInsertionEffect:ie,useMemo:ie,useReducer:ie,useRef:ie,useState:ie,useDebugValue:ie,useDeferredValue:ie,useTransition:ie,useSyncExternalStore:ie,useId:ie,useHostTransitionStatus:ie,useFormState:ie,useActionState:ie,useOptimistic:ie,useMemoCache:ie,useCacheRefresh:ie},bf={readContext:Ne,use:Bo,useCallback:function(t,e){return Ye().memoizedState=[t,e===void 0?null:e],t},useContext:Ne,useEffect:af,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,Ho(4194308,4,rf.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Ho(4194308,4,t,e)},useInsertionEffect:function(t,e){Ho(4,2,t,e)},useMemo:function(t,e){var n=Ye();e=e===void 0?null:e;var l=t();if(Pa){gi(!0);try{t()}finally{gi(!1)}}return n.memoizedState=[l,e],l},useReducer:function(t,e,n){var l=Ye();if(n!==void 0){var o=n(e);if(Pa){gi(!0);try{n(e)}finally{gi(!1)}}}else o=e;return l.memoizedState=l.baseState=o,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:o},l.queue=t,t=t.dispatch=Id.bind(null,vt,t),[l.memoizedState,t]},useRef:function(t){var e=Ye();return t={current:t},e.memoizedState=t},useState:function(t){t=ru(t);var e=t.queue,n=gf.bind(null,vt,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:cu,useDeferredValue:function(t,e){var n=Ye();return fu(n,t,e)},useTransition:function(){var t=ru(!1);return t=df.bind(null,vt,t.queue,!0,!1),Ye().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var l=vt,o=Ye();if(ft){if(n===void 0)throw Error(p(407));n=n()}else{if(n=e(),Yt===null)throw Error(p(349));(wt&124)!==0||Pc(l,e,n)}o.memoizedState=n;var r={value:n,getSnapshot:e};return o.queue=r,af(Gc.bind(null,l,r,t),[t]),l.flags|=2048,xl(9,jo(),kc.bind(null,l,r,n,e),null),n},useId:function(){var t=Ye(),e=Yt.identifierPrefix;if(ft){var n=v,l=h;n=(l&~(1<<32-De(l)-1)).toString(32)+n,e="«"+e+"R"+n,n=Ro++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=Vd++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:du,useFormState:$c,useActionState:$c,useOptimistic:function(t){var e=Ye();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=mu.bind(null,vt,!0,n),n.dispatch=e,[t,e]},useMemoCache:lu,useCacheRefresh:function(){return Ye().memoizedState=Wd.bind(null,vt)}},Sf={readContext:Ne,use:Bo,useCallback:cf,useContext:Ne,useEffect:lf,useImperativeHandle:uf,useInsertionEffect:sf,useLayoutEffect:of,useMemo:ff,useReducer:Zo,useRef:nf,useState:function(){return Zo(hn)},useDebugValue:cu,useDeferredValue:function(t,e){var n=re();return hf(n,Rt.memoizedState,t,e)},useTransition:function(){var t=Zo(hn)[0],e=re().memoizedState;return[typeof t=="boolean"?t:_s(t),e]},useSyncExternalStore:qc,useId:pf,useHostTransitionStatus:du,useFormState:Fc,useActionState:Fc,useOptimistic:function(t,e){var n=re();return Xc(n,Rt,t,e)},useMemoCache:lu,useCacheRefresh:vf},$d={readContext:Ne,use:Bo,useCallback:cf,useContext:Ne,useEffect:lf,useImperativeHandle:uf,useInsertionEffect:sf,useLayoutEffect:of,useMemo:ff,useReducer:ou,useRef:nf,useState:function(){return ou(hn)},useDebugValue:cu,useDeferredValue:function(t,e){var n=re();return Rt===null?fu(n,t,e):hf(n,Rt.memoizedState,t,e)},useTransition:function(){var t=ou(hn)[0],e=re().memoizedState;return[typeof t=="boolean"?t:_s(t),e]},useSyncExternalStore:qc,useId:pf,useHostTransitionStatus:du,useFormState:ef,useActionState:ef,useOptimistic:function(t,e){var n=re();return Rt!==null?Xc(n,Rt,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:lu,useCacheRefresh:vf},bl=null,gs=0;function Po(t){var e=gs;return gs+=1,bl===null&&(bl=[]),Cc(bl,t,e)}function ys(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function ko(t,e){throw e.$$typeof===W?Error(p(525)):(t=Object.prototype.toString.call(e),Error(p(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Tf(t){var e=t._init;return e(t._payload)}function wf(t){function e(S,b){if(t){var M=S.deletions;M===null?(S.deletions=[b],S.flags|=16):M.push(b)}}function n(S,b){if(!t)return null;for(;b!==null;)e(S,b),b=b.sibling;return null}function l(S){for(var b=new Map;S!==null;)S.key!==null?b.set(S.key,S):b.set(S.index,S),S=S.sibling;return b}function o(S,b){return S=fi(S,b),S.index=0,S.sibling=null,S}function r(S,b,M){return S.index=M,t?(M=S.alternate,M!==null?(M=M.index,M<b?(S.flags|=67108866,b):M):(S.flags|=67108866,b)):(S.flags|=1048576,b)}function f(S){return t&&S.alternate===null&&(S.flags|=67108866),S}function d(S,b,M,j){return b===null||b.tag!==6?(b=ss(M,S.mode,j),b.return=S,b):(b=o(b,M),b.return=S,b)}function g(S,b,M,j){var $=M.type;return $===st?Z(S,b,M.props.children,j,M.key):b!==null&&(b.elementType===$||typeof $=="object"&&$!==null&&$.$$typeof===Qt&&Tf($)===b.type)?(b=o(b,M.props),ys(b,M),b.return=S,b):(b=Ba(M.type,M.key,M.props,null,S.mode,j),ys(b,M),b.return=S,b)}function E(S,b,M,j){return b===null||b.tag!==4||b.stateNode.containerInfo!==M.containerInfo||b.stateNode.implementation!==M.implementation?(b=dl(M,S.mode,j),b.return=S,b):(b=o(b,M.children||[]),b.return=S,b)}function Z(S,b,M,j,$){return b===null||b.tag!==7?(b=ji(M,S.mode,j,$),b.return=S,b):(b=o(b,M),b.return=S,b)}function H(S,b,M){if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return b=ss(""+b,S.mode,M),b.return=S,b;if(typeof b=="object"&&b!==null){switch(b.$$typeof){case et:return M=Ba(b.type,b.key,b.props,null,S.mode,M),ys(M,b),M.return=S,M;case St:return b=dl(b,S.mode,M),b.return=S,b;case Qt:var j=b._init;return b=j(b._payload),H(S,b,M)}if(le(b)||Mt(b))return b=ji(b,S.mode,M,null),b.return=S,b;if(typeof b.then=="function")return H(S,Po(b),M);if(b.$$typeof===dt)return H(S,zo(S,b),M);ko(S,b)}return null}function O(S,b,M,j){var $=b!==null?b.key:null;if(typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint")return $!==null?null:d(S,b,""+M,j);if(typeof M=="object"&&M!==null){switch(M.$$typeof){case et:return M.key===$?g(S,b,M,j):null;case St:return M.key===$?E(S,b,M,j):null;case Qt:return $=M._init,M=$(M._payload),O(S,b,M,j)}if(le(M)||Mt(M))return $!==null?null:Z(S,b,M,j,null);if(typeof M.then=="function")return O(S,b,Po(M),j);if(M.$$typeof===dt)return O(S,b,zo(S,M),j);ko(S,M)}return null}function N(S,b,M,j,$){if(typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint")return S=S.get(M)||null,d(b,S,""+j,$);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case et:return S=S.get(j.key===null?M:j.key)||null,g(b,S,j,$);case St:return S=S.get(j.key===null?M:j.key)||null,E(b,S,j,$);case Qt:var yt=j._init;return j=yt(j._payload),N(S,b,M,j,$)}if(le(j)||Mt(j))return S=S.get(M)||null,Z(b,S,j,$,null);if(typeof j.then=="function")return N(S,b,M,Po(j),$);if(j.$$typeof===dt)return N(S,b,M,zo(b,j),$);ko(b,j)}return null}function ct(S,b,M,j){for(var $=null,yt=null,nt=b,ot=b=0,ve=null;nt!==null&&ot<M.length;ot++){nt.index>ot?(ve=nt,nt=null):ve=nt.sibling;var Ot=O(S,nt,M[ot],j);if(Ot===null){nt===null&&(nt=ve);break}t&&nt&&Ot.alternate===null&&e(S,nt),b=r(Ot,b,ot),yt===null?$=Ot:yt.sibling=Ot,yt=Ot,nt=ve}if(ot===M.length)return n(S,nt),ft&&w(S,ot),$;if(nt===null){for(;ot<M.length;ot++)nt=H(S,M[ot],j),nt!==null&&(b=r(nt,b,ot),yt===null?$=nt:yt.sibling=nt,yt=nt);return ft&&w(S,ot),$}for(nt=l(nt);ot<M.length;ot++)ve=N(nt,S,ot,M[ot],j),ve!==null&&(t&&ve.alternate!==null&&nt.delete(ve.key===null?ot:ve.key),b=r(ve,b,ot),yt===null?$=ve:yt.sibling=ve,yt=ve);return t&&nt.forEach(function(ma){return e(S,ma)}),ft&&w(S,ot),$}function lt(S,b,M,j){if(M==null)throw Error(p(151));for(var $=null,yt=null,nt=b,ot=b=0,ve=null,Ot=M.next();nt!==null&&!Ot.done;ot++,Ot=M.next()){nt.index>ot?(ve=nt,nt=null):ve=nt.sibling;var ma=O(S,nt,Ot.value,j);if(ma===null){nt===null&&(nt=ve);break}t&&nt&&ma.alternate===null&&e(S,nt),b=r(ma,b,ot),yt===null?$=ma:yt.sibling=ma,yt=ma,nt=ve}if(Ot.done)return n(S,nt),ft&&w(S,ot),$;if(nt===null){for(;!Ot.done;ot++,Ot=M.next())Ot=H(S,Ot.value,j),Ot!==null&&(b=r(Ot,b,ot),yt===null?$=Ot:yt.sibling=Ot,yt=Ot);return ft&&w(S,ot),$}for(nt=l(nt);!Ot.done;ot++,Ot=M.next())Ot=N(nt,S,ot,Ot.value,j),Ot!==null&&(t&&Ot.alternate!==null&&nt.delete(Ot.key===null?ot:Ot.key),b=r(Ot,b,ot),yt===null?$=Ot:yt.sibling=Ot,yt=Ot);return t&&nt.forEach(function(Fm){return e(S,Fm)}),ft&&w(S,ot),$}function Zt(S,b,M,j){if(typeof M=="object"&&M!==null&&M.type===st&&M.key===null&&(M=M.props.children),typeof M=="object"&&M!==null){switch(M.$$typeof){case et:t:{for(var $=M.key;b!==null;){if(b.key===$){if($=M.type,$===st){if(b.tag===7){n(S,b.sibling),j=o(b,M.props.children),j.return=S,S=j;break t}}else if(b.elementType===$||typeof $=="object"&&$!==null&&$.$$typeof===Qt&&Tf($)===b.type){n(S,b.sibling),j=o(b,M.props),ys(j,M),j.return=S,S=j;break t}n(S,b);break}else e(S,b);b=b.sibling}M.type===st?(j=ji(M.props.children,S.mode,j,M.key),j.return=S,S=j):(j=Ba(M.type,M.key,M.props,null,S.mode,j),ys(j,M),j.return=S,S=j)}return f(S);case St:t:{for($=M.key;b!==null;){if(b.key===$)if(b.tag===4&&b.stateNode.containerInfo===M.containerInfo&&b.stateNode.implementation===M.implementation){n(S,b.sibling),j=o(b,M.children||[]),j.return=S,S=j;break t}else{n(S,b);break}else e(S,b);b=b.sibling}j=dl(M,S.mode,j),j.return=S,S=j}return f(S);case Qt:return $=M._init,M=$(M._payload),Zt(S,b,M,j)}if(le(M))return ct(S,b,M,j);if(Mt(M)){if($=Mt(M),typeof $!="function")throw Error(p(150));return M=$.call(M),lt(S,b,M,j)}if(typeof M.then=="function")return Zt(S,b,Po(M),j);if(M.$$typeof===dt)return Zt(S,b,zo(S,M),j);ko(S,M)}return typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint"?(M=""+M,b!==null&&b.tag===6?(n(S,b.sibling),j=o(b,M),j.return=S,S=j):(n(S,b),j=ss(M,S.mode,j),j.return=S,S=j),f(S)):n(S,b)}return function(S,b,M,j){try{gs=0;var $=Zt(S,b,M,j);return bl=null,$}catch(nt){if(nt===us||nt===Ao)throw nt;var yt=Ue(29,nt,null,S.mode);return yt.lanes=j,yt.return=S,yt}finally{}}}var Sl=wf(!0),Mf=wf(!1),di=B(null),Ui=null;function Fn(t){var e=t.alternate;Y(ce,ce.current&1),Y(di,t),Ui===null&&(e===null||vl.current!==null||e.memoizedState!==null)&&(Ui=t)}function Ef(t){if(t.tag===22){if(Y(ce,ce.current),Y(di,t),Ui===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ui=t)}}else ta()}function ta(){Y(ce,ce.current),Y(di,di.current)}function dn(t){V(di),Ui===t&&(Ui=null),V(ce)}var ce=B(0);function Go(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||ac(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function _u(t,e,n,l){e=t.memoizedState,n=n(l,e),n=n==null?e:k({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var pu={enqueueSetState:function(t,e,n){t=t._reactInternals;var l=ni(),o=Wn(l);o.payload=e,n!=null&&(o.callback=n),e=In(t,o,l),e!==null&&(ai(e,t,l),fs(e,t,l))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var l=ni(),o=Wn(l);o.tag=1,o.payload=e,n!=null&&(o.callback=n),e=In(t,o,l),e!==null&&(ai(e,t,l),fs(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=ni(),l=Wn(n);l.tag=2,e!=null&&(l.callback=e),e=In(t,l,n),e!==null&&(ai(e,t,n),fs(e,t,n))}};function Lf(t,e,n,l,o,r,f){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,r,f):e.prototype&&e.prototype.isPureReactComponent?!Ze(n,l)||!Ze(o,r):!0}function zf(t,e,n,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,l),e.state!==t&&pu.enqueueReplaceState(e,e.state,null)}function ka(t,e){var n=e;if("ref"in e){n={};for(var l in e)l!=="ref"&&(n[l]=e[l])}if(t=t.defaultProps){n===e&&(n=k({},n));for(var o in t)n[o]===void 0&&(n[o]=t[o])}return n}var Yo=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Of(t){Yo(t)}function Af(t){console.error(t)}function Nf(t){Yo(t)}function Vo(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function Cf(t,e,n){try{var l=t.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(o){setTimeout(function(){throw o})}}function vu(t,e,n){return n=Wn(n),n.tag=3,n.payload={element:null},n.callback=function(){Vo(t,e)},n}function Df(t){return t=Wn(t),t.tag=3,t}function Rf(t,e,n,l){var o=n.type.getDerivedStateFromError;if(typeof o=="function"){var r=l.value;t.payload=function(){return o(r)},t.callback=function(){Cf(e,n,l)}}var f=n.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(t.callback=function(){Cf(e,n,l),typeof o!="function"&&(sa===null?sa=new Set([this]):sa.add(this));var d=l.stack;this.componentDidCatch(l.value,{componentStack:d!==null?d:""})})}function Fd(t,e,n,l,o){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=n.alternate,e!==null&&Ha(e,n,o,!0),n=di.current,n!==null){switch(n.tag){case 13:return Ui===null?Pu():n.alternate===null&&ee===0&&(ee=3),n.flags&=-257,n.flags|=65536,n.lanes=o,l===Xr?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([l]):e.add(l),Gu(t,l,o)),!1;case 22:return n.flags|=65536,l===Xr?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([l]):n.add(l)),Gu(t,l,o)),!1}throw Error(p(435,n.tag))}return Gu(t,l,o),Pu(),!1}if(ft)return e=di.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=o,l!==me&&(t=Error(p(422),{cause:l}),Kn(je(t,n)))):(l!==me&&(e=Error(p(423),{cause:l}),Kn(je(e,n))),t=t.current.alternate,t.flags|=65536,o&=-o,t.lanes|=o,l=je(l,n),o=vu(t.stateNode,l,o),Jr(t,o),ee!==4&&(ee=2)),!1;var r=Error(p(520),{cause:l});if(r=je(r,n),Es===null?Es=[r]:Es.push(r),ee!==4&&(ee=2),e===null)return!0;l=je(l,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=o&-o,n.lanes|=t,t=vu(n.stateNode,l,t),Jr(n,t),!1;case 1:if(e=n.type,r=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||r!==null&&typeof r.componentDidCatch=="function"&&(sa===null||!sa.has(r))))return n.flags|=65536,o&=-o,n.lanes|=o,o=Df(o),Rf(o,t,n,l),Jr(n,o),!1}n=n.return}while(n!==null);return!1}var Bf=Error(p(461)),_e=!1;function Se(t,e,n,l){e.child=t===null?Mf(e,null,n,l):Sl(e,t.child,n,l)}function Zf(t,e,n,l,o){n=n.render;var r=e.ref;if("ref"in l){var f={};for(var d in l)d!=="ref"&&(f[d]=l[d])}else f=l;return Ua(e),l=tu(t,e,n,f,r,o),d=eu(),t!==null&&!_e?(iu(t,e,o),mn(t,e,o)):(ft&&d&&U(e),e.flags|=1,Se(t,e,l,o),e.child)}function jf(t,e,n,l,o){if(t===null){var r=n.type;return typeof r=="function"&&!hl(r)&&r.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=r,Hf(t,e,r,l,o)):(t=Ba(n.type,null,l,e,e.mode,o),t.ref=e.ref,t.return=e,e.child=t)}if(r=t.child,!Mu(t,o)){var f=r.memoizedProps;if(n=n.compare,n=n!==null?n:Ze,n(f,l)&&t.ref===e.ref)return mn(t,e,o)}return e.flags|=1,t=fi(r,l),t.ref=e.ref,t.return=e,e.child=t}function Hf(t,e,n,l,o){if(t!==null){var r=t.memoizedProps;if(Ze(r,l)&&t.ref===e.ref)if(_e=!1,e.pendingProps=l=r,Mu(t,o))(t.flags&131072)!==0&&(_e=!0);else return e.lanes=t.lanes,mn(t,e,o)}return gu(t,e,n,l,o)}function Uf(t,e,n){var l=e.pendingProps,o=l.children,r=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if((e.flags&128)!==0){if(l=r!==null?r.baseLanes|n:n,t!==null){for(o=e.child=t.child,r=0;o!==null;)r=r|o.lanes|o.childLanes,o=o.sibling;e.childLanes=r&~l}else e.childLanes=0,e.child=null;return qf(t,e,l,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Oo(e,r!==null?r.cachePool:null),r!==null?jc(e,r):Ir(),Ef(e);else return e.lanes=e.childLanes=536870912,qf(t,e,r!==null?r.baseLanes|n:n,n)}else r!==null?(Oo(e,r.cachePool),jc(e,r),ta(),e.memoizedState=null):(t!==null&&Oo(e,null),Ir(),ta());return Se(t,e,o,n),e.child}function qf(t,e,n,l){var o=Vr();return o=o===null?null:{parent:ue._currentValue,pool:o},e.memoizedState={baseLanes:n,cachePool:o},t!==null&&Oo(e,null),Ir(),Ef(e),t!==null&&Ha(t,e,l,!0),null}function Xo(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(p(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function gu(t,e,n,l,o){return Ua(e),n=tu(t,e,n,l,void 0,o),l=eu(),t!==null&&!_e?(iu(t,e,o),mn(t,e,o)):(ft&&l&&U(e),e.flags|=1,Se(t,e,n,o),e.child)}function Pf(t,e,n,l,o,r){return Ua(e),e.updateQueue=null,n=Uc(e,l,n,o),Hc(t),l=eu(),t!==null&&!_e?(iu(t,e,r),mn(t,e,r)):(ft&&l&&U(e),e.flags|=1,Se(t,e,n,r),e.child)}function kf(t,e,n,l,o){if(Ua(e),e.stateNode===null){var r=cn,f=n.contextType;typeof f=="object"&&f!==null&&(r=Ne(f)),r=new n(l,r),e.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=pu,e.stateNode=r,r._reactInternals=e,r=e.stateNode,r.props=l,r.state=e.memoizedState,r.refs={},Qr(e),f=n.contextType,r.context=typeof f=="object"&&f!==null?Ne(f):cn,r.state=e.memoizedState,f=n.getDerivedStateFromProps,typeof f=="function"&&(_u(e,n,f,l),r.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(f=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),f!==r.state&&pu.enqueueReplaceState(r,r.state,null),ds(e,l,r,o),hs(),r.state=e.memoizedState),typeof r.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){r=e.stateNode;var d=e.memoizedProps,g=ka(n,d);r.props=g;var E=r.context,Z=n.contextType;f=cn,typeof Z=="object"&&Z!==null&&(f=Ne(Z));var H=n.getDerivedStateFromProps;Z=typeof H=="function"||typeof r.getSnapshotBeforeUpdate=="function",d=e.pendingProps!==d,Z||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(d||E!==f)&&zf(e,r,l,f),Jn=!1;var O=e.memoizedState;r.state=O,ds(e,l,r,o),hs(),E=e.memoizedState,d||O!==E||Jn?(typeof H=="function"&&(_u(e,n,H,l),E=e.memoizedState),(g=Jn||Lf(e,n,g,l,O,E,f))?(Z||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"&&(e.flags|=4194308)):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=E),r.props=l,r.state=E,r.context=f,l=g):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{r=e.stateNode,Kr(t,e),f=e.memoizedProps,Z=ka(n,f),r.props=Z,H=e.pendingProps,O=r.context,E=n.contextType,g=cn,typeof E=="object"&&E!==null&&(g=Ne(E)),d=n.getDerivedStateFromProps,(E=typeof d=="function"||typeof r.getSnapshotBeforeUpdate=="function")||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(f!==H||O!==g)&&zf(e,r,l,g),Jn=!1,O=e.memoizedState,r.state=O,ds(e,l,r,o),hs();var N=e.memoizedState;f!==H||O!==N||Jn||t!==null&&t.dependencies!==null&&Lo(t.dependencies)?(typeof d=="function"&&(_u(e,n,d,l),N=e.memoizedState),(Z=Jn||Lf(e,n,Z,l,O,N,g)||t!==null&&t.dependencies!==null&&Lo(t.dependencies))?(E||typeof r.UNSAFE_componentWillUpdate!="function"&&typeof r.componentWillUpdate!="function"||(typeof r.componentWillUpdate=="function"&&r.componentWillUpdate(l,N,g),typeof r.UNSAFE_componentWillUpdate=="function"&&r.UNSAFE_componentWillUpdate(l,N,g)),typeof r.componentDidUpdate=="function"&&(e.flags|=4),typeof r.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof r.componentDidUpdate!="function"||f===t.memoizedProps&&O===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&O===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=N),r.props=l,r.state=N,r.context=g,l=Z):(typeof r.componentDidUpdate!="function"||f===t.memoizedProps&&O===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&O===t.memoizedState||(e.flags|=1024),l=!1)}return r=l,Xo(t,e),l=(e.flags&128)!==0,r||l?(r=e.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:r.render(),e.flags|=1,t!==null&&l?(e.child=Sl(e,t.child,null,o),e.child=Sl(e,null,n,o)):Se(t,e,n,o),e.memoizedState=r.state,t=e.child):t=mn(t,e,o),t}function Gf(t,e,n,l){return Qn(),e.flags|=256,Se(t,e,n,l),e.child}var yu={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function xu(t){return{baseLanes:t,cachePool:Oc()}}function bu(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=mi),t}function Yf(t,e,n){var l=e.pendingProps,o=!1,r=(e.flags&128)!==0,f;if((f=r)||(f=t!==null&&t.memoizedState===null?!1:(ce.current&2)!==0),f&&(o=!0,e.flags&=-129),f=(e.flags&32)!==0,e.flags&=-33,t===null){if(ft){if(o?Fn(e):ta(),ft){var d=tt,g;if(g=d){t:{for(g=d,d=Wt;g.nodeType!==8;){if(!d){d=null;break t}if(g=Li(g.nextSibling),g===null){d=null;break t}}d=g}d!==null?(e.memoizedState={dehydrated:d,treeContext:c!==null?{id:h,overflow:v}:null,retryLane:536870912,hydrationErrors:null},g=Ue(18,null,null,0),g.stateNode=d,g.return=e,e.child=g,I=e,tt=null,g=!0):g=!1}g||Ge(e)}if(d=e.memoizedState,d!==null&&(d=d.dehydrated,d!==null))return ac(d)?e.lanes=32:e.lanes=536870912,null;dn(e)}return d=l.children,l=l.fallback,o?(ta(),o=e.mode,d=Qo({mode:"hidden",children:d},o),l=ji(l,o,n,null),d.return=e,l.return=e,d.sibling=l,e.child=d,o=e.child,o.memoizedState=xu(n),o.childLanes=bu(t,f,n),e.memoizedState=yu,l):(Fn(e),Su(e,d))}if(g=t.memoizedState,g!==null&&(d=g.dehydrated,d!==null)){if(r)e.flags&256?(Fn(e),e.flags&=-257,e=Tu(t,e,n)):e.memoizedState!==null?(ta(),e.child=t.child,e.flags|=128,e=null):(ta(),o=l.fallback,d=e.mode,l=Qo({mode:"visible",children:l.children},d),o=ji(o,d,n,null),o.flags|=2,l.return=e,o.return=e,l.sibling=o,e.child=l,Sl(e,t.child,null,n),l=e.child,l.memoizedState=xu(n),l.childLanes=bu(t,f,n),e.memoizedState=yu,e=o);else if(Fn(e),ac(d)){if(f=d.nextSibling&&d.nextSibling.dataset,f)var E=f.dgst;f=E,l=Error(p(419)),l.stack="",l.digest=f,Kn({value:l,source:null,stack:null}),e=Tu(t,e,n)}else if(_e||Ha(t,e,n,!1),f=(n&t.childLanes)!==0,_e||f){if(f=Yt,f!==null&&(l=n&-n,l=(l&42)!==0?1:ql(l),l=(l&(f.suspendedLanes|n))!==0?0:l,l!==0&&l!==g.retryLane))throw g.retryLane=l,Vn(t,l),ai(f,t,l),Bf;d.data==="$?"||Pu(),e=Tu(t,e,n)}else d.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=g.treeContext,tt=Li(d.nextSibling),I=e,ft=!0,Gt=null,Wt=!1,t!==null&&(s[u++]=h,s[u++]=v,s[u++]=c,h=t.id,v=t.overflow,c=e),e=Su(e,l.children),e.flags|=4096);return e}return o?(ta(),o=l.fallback,d=e.mode,g=t.child,E=g.sibling,l=fi(g,{mode:"hidden",children:l.children}),l.subtreeFlags=g.subtreeFlags&65011712,E!==null?o=fi(E,o):(o=ji(o,d,n,null),o.flags|=2),o.return=e,l.return=e,l.sibling=o,e.child=l,l=o,o=e.child,d=t.child.memoizedState,d===null?d=xu(n):(g=d.cachePool,g!==null?(E=ue._currentValue,g=g.parent!==E?{parent:E,pool:E}:g):g=Oc(),d={baseLanes:d.baseLanes|n,cachePool:g}),o.memoizedState=d,o.childLanes=bu(t,f,n),e.memoizedState=yu,l):(Fn(e),n=t.child,t=n.sibling,n=fi(n,{mode:"visible",children:l.children}),n.return=e,n.sibling=null,t!==null&&(f=e.deletions,f===null?(e.deletions=[t],e.flags|=16):f.push(t)),e.child=n,e.memoizedState=null,n)}function Su(t,e){return e=Qo({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Qo(t,e){return t=Ue(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Tu(t,e,n){return Sl(e,t.child,null,n),t=Su(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Vf(t,e,n){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),ja(t.return,e,n)}function wu(t,e,n,l,o){var r=t.memoizedState;r===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:o}:(r.isBackwards=e,r.rendering=null,r.renderingStartTime=0,r.last=l,r.tail=n,r.tailMode=o)}function Xf(t,e,n){var l=e.pendingProps,o=l.revealOrder,r=l.tail;if(Se(t,e,l.children,n),l=ce.current,(l&2)!==0)l=l&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Vf(t,n,e);else if(t.tag===19)Vf(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(Y(ce,l),o){case"forwards":for(n=e.child,o=null;n!==null;)t=n.alternate,t!==null&&Go(t)===null&&(o=n),n=n.sibling;n=o,n===null?(o=e.child,e.child=null):(o=n.sibling,n.sibling=null),wu(e,!1,o,n,r);break;case"backwards":for(n=null,o=e.child,e.child=null;o!==null;){if(t=o.alternate,t!==null&&Go(t)===null){e.child=o;break}t=o.sibling,o.sibling=n,n=o,o=t}wu(e,!0,n,null,r);break;case"together":wu(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function mn(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),la|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(Ha(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(p(153));if(e.child!==null){for(t=e.child,n=fi(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=fi(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Mu(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Lo(t)))}function tm(t,e,n){switch(e.tag){case 3:Et(e,e.stateNode.containerInfo),Hi(e,ue,t.memoizedState.cache),Qn();break;case 27:case 5:_a(e);break;case 4:Et(e,e.stateNode.containerInfo);break;case 10:Hi(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(Fn(e),e.flags|=128,null):(n&e.child.childLanes)!==0?Yf(t,e,n):(Fn(e),t=mn(t,e,n),t!==null?t.sibling:null);Fn(e);break;case 19:var o=(t.flags&128)!==0;if(l=(n&e.childLanes)!==0,l||(Ha(t,e,n,!1),l=(n&e.childLanes)!==0),o){if(l)return Xf(t,e,n);e.flags|=128}if(o=e.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),Y(ce,ce.current),l)break;return null;case 22:case 23:return e.lanes=0,Uf(t,e,n);case 24:Hi(e,ue,t.memoizedState.cache)}return mn(t,e,n)}function Qf(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)_e=!0;else{if(!Mu(t,n)&&(e.flags&128)===0)return _e=!1,tm(t,e,n);_e=(t.flags&131072)!==0}else _e=!1,ft&&(e.flags&1048576)!==0&&C(e,a,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,o=l._init;if(l=o(l._payload),e.type=l,typeof l=="function")hl(l)?(t=ka(l,t),e.tag=1,e=kf(null,e,l,t,n)):(e.tag=0,e=gu(null,e,l,t,n));else{if(l!=null){if(o=l.$$typeof,o===Lt){e.tag=11,e=Zf(null,e,l,t,n);break t}else if(o===Ht){e.tag=14,e=jf(null,e,l,t,n);break t}}throw e=fe(l)||l,Error(p(306,e,""))}}return e;case 0:return gu(t,e,e.type,e.pendingProps,n);case 1:return l=e.type,o=ka(l,e.pendingProps),kf(t,e,l,o,n);case 3:t:{if(Et(e,e.stateNode.containerInfo),t===null)throw Error(p(387));l=e.pendingProps;var r=e.memoizedState;o=r.element,Kr(t,e),ds(e,l,null,n);var f=e.memoizedState;if(l=f.cache,Hi(e,ue,l),l!==r.cache&&ml(e,[ue],n,!0),hs(),l=f.element,r.isDehydrated)if(r={element:l,isDehydrated:!1,cache:f.cache},e.updateQueue.baseState=r,e.memoizedState=r,e.flags&256){e=Gf(t,e,l,n);break t}else if(l!==o){o=je(Error(p(424)),e),Kn(o),e=Gf(t,e,l,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(tt=Li(t.firstChild),I=e,ft=!0,Gt=null,Wt=!0,n=Mf(e,null,l,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Qn(),l===o){e=mn(t,e,n);break t}Se(t,e,l,n)}e=e.child}return e;case 26:return Xo(t,e),t===null?(n=Ih(e.type,null,e.pendingProps,null))?e.memoizedState=n:ft||(n=e.type,t=e.pendingProps,l=or(X.current).createElement(n),l[he]=e,l[ze]=t,we(l,n,t),se(l),e.stateNode=l):e.memoizedState=Ih(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return _a(e),t===null&&ft&&(l=e.stateNode=Kh(e.type,e.pendingProps,X.current),I=e,Wt=!0,o=tt,ua(e.type)?(lc=o,tt=Li(l.firstChild)):tt=o),Se(t,e,e.pendingProps.children,n),Xo(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&ft&&((o=l=tt)&&(l=zm(l,e.type,e.pendingProps,Wt),l!==null?(e.stateNode=l,I=e,tt=Li(l.firstChild),Wt=!1,o=!0):o=!1),o||Ge(e)),_a(e),o=e.type,r=e.pendingProps,f=t!==null?t.memoizedProps:null,l=r.children,ec(o,r)?l=null:f!==null&&ec(o,f)&&(e.flags|=32),e.memoizedState!==null&&(o=tu(t,e,Xd,null,null,n),Bs._currentValue=o),Xo(t,e),Se(t,e,l,n),e.child;case 6:return t===null&&ft&&((t=n=tt)&&(n=Om(n,e.pendingProps,Wt),n!==null?(e.stateNode=n,I=e,tt=null,t=!0):t=!1),t||Ge(e)),null;case 13:return Yf(t,e,n);case 4:return Et(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=Sl(e,null,l,n):Se(t,e,l,n),e.child;case 11:return Zf(t,e,e.type,e.pendingProps,n);case 7:return Se(t,e,e.pendingProps,n),e.child;case 8:return Se(t,e,e.pendingProps.children,n),e.child;case 12:return Se(t,e,e.pendingProps.children,n),e.child;case 10:return l=e.pendingProps,Hi(e,e.type,l.value),Se(t,e,l.children,n),e.child;case 9:return o=e.type._context,l=e.pendingProps.children,Ua(e),o=Ne(o),l=l(o),e.flags|=1,Se(t,e,l,n),e.child;case 14:return jf(t,e,e.type,e.pendingProps,n);case 15:return Hf(t,e,e.type,e.pendingProps,n);case 19:return Xf(t,e,n);case 31:return l=e.pendingProps,n=e.mode,l={mode:l.mode,children:l.children},t===null?(n=Qo(l,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=fi(t.child,l),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return Uf(t,e,n);case 24:return Ua(e),l=Ne(ue),t===null?(o=Vr(),o===null&&(o=Yt,r=Gr(),o.pooledCache=r,r.refCount++,r!==null&&(o.pooledCacheLanes|=n),o=r),e.memoizedState={parent:l,cache:o},Qr(e),Hi(e,ue,o)):((t.lanes&n)!==0&&(Kr(t,e),ds(e,null,null,n),hs()),o=t.memoizedState,r=e.memoizedState,o.parent!==l?(o={parent:l,cache:l},e.memoizedState=o,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=o),Hi(e,ue,l)):(l=r.cache,Hi(e,ue,l),l!==o.cache&&ml(e,[ue],n,!0))),Se(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(p(156,e.tag))}function _n(t){t.flags|=4}function Kf(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!id(e)){if(e=di.current,e!==null&&((wt&4194048)===wt?Ui!==null:(wt&62914560)!==wt&&(wt&536870912)===0||e!==Ui))throw cs=Xr,Ac;t.flags|=8192}}function Ko(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Ul():536870912,t.lanes|=e,El|=e)}function xs(t,e){if(!ft)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function It(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,l=0;if(e)for(var o=t.child;o!==null;)n|=o.lanes|o.childLanes,l|=o.subtreeFlags&65011712,l|=o.flags&65011712,o.return=t,o=o.sibling;else for(o=t.child;o!==null;)n|=o.lanes|o.childLanes,l|=o.subtreeFlags,l|=o.flags,o.return=t,o=o.sibling;return t.subtreeFlags|=l,t.childLanes=n,e}function em(t,e,n){var l=e.pendingProps;switch(K(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return It(e),null;case 1:return It(e),null;case 3:return n=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Mi(ue),vi(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(Za(e)?_n(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Eo())),It(e),null;case 26:return n=e.memoizedState,t===null?(_n(e),n!==null?(It(e),Kf(e,n)):(It(e),e.flags&=-16777217)):n?n!==t.memoizedState?(_n(e),It(e),Kf(e,n)):(It(e),e.flags&=-16777217):(t.memoizedProps!==l&&_n(e),It(e),e.flags&=-16777217),null;case 27:bn(e),n=X.current;var o=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&_n(e);else{if(!l){if(e.stateNode===null)throw Error(p(166));return It(e),null}t=J.current,Za(e)?wo(e):(t=Kh(o,l,n),e.stateNode=t,_n(e))}return It(e),null;case 5:if(bn(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&_n(e);else{if(!l){if(e.stateNode===null)throw Error(p(166));return It(e),null}if(t=J.current,Za(e))wo(e);else{switch(o=or(X.current),t){case 1:t=o.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=o.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=o.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?o.createElement("select",{is:l.is}):o.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?o.createElement(n,{is:l.is}):o.createElement(n)}}t[he]=e,t[ze]=l;t:for(o=e.child;o!==null;){if(o.tag===5||o.tag===6)t.appendChild(o.stateNode);else if(o.tag!==4&&o.tag!==27&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===e)break t;for(;o.sibling===null;){if(o.return===null||o.return===e)break t;o=o.return}o.sibling.return=o.return,o=o.sibling}e.stateNode=t;t:switch(we(t,n,l),n){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&_n(e)}}return It(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&_n(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(p(166));if(t=X.current,Za(e)){if(t=e.stateNode,n=e.memoizedProps,l=null,o=I,o!==null)switch(o.tag){case 27:case 5:l=o.memoizedProps}t[he]=e,t=!!(t.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||Ph(t.nodeValue,n)),t||Ge(e)}else t=or(t).createTextNode(l),t[he]=e,e.stateNode=t}return It(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(o=Za(e),l!==null&&l.dehydrated!==null){if(t===null){if(!o)throw Error(p(318));if(o=e.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(p(317));o[he]=e}else Qn(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;It(e),o=!1}else o=Eo(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=o),o=!0;if(!o)return e.flags&256?(dn(e),e):(dn(e),null)}if(dn(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=l!==null,t=t!==null&&t.memoizedState!==null,n){l=e.child,o=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(o=l.alternate.memoizedState.cachePool.pool);var r=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(r=l.memoizedState.cachePool.pool),r!==o&&(l.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),Ko(e,e.updateQueue),It(e),null;case 4:return vi(),t===null&&Wu(e.stateNode.containerInfo),It(e),null;case 10:return Mi(e.type),It(e),null;case 19:if(V(ce),o=e.memoizedState,o===null)return It(e),null;if(l=(e.flags&128)!==0,r=o.rendering,r===null)if(l)xs(o,!1);else{if(ee!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(r=Go(t),r!==null){for(e.flags|=128,xs(o,!1),t=r.updateQueue,e.updateQueue=t,Ko(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)ls(n,t),n=n.sibling;return Y(ce,ce.current&1|2),e.child}t=t.sibling}o.tail!==null&&qe()>Io&&(e.flags|=128,l=!0,xs(o,!1),e.lanes=4194304)}else{if(!l)if(t=Go(r),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,Ko(e,t),xs(o,!0),o.tail===null&&o.tailMode==="hidden"&&!r.alternate&&!ft)return It(e),null}else 2*qe()-o.renderingStartTime>Io&&n!==536870912&&(e.flags|=128,l=!0,xs(o,!1),e.lanes=4194304);o.isBackwards?(r.sibling=e.child,e.child=r):(t=o.last,t!==null?t.sibling=r:e.child=r,o.last=r)}return o.tail!==null?(e=o.tail,o.rendering=e,o.tail=e.sibling,o.renderingStartTime=qe(),e.sibling=null,t=ce.current,Y(ce,l?t&1|2:t&1),e):(It(e),null);case 22:case 23:return dn(e),$r(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?(n&536870912)!==0&&(e.flags&128)===0&&(It(e),e.subtreeFlags&6&&(e.flags|=8192)):It(e),n=e.updateQueue,n!==null&&Ko(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==n&&(e.flags|=2048),t!==null&&V(qa),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),Mi(ue),It(e),null;case 25:return null;case 30:return null}throw Error(p(156,e.tag))}function im(t,e){switch(K(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Mi(ue),vi(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return bn(e),null;case 13:if(dn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(p(340));Qn()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return V(ce),null;case 4:return vi(),null;case 10:return Mi(e.type),null;case 22:case 23:return dn(e),$r(),t!==null&&V(qa),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Mi(ue),null;case 25:return null;default:return null}}function Jf(t,e){switch(K(e),e.tag){case 3:Mi(ue),vi();break;case 26:case 27:case 5:bn(e);break;case 4:vi();break;case 13:dn(e);break;case 19:V(ce);break;case 10:Mi(e.type);break;case 22:case 23:dn(e),$r(),t!==null&&V(qa);break;case 24:Mi(ue)}}function bs(t,e){try{var n=e.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var o=l.next;n=o;do{if((n.tag&t)===t){l=void 0;var r=n.create,f=n.inst;l=r(),f.destroy=l}n=n.next}while(n!==o)}}catch(d){Ut(e,e.return,d)}}function ea(t,e,n){try{var l=e.updateQueue,o=l!==null?l.lastEffect:null;if(o!==null){var r=o.next;l=r;do{if((l.tag&t)===t){var f=l.inst,d=f.destroy;if(d!==void 0){f.destroy=void 0,o=e;var g=n,E=d;try{E()}catch(Z){Ut(o,g,Z)}}}l=l.next}while(l!==r)}}catch(Z){Ut(e,e.return,Z)}}function Wf(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{Zc(e,n)}catch(l){Ut(t,t.return,l)}}}function If(t,e,n){n.props=ka(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(l){Ut(t,e,l)}}function Ss(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof n=="function"?t.refCleanup=n(l):n.current=l}}catch(o){Ut(t,e,o)}}function qi(t,e){var n=t.ref,l=t.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(o){Ut(t,e,o)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(o){Ut(t,e,o)}else n.current=null}function $f(t){var e=t.type,n=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break t;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(o){Ut(t,t.return,o)}}function Eu(t,e,n){try{var l=t.stateNode;Tm(l,t.type,n,e),l[ze]=e}catch(o){Ut(t,t.return,o)}}function Ff(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&ua(t.type)||t.tag===4}function Lu(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Ff(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&ua(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function zu(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=sr));else if(l!==4&&(l===27&&ua(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(zu(t,e,n),t=t.sibling;t!==null;)zu(t,e,n),t=t.sibling}function Jo(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(l!==4&&(l===27&&ua(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(Jo(t,e,n),t=t.sibling;t!==null;)Jo(t,e,n),t=t.sibling}function th(t){var e=t.stateNode,n=t.memoizedProps;try{for(var l=t.type,o=e.attributes;o.length;)e.removeAttributeNode(o[0]);we(e,l,n),e[he]=t,e[ze]=n}catch(r){Ut(t,t.return,r)}}var pn=!1,ne=!1,Ou=!1,eh=typeof WeakSet=="function"?WeakSet:Set,pe=null;function nm(t,e){if(t=t.containerInfo,Fu=dr,t=Oa(t),Aa(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var o=l.anchorOffset,r=l.focusNode;l=l.focusOffset;try{n.nodeType,r.nodeType}catch{n=null;break t}var f=0,d=-1,g=-1,E=0,Z=0,H=t,O=null;e:for(;;){for(var N;H!==n||o!==0&&H.nodeType!==3||(d=f+o),H!==r||l!==0&&H.nodeType!==3||(g=f+l),H.nodeType===3&&(f+=H.nodeValue.length),(N=H.firstChild)!==null;)O=H,H=N;for(;;){if(H===t)break e;if(O===n&&++E===o&&(d=f),O===r&&++Z===l&&(g=f),(N=H.nextSibling)!==null)break;H=O,O=H.parentNode}H=N}n=d===-1||g===-1?null:{start:d,end:g}}else n=null}n=n||{start:0,end:0}}else n=null;for(tc={focusedElem:t,selectionRange:n},dr=!1,pe=e;pe!==null;)if(e=pe,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,pe=t;else for(;pe!==null;){switch(e=pe,r=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&r!==null){t=void 0,n=e,o=r.memoizedProps,r=r.memoizedState,l=n.stateNode;try{var ct=ka(n.type,o,n.elementType===n.type);t=l.getSnapshotBeforeUpdate(ct,r),l.__reactInternalSnapshotBeforeUpdate=t}catch(lt){Ut(n,n.return,lt)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)nc(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":nc(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(p(163))}if(t=e.sibling,t!==null){t.return=e.return,pe=t;break}pe=e.return}}function ih(t,e,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:ia(t,n),l&4&&bs(5,n);break;case 1:if(ia(t,n),l&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(f){Ut(n,n.return,f)}else{var o=ka(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(o,e,t.__reactInternalSnapshotBeforeUpdate)}catch(f){Ut(n,n.return,f)}}l&64&&Wf(n),l&512&&Ss(n,n.return);break;case 3:if(ia(t,n),l&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{Zc(t,e)}catch(f){Ut(n,n.return,f)}}break;case 27:e===null&&l&4&&th(n);case 26:case 5:ia(t,n),e===null&&l&4&&$f(n),l&512&&Ss(n,n.return);break;case 12:ia(t,n);break;case 13:ia(t,n),l&4&&lh(t,n),l&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=hm.bind(null,n),Am(t,n))));break;case 22:if(l=n.memoizedState!==null||pn,!l){e=e!==null&&e.memoizedState!==null||ne,o=pn;var r=ne;pn=l,(ne=e)&&!r?na(t,n,(n.subtreeFlags&8772)!==0):ia(t,n),pn=o,ne=r}break;case 30:break;default:ia(t,n)}}function nh(t){var e=t.alternate;e!==null&&(t.alternate=null,nh(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Ja(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Xt=null,Ve=!1;function vn(t,e,n){for(n=n.child;n!==null;)ah(t,e,n),n=n.sibling}function ah(t,e,n){if(Le&&typeof Le.onCommitFiberUnmount=="function")try{Le.onCommitFiberUnmount(Yi,n)}catch{}switch(n.tag){case 26:ne||qi(n,e),vn(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:ne||qi(n,e);var l=Xt,o=Ve;ua(n.type)&&(Xt=n.stateNode,Ve=!1),vn(t,e,n),Ns(n.stateNode),Xt=l,Ve=o;break;case 5:ne||qi(n,e);case 6:if(l=Xt,o=Ve,Xt=null,vn(t,e,n),Xt=l,Ve=o,Xt!==null)if(Ve)try{(Xt.nodeType===9?Xt.body:Xt.nodeName==="HTML"?Xt.ownerDocument.body:Xt).removeChild(n.stateNode)}catch(r){Ut(n,e,r)}else try{Xt.removeChild(n.stateNode)}catch(r){Ut(n,e,r)}break;case 18:Xt!==null&&(Ve?(t=Xt,Xh(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),Us(t)):Xh(Xt,n.stateNode));break;case 4:l=Xt,o=Ve,Xt=n.stateNode.containerInfo,Ve=!0,vn(t,e,n),Xt=l,Ve=o;break;case 0:case 11:case 14:case 15:ne||ea(2,n,e),ne||ea(4,n,e),vn(t,e,n);break;case 1:ne||(qi(n,e),l=n.stateNode,typeof l.componentWillUnmount=="function"&&If(n,e,l)),vn(t,e,n);break;case 21:vn(t,e,n);break;case 22:ne=(l=ne)||n.memoizedState!==null,vn(t,e,n),ne=l;break;default:vn(t,e,n)}}function lh(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Us(t)}catch(n){Ut(e,e.return,n)}}function am(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new eh),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new eh),e;default:throw Error(p(435,t.tag))}}function Au(t,e){var n=am(t);e.forEach(function(l){var o=dm.bind(null,t,l);n.has(l)||(n.add(l),l.then(o,o))})}function ti(t,e){var n=e.deletions;if(n!==null)for(var l=0;l<n.length;l++){var o=n[l],r=t,f=e,d=f;t:for(;d!==null;){switch(d.tag){case 27:if(ua(d.type)){Xt=d.stateNode,Ve=!1;break t}break;case 5:Xt=d.stateNode,Ve=!1;break t;case 3:case 4:Xt=d.stateNode.containerInfo,Ve=!0;break t}d=d.return}if(Xt===null)throw Error(p(160));ah(r,f,o),Xt=null,Ve=!1,r=o.alternate,r!==null&&(r.return=null),o.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)sh(e,t),e=e.sibling}var Ei=null;function sh(t,e){var n=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:ti(e,t),ei(t),l&4&&(ea(3,t,t.return),bs(3,t),ea(5,t,t.return));break;case 1:ti(e,t),ei(t),l&512&&(ne||n===null||qi(n,n.return)),l&64&&pn&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var o=Ei;if(ti(e,t),ei(t),l&512&&(ne||n===null||qi(n,n.return)),l&4){var r=n!==null?n.memoizedState:null;if(l=t.memoizedState,n===null)if(l===null)if(t.stateNode===null){t:{l=t.type,n=t.memoizedProps,o=o.ownerDocument||o;e:switch(l){case"title":r=o.getElementsByTagName("title")[0],(!r||r[En]||r[he]||r.namespaceURI==="http://www.w3.org/2000/svg"||r.hasAttribute("itemprop"))&&(r=o.createElement(l),o.head.insertBefore(r,o.querySelector("head > title"))),we(r,l,n),r[he]=t,se(r),l=r;break t;case"link":var f=td("link","href",o).get(l+(n.href||""));if(f){for(var d=0;d<f.length;d++)if(r=f[d],r.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&r.getAttribute("rel")===(n.rel==null?null:n.rel)&&r.getAttribute("title")===(n.title==null?null:n.title)&&r.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){f.splice(d,1);break e}}r=o.createElement(l),we(r,l,n),o.head.appendChild(r);break;case"meta":if(f=td("meta","content",o).get(l+(n.content||""))){for(d=0;d<f.length;d++)if(r=f[d],r.getAttribute("content")===(n.content==null?null:""+n.content)&&r.getAttribute("name")===(n.name==null?null:n.name)&&r.getAttribute("property")===(n.property==null?null:n.property)&&r.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&r.getAttribute("charset")===(n.charSet==null?null:n.charSet)){f.splice(d,1);break e}}r=o.createElement(l),we(r,l,n),o.head.appendChild(r);break;default:throw Error(p(468,l))}r[he]=t,se(r),l=r}t.stateNode=l}else ed(o,t.type,t.stateNode);else t.stateNode=Fh(o,l,t.memoizedProps);else r!==l?(r===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):r.count--,l===null?ed(o,t.type,t.stateNode):Fh(o,l,t.memoizedProps)):l===null&&t.stateNode!==null&&Eu(t,t.memoizedProps,n.memoizedProps)}break;case 27:ti(e,t),ei(t),l&512&&(ne||n===null||qi(n,n.return)),n!==null&&l&4&&Eu(t,t.memoizedProps,n.memoizedProps);break;case 5:if(ti(e,t),ei(t),l&512&&(ne||n===null||qi(n,n.return)),t.flags&32){o=t.stateNode;try{si(o,"")}catch(N){Ut(t,t.return,N)}}l&4&&t.stateNode!=null&&(o=t.memoizedProps,Eu(t,o,n!==null?n.memoizedProps:o)),l&1024&&(Ou=!0);break;case 6:if(ti(e,t),ei(t),l&4){if(t.stateNode===null)throw Error(p(162));l=t.memoizedProps,n=t.stateNode;try{n.nodeValue=l}catch(N){Ut(t,t.return,N)}}break;case 3:if(cr=null,o=Ei,Ei=rr(e.containerInfo),ti(e,t),Ei=o,ei(t),l&4&&n!==null&&n.memoizedState.isDehydrated)try{Us(e.containerInfo)}catch(N){Ut(t,t.return,N)}Ou&&(Ou=!1,oh(t));break;case 4:l=Ei,Ei=rr(t.stateNode.containerInfo),ti(e,t),ei(t),Ei=l;break;case 12:ti(e,t),ei(t);break;case 13:ti(e,t),ei(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Zu=qe()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,Au(t,l)));break;case 22:o=t.memoizedState!==null;var g=n!==null&&n.memoizedState!==null,E=pn,Z=ne;if(pn=E||o,ne=Z||g,ti(e,t),ne=Z,pn=E,ei(t),l&8192)t:for(e=t.stateNode,e._visibility=o?e._visibility&-2:e._visibility|1,o&&(n===null||g||pn||ne||Ga(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){g=n=e;try{if(r=g.stateNode,o)f=r.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{d=g.stateNode;var H=g.memoizedProps.style,O=H!=null&&H.hasOwnProperty("display")?H.display:null;d.style.display=O==null||typeof O=="boolean"?"":(""+O).trim()}}catch(N){Ut(g,g.return,N)}}}else if(e.tag===6){if(n===null){g=e;try{g.stateNode.nodeValue=o?"":g.memoizedProps}catch(N){Ut(g,g.return,N)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,Au(t,n))));break;case 19:ti(e,t),ei(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,Au(t,l)));break;case 30:break;case 21:break;default:ti(e,t),ei(t)}}function ei(t){var e=t.flags;if(e&2){try{for(var n,l=t.return;l!==null;){if(Ff(l)){n=l;break}l=l.return}if(n==null)throw Error(p(160));switch(n.tag){case 27:var o=n.stateNode,r=Lu(t);Jo(t,r,o);break;case 5:var f=n.stateNode;n.flags&32&&(si(f,""),n.flags&=-33);var d=Lu(t);Jo(t,d,f);break;case 3:case 4:var g=n.stateNode.containerInfo,E=Lu(t);zu(t,E,g);break;default:throw Error(p(161))}}catch(Z){Ut(t,t.return,Z)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function oh(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;oh(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function ia(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)ih(t,e.alternate,e),e=e.sibling}function Ga(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:ea(4,e,e.return),Ga(e);break;case 1:qi(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&If(e,e.return,n),Ga(e);break;case 27:Ns(e.stateNode);case 26:case 5:qi(e,e.return),Ga(e);break;case 22:e.memoizedState===null&&Ga(e);break;case 30:Ga(e);break;default:Ga(e)}t=t.sibling}}function na(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,o=t,r=e,f=r.flags;switch(r.tag){case 0:case 11:case 15:na(o,r,n),bs(4,r);break;case 1:if(na(o,r,n),l=r,o=l.stateNode,typeof o.componentDidMount=="function")try{o.componentDidMount()}catch(E){Ut(l,l.return,E)}if(l=r,o=l.updateQueue,o!==null){var d=l.stateNode;try{var g=o.shared.hiddenCallbacks;if(g!==null)for(o.shared.hiddenCallbacks=null,o=0;o<g.length;o++)Bc(g[o],d)}catch(E){Ut(l,l.return,E)}}n&&f&64&&Wf(r),Ss(r,r.return);break;case 27:th(r);case 26:case 5:na(o,r,n),n&&l===null&&f&4&&$f(r),Ss(r,r.return);break;case 12:na(o,r,n);break;case 13:na(o,r,n),n&&f&4&&lh(o,r);break;case 22:r.memoizedState===null&&na(o,r,n),Ss(r,r.return);break;case 30:break;default:na(o,r,n)}e=e.sibling}}function Nu(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&os(n))}function Cu(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&os(t))}function Pi(t,e,n,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)rh(t,e,n,l),e=e.sibling}function rh(t,e,n,l){var o=e.flags;switch(e.tag){case 0:case 11:case 15:Pi(t,e,n,l),o&2048&&bs(9,e);break;case 1:Pi(t,e,n,l);break;case 3:Pi(t,e,n,l),o&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&os(t)));break;case 12:if(o&2048){Pi(t,e,n,l),t=e.stateNode;try{var r=e.memoizedProps,f=r.id,d=r.onPostCommit;typeof d=="function"&&d(f,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(g){Ut(e,e.return,g)}}else Pi(t,e,n,l);break;case 13:Pi(t,e,n,l);break;case 23:break;case 22:r=e.stateNode,f=e.alternate,e.memoizedState!==null?r._visibility&2?Pi(t,e,n,l):Ts(t,e):r._visibility&2?Pi(t,e,n,l):(r._visibility|=2,Tl(t,e,n,l,(e.subtreeFlags&10256)!==0)),o&2048&&Nu(f,e);break;case 24:Pi(t,e,n,l),o&2048&&Cu(e.alternate,e);break;default:Pi(t,e,n,l)}}function Tl(t,e,n,l,o){for(o=o&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var r=t,f=e,d=n,g=l,E=f.flags;switch(f.tag){case 0:case 11:case 15:Tl(r,f,d,g,o),bs(8,f);break;case 23:break;case 22:var Z=f.stateNode;f.memoizedState!==null?Z._visibility&2?Tl(r,f,d,g,o):Ts(r,f):(Z._visibility|=2,Tl(r,f,d,g,o)),o&&E&2048&&Nu(f.alternate,f);break;case 24:Tl(r,f,d,g,o),o&&E&2048&&Cu(f.alternate,f);break;default:Tl(r,f,d,g,o)}e=e.sibling}}function Ts(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,l=e,o=l.flags;switch(l.tag){case 22:Ts(n,l),o&2048&&Nu(l.alternate,l);break;case 24:Ts(n,l),o&2048&&Cu(l.alternate,l);break;default:Ts(n,l)}e=e.sibling}}var ws=8192;function wl(t){if(t.subtreeFlags&ws)for(t=t.child;t!==null;)uh(t),t=t.sibling}function uh(t){switch(t.tag){case 26:wl(t),t.flags&ws&&t.memoizedState!==null&&Gm(Ei,t.memoizedState,t.memoizedProps);break;case 5:wl(t);break;case 3:case 4:var e=Ei;Ei=rr(t.stateNode.containerInfo),wl(t),Ei=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=ws,ws=16777216,wl(t),ws=e):wl(t));break;default:wl(t)}}function ch(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Ms(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];pe=l,hh(l,t)}ch(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)fh(t),t=t.sibling}function fh(t){switch(t.tag){case 0:case 11:case 15:Ms(t),t.flags&2048&&ea(9,t,t.return);break;case 3:Ms(t);break;case 12:Ms(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Wo(t)):Ms(t);break;default:Ms(t)}}function Wo(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];pe=l,hh(l,t)}ch(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:ea(8,e,e.return),Wo(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,Wo(e));break;default:Wo(e)}t=t.sibling}}function hh(t,e){for(;pe!==null;){var n=pe;switch(n.tag){case 0:case 11:case 15:ea(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:os(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,pe=l;else t:for(n=t;pe!==null;){l=pe;var o=l.sibling,r=l.return;if(nh(l),l===n){pe=null;break t}if(o!==null){o.return=r,pe=o;break t}pe=r}}}var lm={getCacheForType:function(t){var e=Ne(ue),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},sm=typeof WeakMap=="function"?WeakMap:Map,Nt=0,Yt=null,xt=null,wt=0,Ct=0,ii=null,aa=!1,Ml=!1,Du=!1,gn=0,ee=0,la=0,Ya=0,Ru=0,mi=0,El=0,Es=null,Xe=null,Bu=!1,Zu=0,Io=1/0,$o=null,sa=null,Te=0,oa=null,Ll=null,zl=0,ju=0,Hu=null,dh=null,Ls=0,Uu=null;function ni(){if((Nt&2)!==0&&wt!==0)return wt&-wt;if(D.T!==null){var t=_l;return t!==0?t:Xu()}return $s()}function mh(){mi===0&&(mi=(wt&536870912)===0||ft?Js():536870912);var t=di.current;return t!==null&&(t.flags|=32),mi}function ai(t,e,n){(t===Yt&&(Ct===2||Ct===9)||t.cancelPendingCommit!==null)&&(Ol(t,0),ra(t,wt,mi,!1)),wn(t,n),((Nt&2)===0||t!==Yt)&&(t===Yt&&((Nt&2)===0&&(Ya|=n),ee===4&&ra(t,wt,mi,!1)),ki(t))}function _h(t,e,n){if((Nt&6)!==0)throw Error(p(327));var l=!n&&(e&124)===0&&(e&t.expiredLanes)===0||yi(t,e),o=l?um(t,e):ku(t,e,!0),r=l;do{if(o===0){Ml&&!l&&ra(t,e,0,!1);break}else{if(n=t.current.alternate,r&&!om(n)){o=ku(t,e,!1),r=!1;continue}if(o===2){if(r=e,t.errorRecoveryDisabledLanes&r)var f=0;else f=t.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){e=f;t:{var d=t;o=Es;var g=d.current.memoizedState.isDehydrated;if(g&&(Ol(d,f).flags|=256),f=ku(d,f,!1),f!==2){if(Du&&!g){d.errorRecoveryDisabledLanes|=r,Ya|=r,o=4;break t}r=Xe,Xe=o,r!==null&&(Xe===null?Xe=r:Xe.push.apply(Xe,r))}o=f}if(r=!1,o!==2)continue}}if(o===1){Ol(t,0),ra(t,e,0,!0);break}t:{switch(l=t,r=o,r){case 0:case 1:throw Error(p(345));case 4:if((e&4194048)!==e)break;case 6:ra(l,e,mi,!aa);break t;case 2:Xe=null;break;case 3:case 5:break;default:throw Error(p(329))}if((e&62914560)===e&&(o=Zu+300-qe(),10<o)){if(ra(l,e,mi,!aa),Qa(l,0,!0)!==0)break t;l.timeoutHandle=Yh(ph.bind(null,l,n,Xe,$o,Bu,e,mi,Ya,El,aa,r,2,-0,0),o);break t}ph(l,n,Xe,$o,Bu,e,mi,Ya,El,aa,r,0,-0,0)}}break}while(!0);ki(t)}function ph(t,e,n,l,o,r,f,d,g,E,Z,H,O,N){if(t.timeoutHandle=-1,H=e.subtreeFlags,(H&8192||(H&16785408)===16785408)&&(Rs={stylesheets:null,count:0,unsuspend:km},uh(e),H=Ym(),H!==null)){t.cancelPendingCommit=H(Th.bind(null,t,e,r,n,l,o,f,d,g,Z,1,O,N)),ra(t,r,f,!E);return}Th(t,e,r,n,l,o,f,d,g)}function om(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var o=n[l],r=o.getSnapshot;o=o.value;try{if(!Be(r(),o))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function ra(t,e,n,l){e&=~Ru,e&=~Ya,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var o=e;0<o;){var r=31-De(o),f=1<<r;l[r]=-1,o&=~f}n!==0&&Ws(t,n,e)}function Fo(){return(Nt&6)===0?(zs(0),!1):!0}function qu(){if(xt!==null){if(Ct===0)var t=xt.return;else t=xt,wi=hi=null,nu(t),bl=null,gs=0,t=xt;for(;t!==null;)Jf(t.alternate,t),t=t.return;xt=null}}function Ol(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,Mm(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),qu(),Yt=t,xt=n=fi(t.current,null),wt=e,Ct=0,ii=null,aa=!1,Ml=yi(t,e),Du=!1,El=mi=Ru=Ya=la=ee=0,Xe=Es=null,Bu=!1,(e&8)!==0&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var o=31-De(l),r=1<<o;e|=t[o],l&=~r}return gn=e,Ca(),n}function vh(t,e){vt=null,D.H=qo,e===us||e===Ao?(e=Dc(),Ct=3):e===Ac?(e=Dc(),Ct=4):Ct=e===Bf?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,ii=e,xt===null&&(ee=1,Vo(t,je(e,t.current)))}function gh(){var t=D.H;return D.H=qo,t===null?qo:t}function yh(){var t=D.A;return D.A=lm,t}function Pu(){ee=4,aa||(wt&4194048)!==wt&&di.current!==null||(Ml=!0),(la&134217727)===0&&(Ya&134217727)===0||Yt===null||ra(Yt,wt,mi,!1)}function ku(t,e,n){var l=Nt;Nt|=2;var o=gh(),r=yh();(Yt!==t||wt!==e)&&($o=null,Ol(t,e)),e=!1;var f=ee;t:do try{if(Ct!==0&&xt!==null){var d=xt,g=ii;switch(Ct){case 8:qu(),f=6;break t;case 3:case 2:case 9:case 6:di.current===null&&(e=!0);var E=Ct;if(Ct=0,ii=null,Al(t,d,g,E),n&&Ml){f=0;break t}break;default:E=Ct,Ct=0,ii=null,Al(t,d,g,E)}}rm(),f=ee;break}catch(Z){vh(t,Z)}while(!0);return e&&t.shellSuspendCounter++,wi=hi=null,Nt=l,D.H=o,D.A=r,xt===null&&(Yt=null,wt=0,Ca()),f}function rm(){for(;xt!==null;)xh(xt)}function um(t,e){var n=Nt;Nt|=2;var l=gh(),o=yh();Yt!==t||wt!==e?($o=null,Io=qe()+500,Ol(t,e)):Ml=yi(t,e);t:do try{if(Ct!==0&&xt!==null){e=xt;var r=ii;e:switch(Ct){case 1:Ct=0,ii=null,Al(t,e,r,1);break;case 2:case 9:if(Nc(r)){Ct=0,ii=null,bh(e);break}e=function(){Ct!==2&&Ct!==9||Yt!==t||(Ct=7),ki(t)},r.then(e,e);break t;case 3:Ct=7;break t;case 4:Ct=5;break t;case 7:Nc(r)?(Ct=0,ii=null,bh(e)):(Ct=0,ii=null,Al(t,e,r,7));break;case 5:var f=null;switch(xt.tag){case 26:f=xt.memoizedState;case 5:case 27:var d=xt;if(!f||id(f)){Ct=0,ii=null;var g=d.sibling;if(g!==null)xt=g;else{var E=d.return;E!==null?(xt=E,tr(E)):xt=null}break e}}Ct=0,ii=null,Al(t,e,r,5);break;case 6:Ct=0,ii=null,Al(t,e,r,6);break;case 8:qu(),ee=6;break t;default:throw Error(p(462))}}cm();break}catch(Z){vh(t,Z)}while(!0);return wi=hi=null,D.H=l,D.A=o,Nt=n,xt!==null?0:(Yt=null,wt=0,Ca(),ee)}function cm(){for(;xt!==null&&!Gs();)xh(xt)}function xh(t){var e=Qf(t.alternate,t,gn);t.memoizedProps=t.pendingProps,e===null?tr(t):xt=e}function bh(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=Pf(n,e,e.pendingProps,e.type,void 0,wt);break;case 11:e=Pf(n,e,e.pendingProps,e.type.render,e.ref,wt);break;case 5:nu(e);default:Jf(n,e),e=xt=ls(e,gn),e=Qf(n,e,gn)}t.memoizedProps=t.pendingProps,e===null?tr(t):xt=e}function Al(t,e,n,l){wi=hi=null,nu(e),bl=null,gs=0;var o=e.return;try{if(Fd(t,o,e,n,wt)){ee=1,Vo(t,je(n,t.current)),xt=null;return}}catch(r){if(o!==null)throw xt=o,r;ee=1,Vo(t,je(n,t.current)),xt=null;return}e.flags&32768?(ft||l===1?t=!0:Ml||(wt&536870912)!==0?t=!1:(aa=t=!0,(l===2||l===9||l===3||l===6)&&(l=di.current,l!==null&&l.tag===13&&(l.flags|=16384))),Sh(e,t)):tr(e)}function tr(t){var e=t;do{if((e.flags&32768)!==0){Sh(e,aa);return}t=e.return;var n=em(e.alternate,e,gn);if(n!==null){xt=n;return}if(e=e.sibling,e!==null){xt=e;return}xt=e=t}while(e!==null);ee===0&&(ee=5)}function Sh(t,e){do{var n=im(t.alternate,t);if(n!==null){n.flags&=32767,xt=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){xt=t;return}xt=t=n}while(t!==null);ee=6,xt=null}function Th(t,e,n,l,o,r,f,d,g){t.cancelPendingCommit=null;do er();while(Te!==0);if((Nt&6)!==0)throw Error(p(327));if(e!==null){if(e===t.current)throw Error(p(177));if(r=e.lanes|e.childLanes,r|=Fe,wr(t,n,r,f,d,g),t===Yt&&(xt=Yt=null,wt=0),Ll=e,oa=t,zl=n,ju=r,Hu=o,dh=l,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,mm(Sn,function(){return zh(),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||l){l=D.T,D.T=null,o=Q.p,Q.p=2,f=Nt,Nt|=4;try{nm(t,e,n)}finally{Nt=f,Q.p=o,D.T=l}}Te=1,wh(),Mh(),Eh()}}function wh(){if(Te===1){Te=0;var t=oa,e=Ll,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=D.T,D.T=null;var l=Q.p;Q.p=2;var o=Nt;Nt|=4;try{sh(e,t);var r=tc,f=Oa(t.containerInfo),d=r.focusedElem,g=r.selectionRange;if(f!==d&&d&&d.ownerDocument&&ol(d.ownerDocument.documentElement,d)){if(g!==null&&Aa(d)){var E=g.start,Z=g.end;if(Z===void 0&&(Z=E),"selectionStart"in d)d.selectionStart=E,d.selectionEnd=Math.min(Z,d.value.length);else{var H=d.ownerDocument||document,O=H&&H.defaultView||window;if(O.getSelection){var N=O.getSelection(),ct=d.textContent.length,lt=Math.min(g.start,ct),Zt=g.end===void 0?lt:Math.min(g.end,ct);!N.extend&&lt>Zt&&(f=Zt,Zt=lt,lt=f);var S=ns(d,lt),b=ns(d,Zt);if(S&&b&&(N.rangeCount!==1||N.anchorNode!==S.node||N.anchorOffset!==S.offset||N.focusNode!==b.node||N.focusOffset!==b.offset)){var M=H.createRange();M.setStart(S.node,S.offset),N.removeAllRanges(),lt>Zt?(N.addRange(M),N.extend(b.node,b.offset)):(M.setEnd(b.node,b.offset),N.addRange(M))}}}}for(H=[],N=d;N=N.parentNode;)N.nodeType===1&&H.push({element:N,left:N.scrollLeft,top:N.scrollTop});for(typeof d.focus=="function"&&d.focus(),d=0;d<H.length;d++){var j=H[d];j.element.scrollLeft=j.left,j.element.scrollTop=j.top}}dr=!!Fu,tc=Fu=null}finally{Nt=o,Q.p=l,D.T=n}}t.current=e,Te=2}}function Mh(){if(Te===2){Te=0;var t=oa,e=Ll,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=D.T,D.T=null;var l=Q.p;Q.p=2;var o=Nt;Nt|=4;try{ih(t,e.alternate,e)}finally{Nt=o,Q.p=l,D.T=n}}Te=3}}function Eh(){if(Te===4||Te===3){Te=0,Ys();var t=oa,e=Ll,n=zl,l=dh;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Te=5:(Te=0,Ll=oa=null,Lh(t,t.pendingLanes));var o=t.pendingLanes;if(o===0&&(sa=null),Pl(n),e=e.stateNode,Le&&typeof Le.onCommitFiberRoot=="function")try{Le.onCommitFiberRoot(Yi,e,void 0,(e.current.flags&128)===128)}catch{}if(l!==null){e=D.T,o=Q.p,Q.p=2,D.T=null;try{for(var r=t.onRecoverableError,f=0;f<l.length;f++){var d=l[f];r(d.value,{componentStack:d.stack})}}finally{D.T=e,Q.p=o}}(zl&3)!==0&&er(),ki(t),o=t.pendingLanes,(n&4194090)!==0&&(o&42)!==0?t===Uu?Ls++:(Ls=0,Uu=t):Ls=0,zs(0)}}function Lh(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,os(e)))}function er(t){return wh(),Mh(),Eh(),zh()}function zh(){if(Te!==5)return!1;var t=oa,e=ju;ju=0;var n=Pl(zl),l=D.T,o=Q.p;try{Q.p=32>n?32:n,D.T=null,n=Hu,Hu=null;var r=oa,f=zl;if(Te=0,Ll=oa=null,zl=0,(Nt&6)!==0)throw Error(p(331));var d=Nt;if(Nt|=4,fh(r.current),rh(r,r.current,f,n),Nt=d,zs(0,!1),Le&&typeof Le.onPostCommitFiberRoot=="function")try{Le.onPostCommitFiberRoot(Yi,r)}catch{}return!0}finally{Q.p=o,D.T=l,Lh(t,e)}}function Oh(t,e,n){e=je(n,e),e=vu(t.stateNode,e,2),t=In(t,e,2),t!==null&&(wn(t,2),ki(t))}function Ut(t,e,n){if(t.tag===3)Oh(t,t,n);else for(;e!==null;){if(e.tag===3){Oh(e,t,n);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(sa===null||!sa.has(l))){t=je(n,t),n=Df(2),l=In(e,n,2),l!==null&&(Rf(n,l,e,t),wn(l,2),ki(l));break}}e=e.return}}function Gu(t,e,n){var l=t.pingCache;if(l===null){l=t.pingCache=new sm;var o=new Set;l.set(e,o)}else o=l.get(e),o===void 0&&(o=new Set,l.set(e,o));o.has(n)||(Du=!0,o.add(n),t=fm.bind(null,t,e,n),e.then(t,t))}function fm(t,e,n){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,Yt===t&&(wt&n)===n&&(ee===4||ee===3&&(wt&62914560)===wt&&300>qe()-Zu?(Nt&2)===0&&Ol(t,0):Ru|=n,El===wt&&(El=0)),ki(t)}function Ah(t,e){e===0&&(e=Ul()),t=Vn(t,e),t!==null&&(wn(t,e),ki(t))}function hm(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),Ah(t,n)}function dm(t,e){var n=0;switch(t.tag){case 13:var l=t.stateNode,o=t.memoizedState;o!==null&&(n=o.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(p(314))}l!==null&&l.delete(e),Ah(t,n)}function mm(t,e){return pa(t,e)}var ir=null,Nl=null,Yu=!1,nr=!1,Vu=!1,Va=0;function ki(t){t!==Nl&&t.next===null&&(Nl===null?ir=Nl=t:Nl=Nl.next=t),nr=!0,Yu||(Yu=!0,pm())}function zs(t,e){if(!Vu&&nr){Vu=!0;do for(var n=!1,l=ir;l!==null;){if(t!==0){var o=l.pendingLanes;if(o===0)var r=0;else{var f=l.suspendedLanes,d=l.pingedLanes;r=(1<<31-De(42|t)+1)-1,r&=o&~(f&~d),r=r&201326741?r&201326741|1:r?r|2:0}r!==0&&(n=!0,Rh(l,r))}else r=wt,r=Qa(l,l===Yt?r:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(r&3)===0||yi(l,r)||(n=!0,Rh(l,r));l=l.next}while(n);Vu=!1}}function _m(){Nh()}function Nh(){nr=Yu=!1;var t=0;Va!==0&&(wm()&&(t=Va),Va=0);for(var e=qe(),n=null,l=ir;l!==null;){var o=l.next,r=Ch(l,e);r===0?(l.next=null,n===null?ir=o:n.next=o,o===null&&(Nl=n)):(n=l,(t!==0||(r&3)!==0)&&(nr=!0)),l=o}zs(t)}function Ch(t,e){for(var n=t.suspendedLanes,l=t.pingedLanes,o=t.expirationTimes,r=t.pendingLanes&-62914561;0<r;){var f=31-De(r),d=1<<f,g=o[f];g===-1?((d&n)===0||(d&l)!==0)&&(o[f]=Tr(d,e)):g<=e&&(t.expiredLanes|=d),r&=~d}if(e=Yt,n=wt,n=Qa(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,n===0||t===e&&(Ct===2||Ct===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&Zl(l),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||yi(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(l!==null&&Zl(l),Pl(n)){case 2:case 8:n=jl;break;case 32:n=Sn;break;case 268435456:n=Hl;break;default:n=Sn}return l=Dh.bind(null,t),n=pa(n,l),t.callbackPriority=e,t.callbackNode=n,e}return l!==null&&l!==null&&Zl(l),t.callbackPriority=2,t.callbackNode=null,2}function Dh(t,e){if(Te!==0&&Te!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(er()&&t.callbackNode!==n)return null;var l=wt;return l=Qa(t,t===Yt?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(_h(t,l,e),Ch(t,qe()),t.callbackNode!=null&&t.callbackNode===n?Dh.bind(null,t):null)}function Rh(t,e){if(er())return null;_h(t,e,!0)}function pm(){Em(function(){(Nt&6)!==0?pa(Vs,_m):Nh()})}function Xu(){return Va===0&&(Va=Js()),Va}function Bh(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Cn(""+t)}function Zh(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function vm(t,e,n,l,o){if(e==="submit"&&n&&n.stateNode===o){var r=Bh((o[ze]||null).action),f=l.submitter;f&&(e=(e=f[ze]||null)?Bh(e.formAction):f.getAttribute("formAction"),e!==null&&(r=e,f=null));var d=new wa("action","action",null,l,o);t.push({event:d,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Va!==0){var g=f?Zh(o,f):new FormData(o);hu(n,{pending:!0,data:g,method:o.method,action:r},null,g)}}else typeof r=="function"&&(d.preventDefault(),g=f?Zh(o,f):new FormData(o),hu(n,{pending:!0,data:g,method:o.method,action:r},r,g))},currentTarget:o}]})}}for(var Qu=0;Qu<on.length;Qu++){var Ku=on[Qu],gm=Ku.toLowerCase(),ym=Ku[0].toUpperCase()+Ku.slice(1);$e(gm,"on"+ym)}$e(bo,"onAnimationEnd"),$e(Ie,"onAnimationIteration"),$e(Na,"onAnimationStart"),$e("dblclick","onDoubleClick"),$e("focusin","onFocus"),$e("focusout","onBlur"),$e(Pr,"onTransitionRun"),$e(fl,"onTransitionStart"),$e(kr,"onTransitionCancel"),$e(as,"onTransitionEnd"),Ki("onMouseEnter",["mouseout","mouseover"]),Ki("onMouseLeave",["mouseout","mouseover"]),Ki("onPointerEnter",["pointerout","pointerover"]),Ki("onPointerLeave",["pointerout","pointerover"]),Qi("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Qi("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Qi("onBeforeInput",["compositionend","keypress","textInput","paste"]),Qi("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Qi("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Qi("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Os="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),xm=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Os));function jh(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var l=t[n],o=l.event;l=l.listeners;t:{var r=void 0;if(e)for(var f=l.length-1;0<=f;f--){var d=l[f],g=d.instance,E=d.currentTarget;if(d=d.listener,g!==r&&o.isPropagationStopped())break t;r=d,o.currentTarget=E;try{r(o)}catch(Z){Yo(Z)}o.currentTarget=null,r=g}else for(f=0;f<l.length;f++){if(d=l[f],g=d.instance,E=d.currentTarget,d=d.listener,g!==r&&o.isPropagationStopped())break t;r=d,o.currentTarget=E;try{r(o)}catch(Z){Yo(Z)}o.currentTarget=null,r=g}}}}function bt(t,e){var n=e[Pe];n===void 0&&(n=e[Pe]=new Set);var l=t+"__bubble";n.has(l)||(Hh(e,t,2,!1),n.add(l))}function Ju(t,e,n){var l=0;e&&(l|=4),Hh(n,t,l,e)}var ar="_reactListening"+Math.random().toString(36).slice(2);function Wu(t){if(!t[ar]){t[ar]=!0,to.forEach(function(n){n!=="selectionchange"&&(xm.has(n)||Ju(n,!1,t),Ju(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[ar]||(e[ar]=!0,Ju("selectionchange",!1,e))}}function Hh(t,e,n,l){switch(rd(e)){case 2:var o=Qm;break;case 8:o=Km;break;default:o=cc}n=o.bind(null,e,n,t),o=void 0,!Ta||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(o=!0),l?o!==void 0?t.addEventListener(e,n,{capture:!0,passive:o}):t.addEventListener(e,n,!0):o!==void 0?t.addEventListener(e,n,{passive:o}):t.addEventListener(e,n,!1)}function Iu(t,e,n,l,o){var r=l;if((e&1)===0&&(e&2)===0&&l!==null)t:for(;;){if(l===null)return;var f=l.tag;if(f===3||f===4){var d=l.stateNode.containerInfo;if(d===o)break;if(f===4)for(f=l.return;f!==null;){var g=f.tag;if((g===3||g===4)&&f.stateNode.containerInfo===o)return;f=f.return}for(;d!==null;){if(f=Ni(d),f===null)return;if(g=f.tag,g===5||g===6||g===26||g===27){l=r=f;continue t}d=d.parentNode}}l=l.return}Qe(function(){var E=r,Z=Dn(n),H=[];t:{var O=So.get(t);if(O!==void 0){var N=wa,ct=t;switch(t){case"keypress":if(te(n)===0)break t;case"keydown":case"keyup":N=Dr;break;case"focusin":ct="focus",N=Kl;break;case"focusout":ct="blur",N=Kl;break;case"beforeblur":case"afterblur":N=Kl;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":N=Zn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":N=zr;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":N=Br;break;case bo:case Ie:case Na:N=Or;break;case as:N=Zr;break;case"scroll":case"scrollend":N=Lr;break;case"wheel":N=fo;break;case"copy":case"cut":case"paste":N=Jl;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":N=Il;break;case"toggle":case"beforetoggle":N=nn}var lt=(e&4)!==0,Zt=!lt&&(t==="scroll"||t==="scrollend"),S=lt?O!==null?O+"Capture":null:O;lt=[];for(var b=E,M;b!==null;){var j=b;if(M=j.stateNode,j=j.tag,j!==5&&j!==26&&j!==27||M===null||S===null||(j=zt(b,S),j!=null&&lt.push(As(b,j,M))),Zt)break;b=b.return}0<lt.length&&(O=new N(O,ct,null,n,Z),H.push({event:O,listeners:lt}))}}if((e&7)===0){t:{if(O=t==="mouseover"||t==="pointerover",N=t==="mouseout"||t==="pointerout",O&&n!==Sa&&(ct=n.relatedTarget||n.fromElement)&&(Ni(ct)||ct[Mn]))break t;if((N||O)&&(O=Z.window===Z?Z:(O=Z.ownerDocument)?O.defaultView||O.parentWindow:window,N?(ct=n.relatedTarget||n.toElement,N=E,ct=ct?Ni(ct):null,ct!==null&&(Zt=q(ct),lt=ct.tag,ct!==Zt||lt!==5&&lt!==27&&lt!==6)&&(ct=null)):(N=null,ct=E),N!==ct)){if(lt=Zn,j="onMouseLeave",S="onMouseEnter",b="mouse",(t==="pointerout"||t==="pointerover")&&(lt=Il,j="onPointerLeave",S="onPointerEnter",b="pointer"),Zt=N==null?O:li(N),M=ct==null?O:li(ct),O=new lt(j,b+"leave",N,n,Z),O.target=Zt,O.relatedTarget=M,j=null,Ni(Z)===E&&(lt=new lt(S,b+"enter",ct,n,Z),lt.target=M,lt.relatedTarget=Zt,j=lt),Zt=j,N&&ct)e:{for(lt=N,S=ct,b=0,M=lt;M;M=Cl(M))b++;for(M=0,j=S;j;j=Cl(j))M++;for(;0<b-M;)lt=Cl(lt),b--;for(;0<M-b;)S=Cl(S),M--;for(;b--;){if(lt===S||S!==null&&lt===S.alternate)break e;lt=Cl(lt),S=Cl(S)}lt=null}else lt=null;N!==null&&Uh(H,O,N,lt,!1),ct!==null&&Zt!==null&&Uh(H,Zt,ct,lt,!0)}}t:{if(O=E?li(E):window,N=O.nodeName&&O.nodeName.toLowerCase(),N==="select"||N==="input"&&O.type==="file")var $=Un;else if(ln(O))if(ts)$=qr;else{$=Ur;var yt=is}else N=O.nodeName,!N||N.toLowerCase()!=="input"||O.type!=="checkbox"&&O.type!=="radio"?E&&ba(E.elementType)&&($=Un):$=Ti;if($&&($=$(t,E))){vo(H,$,n,Z);break t}yt&&yt(t,O,E),t==="focusout"&&E&&O.type==="number"&&E.memoizedProps.value!=null&&An(O,"number",O.value)}switch(yt=E?li(E):window,t){case"focusin":(ln(yt)||yt.contentEditable==="true")&&(We=yt,kn=E,sn=null);break;case"focusout":sn=kn=We=null;break;case"mousedown":ul=!0;break;case"contextmenu":case"mouseup":case"dragend":ul=!1,yo(H,n,Z);break;case"selectionchange":if(rl)break;case"keydown":case"keyup":yo(H,n,Z)}var nt;if(an)t:{switch(t){case"compositionstart":var ot="onCompositionStart";break t;case"compositionend":ot="onCompositionEnd";break t;case"compositionupdate":ot="onCompositionUpdate";break t}ot=void 0}else jn?ll(t,n)&&(ot="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(ot="onCompositionStart");ot&&(Fl&&n.locale!=="ko"&&(jn||ot!=="onCompositionStart"?ot==="onCompositionEnd"&&jn&&(nt=Bn()):(ri=Z,bi="value"in ri?ri.value:ri.textContent,jn=!0)),yt=lr(E,ot),0<yt.length&&(ot=new Ke(ot,t,null,n,Z),H.push({event:ot,listeners:yt}),nt?ot.data=nt:(nt=_o(n),nt!==null&&(ot.data=nt)))),(nt=ho?po(t,n):Hr(t,n))&&(ot=lr(E,"onBeforeInput"),0<ot.length&&(yt=new Ke("onBeforeInput","beforeinput",null,n,Z),H.push({event:yt,listeners:ot}),yt.data=nt)),vm(H,t,E,n,Z)}jh(H,e)})}function As(t,e,n){return{instance:t,listener:e,currentTarget:n}}function lr(t,e){for(var n=e+"Capture",l=[];t!==null;){var o=t,r=o.stateNode;if(o=o.tag,o!==5&&o!==26&&o!==27||r===null||(o=zt(t,n),o!=null&&l.unshift(As(t,o,r)),o=zt(t,e),o!=null&&l.push(As(t,o,r))),t.tag===3)return l;t=t.return}return[]}function Cl(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Uh(t,e,n,l,o){for(var r=e._reactName,f=[];n!==null&&n!==l;){var d=n,g=d.alternate,E=d.stateNode;if(d=d.tag,g!==null&&g===l)break;d!==5&&d!==26&&d!==27||E===null||(g=E,o?(E=zt(n,r),E!=null&&f.unshift(As(n,E,g))):o||(E=zt(n,r),E!=null&&f.push(As(n,E,g)))),n=n.return}f.length!==0&&t.push({event:e,listeners:f})}var bm=/\r\n?/g,Sm=/\u0000|\uFFFD/g;function qh(t){return(typeof t=="string"?t:""+t).replace(bm,`
`).replace(Sm,"")}function Ph(t,e){return e=qh(e),qh(t)===e}function sr(){}function Bt(t,e,n,l,o,r){switch(n){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||si(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&si(t,""+l);break;case"className":Ia(t,"class",l);break;case"tabIndex":Ia(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Ia(t,n,l);break;case"style":Nn(t,l,r);break;case"data":if(e!=="object"){Ia(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=Cn(""+l),t.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof r=="function"&&(n==="formAction"?(e!=="input"&&Bt(t,e,"name",o.name,o,null),Bt(t,e,"formEncType",o.formEncType,o,null),Bt(t,e,"formMethod",o.formMethod,o,null),Bt(t,e,"formTarget",o.formTarget,o,null)):(Bt(t,e,"encType",o.encType,o,null),Bt(t,e,"method",o.method,o,null),Bt(t,e,"target",o.target,o,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=Cn(""+l),t.setAttribute(n,l);break;case"onClick":l!=null&&(t.onclick=sr);break;case"onScroll":l!=null&&bt("scroll",t);break;case"onScrollEnd":l!=null&&bt("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(p(61));if(n=l.__html,n!=null){if(o.children!=null)throw Error(p(60));t.innerHTML=n}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}n=Cn(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""+l):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":l===!0?t.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,l):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(n,l):t.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(n):t.setAttribute(n,l);break;case"popover":bt("beforetoggle",t),bt("toggle",t),Wa(t,"popover",l);break;case"xlinkActuate":xi(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":xi(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":xi(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":xi(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":xi(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":xi(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":xi(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":xi(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":xi(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Wa(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Vl.get(n)||n,Wa(t,n,l))}}function $u(t,e,n,l,o,r){switch(n){case"style":Nn(t,l,r);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(p(61));if(n=l.__html,n!=null){if(o.children!=null)throw Error(p(60));t.innerHTML=n}}break;case"children":typeof l=="string"?si(t,l):(typeof l=="number"||typeof l=="bigint")&&si(t,""+l);break;case"onScroll":l!=null&&bt("scroll",t);break;case"onScrollEnd":l!=null&&bt("scrollend",t);break;case"onClick":l!=null&&(t.onclick=sr);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!eo.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(o=n.endsWith("Capture"),e=n.slice(2,o?n.length-7:void 0),r=t[ze]||null,r=r!=null?r[n]:null,typeof r=="function"&&t.removeEventListener(e,r,o),typeof l=="function")){typeof r!="function"&&r!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,l,o);break t}n in t?t[n]=l:l===!0?t.setAttribute(n,""):Wa(t,n,l)}}}function we(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":bt("error",t),bt("load",t);var l=!1,o=!1,r;for(r in n)if(n.hasOwnProperty(r)){var f=n[r];if(f!=null)switch(r){case"src":l=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(p(137,e));default:Bt(t,e,r,f,n,null)}}o&&Bt(t,e,"srcSet",n.srcSet,n,null),l&&Bt(t,e,"src",n.src,n,null);return;case"input":bt("invalid",t);var d=r=f=o=null,g=null,E=null;for(l in n)if(n.hasOwnProperty(l)){var Z=n[l];if(Z!=null)switch(l){case"name":o=Z;break;case"type":f=Z;break;case"checked":g=Z;break;case"defaultChecked":E=Z;break;case"value":r=Z;break;case"defaultValue":d=Z;break;case"children":case"dangerouslySetInnerHTML":if(Z!=null)throw Error(p(137,e));break;default:Bt(t,e,l,Z,n,null)}}no(t,r,d,g,E,f,o,!1),On(t);return;case"select":bt("invalid",t),l=f=r=null;for(o in n)if(n.hasOwnProperty(o)&&(d=n[o],d!=null))switch(o){case"value":r=d;break;case"defaultValue":f=d;break;case"multiple":l=d;default:Bt(t,e,o,d,n,null)}e=r,n=f,t.multiple=!!l,e!=null?ke(t,!!l,e,!1):n!=null&&ke(t,!!l,n,!0);return;case"textarea":bt("invalid",t),r=o=l=null;for(f in n)if(n.hasOwnProperty(f)&&(d=n[f],d!=null))switch(f){case"value":l=d;break;case"defaultValue":o=d;break;case"children":r=d;break;case"dangerouslySetInnerHTML":if(d!=null)throw Error(p(91));break;default:Bt(t,e,f,d,n,null)}Ci(t,l,o,r),On(t);return;case"option":for(g in n)if(n.hasOwnProperty(g)&&(l=n[g],l!=null))switch(g){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Bt(t,e,g,l,n,null)}return;case"dialog":bt("beforetoggle",t),bt("toggle",t),bt("cancel",t),bt("close",t);break;case"iframe":case"object":bt("load",t);break;case"video":case"audio":for(l=0;l<Os.length;l++)bt(Os[l],t);break;case"image":bt("error",t),bt("load",t);break;case"details":bt("toggle",t);break;case"embed":case"source":case"link":bt("error",t),bt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(E in n)if(n.hasOwnProperty(E)&&(l=n[E],l!=null))switch(E){case"children":case"dangerouslySetInnerHTML":throw Error(p(137,e));default:Bt(t,e,E,l,n,null)}return;default:if(ba(e)){for(Z in n)n.hasOwnProperty(Z)&&(l=n[Z],l!==void 0&&$u(t,e,Z,l,n,void 0));return}}for(d in n)n.hasOwnProperty(d)&&(l=n[d],l!=null&&Bt(t,e,d,l,n,null))}function Tm(t,e,n,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,r=null,f=null,d=null,g=null,E=null,Z=null;for(N in n){var H=n[N];if(n.hasOwnProperty(N)&&H!=null)switch(N){case"checked":break;case"value":break;case"defaultValue":g=H;default:l.hasOwnProperty(N)||Bt(t,e,N,null,l,H)}}for(var O in l){var N=l[O];if(H=n[O],l.hasOwnProperty(O)&&(N!=null||H!=null))switch(O){case"type":r=N;break;case"name":o=N;break;case"checked":E=N;break;case"defaultChecked":Z=N;break;case"value":f=N;break;case"defaultValue":d=N;break;case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(p(137,e));break;default:N!==H&&Bt(t,e,O,N,l,H)}}Oe(t,f,d,g,E,Z,r,o);return;case"select":N=f=d=O=null;for(r in n)if(g=n[r],n.hasOwnProperty(r)&&g!=null)switch(r){case"value":break;case"multiple":N=g;default:l.hasOwnProperty(r)||Bt(t,e,r,null,l,g)}for(o in l)if(r=l[o],g=n[o],l.hasOwnProperty(o)&&(r!=null||g!=null))switch(o){case"value":O=r;break;case"defaultValue":d=r;break;case"multiple":f=r;default:r!==g&&Bt(t,e,o,r,l,g)}e=d,n=f,l=N,O!=null?ke(t,!!n,O,!1):!!l!=!!n&&(e!=null?ke(t,!!n,e,!0):ke(t,!!n,n?[]:"",!1));return;case"textarea":N=O=null;for(d in n)if(o=n[d],n.hasOwnProperty(d)&&o!=null&&!l.hasOwnProperty(d))switch(d){case"value":break;case"children":break;default:Bt(t,e,d,null,l,o)}for(f in l)if(o=l[f],r=n[f],l.hasOwnProperty(f)&&(o!=null||r!=null))switch(f){case"value":O=o;break;case"defaultValue":N=o;break;case"children":break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(p(91));break;default:o!==r&&Bt(t,e,f,o,l,r)}Jt(t,O,N);return;case"option":for(var ct in n)if(O=n[ct],n.hasOwnProperty(ct)&&O!=null&&!l.hasOwnProperty(ct))switch(ct){case"selected":t.selected=!1;break;default:Bt(t,e,ct,null,l,O)}for(g in l)if(O=l[g],N=n[g],l.hasOwnProperty(g)&&O!==N&&(O!=null||N!=null))switch(g){case"selected":t.selected=O&&typeof O!="function"&&typeof O!="symbol";break;default:Bt(t,e,g,O,l,N)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var lt in n)O=n[lt],n.hasOwnProperty(lt)&&O!=null&&!l.hasOwnProperty(lt)&&Bt(t,e,lt,null,l,O);for(E in l)if(O=l[E],N=n[E],l.hasOwnProperty(E)&&O!==N&&(O!=null||N!=null))switch(E){case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(p(137,e));break;default:Bt(t,e,E,O,l,N)}return;default:if(ba(e)){for(var Zt in n)O=n[Zt],n.hasOwnProperty(Zt)&&O!==void 0&&!l.hasOwnProperty(Zt)&&$u(t,e,Zt,void 0,l,O);for(Z in l)O=l[Z],N=n[Z],!l.hasOwnProperty(Z)||O===N||O===void 0&&N===void 0||$u(t,e,Z,O,l,N);return}}for(var S in n)O=n[S],n.hasOwnProperty(S)&&O!=null&&!l.hasOwnProperty(S)&&Bt(t,e,S,null,l,O);for(H in l)O=l[H],N=n[H],!l.hasOwnProperty(H)||O===N||O==null&&N==null||Bt(t,e,H,O,l,N)}var Fu=null,tc=null;function or(t){return t.nodeType===9?t:t.ownerDocument}function kh(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Gh(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function ec(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var ic=null;function wm(){var t=window.event;return t&&t.type==="popstate"?t===ic?!1:(ic=t,!0):(ic=null,!1)}var Yh=typeof setTimeout=="function"?setTimeout:void 0,Mm=typeof clearTimeout=="function"?clearTimeout:void 0,Vh=typeof Promise=="function"?Promise:void 0,Em=typeof queueMicrotask=="function"?queueMicrotask:typeof Vh<"u"?function(t){return Vh.resolve(null).then(t).catch(Lm)}:Yh;function Lm(t){setTimeout(function(){throw t})}function ua(t){return t==="head"}function Xh(t,e){var n=e,l=0,o=0;do{var r=n.nextSibling;if(t.removeChild(n),r&&r.nodeType===8)if(n=r.data,n==="/$"){if(0<l&&8>l){n=l;var f=t.ownerDocument;if(n&1&&Ns(f.documentElement),n&2&&Ns(f.body),n&4)for(n=f.head,Ns(n),f=n.firstChild;f;){var d=f.nextSibling,g=f.nodeName;f[En]||g==="SCRIPT"||g==="STYLE"||g==="LINK"&&f.rel.toLowerCase()==="stylesheet"||n.removeChild(f),f=d}}if(o===0){t.removeChild(r),Us(e);return}o--}else n==="$"||n==="$?"||n==="$!"?o++:l=n.charCodeAt(0)-48;else l=0;n=r}while(n);Us(e)}function nc(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":nc(n),Ja(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function zm(t,e,n,l){for(;t.nodeType===1;){var o=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[En])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(r=t.getAttribute("rel"),r==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(r!==o.rel||t.getAttribute("href")!==(o.href==null||o.href===""?null:o.href)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin)||t.getAttribute("title")!==(o.title==null?null:o.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(r=t.getAttribute("src"),(r!==(o.src==null?null:o.src)||t.getAttribute("type")!==(o.type==null?null:o.type)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin))&&r&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var r=o.name==null?null:""+o.name;if(o.type==="hidden"&&t.getAttribute("name")===r)return t}else return t;if(t=Li(t.nextSibling),t===null)break}return null}function Om(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Li(t.nextSibling),t===null))return null;return t}function ac(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Am(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var l=function(){e(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function Li(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var lc=null;function Qh(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function Kh(t,e,n){switch(e=or(n),t){case"html":if(t=e.documentElement,!t)throw Error(p(452));return t;case"head":if(t=e.head,!t)throw Error(p(453));return t;case"body":if(t=e.body,!t)throw Error(p(454));return t;default:throw Error(p(451))}}function Ns(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Ja(t)}var _i=new Map,Jh=new Set;function rr(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var yn=Q.d;Q.d={f:Nm,r:Cm,D:Dm,C:Rm,L:Bm,m:Zm,X:Hm,S:jm,M:Um};function Nm(){var t=yn.f(),e=Fo();return t||e}function Cm(t){var e=Vi(t);e!==null&&e.tag===5&&e.type==="form"?_f(e):yn.r(t)}var Dl=typeof document>"u"?null:document;function Wh(t,e,n){var l=Dl;if(l&&typeof e=="string"&&e){var o=xe(e);o='link[rel="'+t+'"][href="'+o+'"]',typeof n=="string"&&(o+='[crossorigin="'+n+'"]'),Jh.has(o)||(Jh.add(o),t={rel:t,crossOrigin:n,href:e},l.querySelector(o)===null&&(e=l.createElement("link"),we(e,"link",t),se(e),l.head.appendChild(e)))}}function Dm(t){yn.D(t),Wh("dns-prefetch",t,null)}function Rm(t,e){yn.C(t,e),Wh("preconnect",t,e)}function Bm(t,e,n){yn.L(t,e,n);var l=Dl;if(l&&t&&e){var o='link[rel="preload"][as="'+xe(e)+'"]';e==="image"&&n&&n.imageSrcSet?(o+='[imagesrcset="'+xe(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(o+='[imagesizes="'+xe(n.imageSizes)+'"]')):o+='[href="'+xe(t)+'"]';var r=o;switch(e){case"style":r=Rl(t);break;case"script":r=Bl(t)}_i.has(r)||(t=k({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),_i.set(r,t),l.querySelector(o)!==null||e==="style"&&l.querySelector(Cs(r))||e==="script"&&l.querySelector(Ds(r))||(e=l.createElement("link"),we(e,"link",t),se(e),l.head.appendChild(e)))}}function Zm(t,e){yn.m(t,e);var n=Dl;if(n&&t){var l=e&&typeof e.as=="string"?e.as:"script",o='link[rel="modulepreload"][as="'+xe(l)+'"][href="'+xe(t)+'"]',r=o;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":r=Bl(t)}if(!_i.has(r)&&(t=k({rel:"modulepreload",href:t},e),_i.set(r,t),n.querySelector(o)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Ds(r)))return}l=n.createElement("link"),we(l,"link",t),se(l),n.head.appendChild(l)}}}function jm(t,e,n){yn.S(t,e,n);var l=Dl;if(l&&t){var o=Xi(l).hoistableStyles,r=Rl(t);e=e||"default";var f=o.get(r);if(!f){var d={loading:0,preload:null};if(f=l.querySelector(Cs(r)))d.loading=5;else{t=k({rel:"stylesheet",href:t,"data-precedence":e},n),(n=_i.get(r))&&sc(t,n);var g=f=l.createElement("link");se(g),we(g,"link",t),g._p=new Promise(function(E,Z){g.onload=E,g.onerror=Z}),g.addEventListener("load",function(){d.loading|=1}),g.addEventListener("error",function(){d.loading|=2}),d.loading|=4,ur(f,e,l)}f={type:"stylesheet",instance:f,count:1,state:d},o.set(r,f)}}}function Hm(t,e){yn.X(t,e);var n=Dl;if(n&&t){var l=Xi(n).hoistableScripts,o=Bl(t),r=l.get(o);r||(r=n.querySelector(Ds(o)),r||(t=k({src:t,async:!0},e),(e=_i.get(o))&&oc(t,e),r=n.createElement("script"),se(r),we(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(o,r))}}function Um(t,e){yn.M(t,e);var n=Dl;if(n&&t){var l=Xi(n).hoistableScripts,o=Bl(t),r=l.get(o);r||(r=n.querySelector(Ds(o)),r||(t=k({src:t,async:!0,type:"module"},e),(e=_i.get(o))&&oc(t,e),r=n.createElement("script"),se(r),we(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(o,r))}}function Ih(t,e,n,l){var o=(o=X.current)?rr(o):null;if(!o)throw Error(p(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=Rl(n.href),n=Xi(o).hoistableStyles,l=n.get(e),l||(l={type:"style",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=Rl(n.href);var r=Xi(o).hoistableStyles,f=r.get(t);if(f||(o=o.ownerDocument||o,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},r.set(t,f),(r=o.querySelector(Cs(t)))&&!r._p&&(f.instance=r,f.state.loading=5),_i.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},_i.set(t,n),r||qm(o,t,n,f.state))),e&&l===null)throw Error(p(528,""));return f}if(e&&l!==null)throw Error(p(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Bl(n),n=Xi(o).hoistableScripts,l=n.get(e),l||(l={type:"script",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(p(444,t))}}function Rl(t){return'href="'+xe(t)+'"'}function Cs(t){return'link[rel="stylesheet"]['+t+"]"}function $h(t){return k({},t,{"data-precedence":t.precedence,precedence:null})}function qm(t,e,n,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),we(e,"link",n),se(e),t.head.appendChild(e))}function Bl(t){return'[src="'+xe(t)+'"]'}function Ds(t){return"script[async]"+t}function Fh(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+xe(n.href)+'"]');if(l)return e.instance=l,se(l),l;var o=k({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),se(l),we(l,"style",o),ur(l,n.precedence,t),e.instance=l;case"stylesheet":o=Rl(n.href);var r=t.querySelector(Cs(o));if(r)return e.state.loading|=4,e.instance=r,se(r),r;l=$h(n),(o=_i.get(o))&&sc(l,o),r=(t.ownerDocument||t).createElement("link"),se(r);var f=r;return f._p=new Promise(function(d,g){f.onload=d,f.onerror=g}),we(r,"link",l),e.state.loading|=4,ur(r,n.precedence,t),e.instance=r;case"script":return r=Bl(n.src),(o=t.querySelector(Ds(r)))?(e.instance=o,se(o),o):(l=n,(o=_i.get(r))&&(l=k({},n),oc(l,o)),t=t.ownerDocument||t,o=t.createElement("script"),se(o),we(o,"link",l),t.head.appendChild(o),e.instance=o);case"void":return null;default:throw Error(p(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(l=e.instance,e.state.loading|=4,ur(l,n.precedence,t));return e.instance}function ur(t,e,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=l.length?l[l.length-1]:null,r=o,f=0;f<l.length;f++){var d=l[f];if(d.dataset.precedence===e)r=d;else if(r!==o)break}r?r.parentNode.insertBefore(t,r.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function sc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function oc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var cr=null;function td(t,e,n){if(cr===null){var l=new Map,o=cr=new Map;o.set(n,l)}else o=cr,l=o.get(n),l||(l=new Map,o.set(n,l));if(l.has(t))return l;for(l.set(t,null),n=n.getElementsByTagName(t),o=0;o<n.length;o++){var r=n[o];if(!(r[En]||r[he]||t==="link"&&r.getAttribute("rel")==="stylesheet")&&r.namespaceURI!=="http://www.w3.org/2000/svg"){var f=r.getAttribute(e)||"";f=t+f;var d=l.get(f);d?d.push(r):l.set(f,[r])}}return l}function ed(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function Pm(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function id(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Rs=null;function km(){}function Gm(t,e,n){if(Rs===null)throw Error(p(475));var l=Rs;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var o=Rl(n.href),r=t.querySelector(Cs(o));if(r){t=r._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=fr.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=r,se(r);return}r=t.ownerDocument||t,n=$h(n),(o=_i.get(o))&&sc(n,o),r=r.createElement("link"),se(r);var f=r;f._p=new Promise(function(d,g){f.onload=d,f.onerror=g}),we(r,"link",n),e.instance=r}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(l.count++,e=fr.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function Ym(){if(Rs===null)throw Error(p(475));var t=Rs;return t.stylesheets&&t.count===0&&rc(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&rc(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function fr(){if(this.count--,this.count===0){if(this.stylesheets)rc(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var hr=null;function rc(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,hr=new Map,e.forEach(Vm,t),hr=null,fr.call(t))}function Vm(t,e){if(!(e.state.loading&4)){var n=hr.get(t);if(n)var l=n.get(null);else{n=new Map,hr.set(t,n);for(var o=t.querySelectorAll("link[data-precedence],style[data-precedence]"),r=0;r<o.length;r++){var f=o[r];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(n.set(f.dataset.precedence,f),l=f)}l&&n.set(null,l)}o=e.instance,f=o.getAttribute("data-precedence"),r=n.get(f)||l,r===l&&n.set(null,o),n.set(f,o),this.count++,l=fr.bind(this),o.addEventListener("load",l),o.addEventListener("error",l),r?r.parentNode.insertBefore(o,r.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(o,t.firstChild)),e.state.loading|=4}}var Bs={$$typeof:dt,Provider:null,Consumer:null,_currentValue:G,_currentValue2:G,_threadCount:0};function Xm(t,e,n,l,o,r,f,d){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Ka(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ka(0),this.hiddenUpdates=Ka(null),this.identifierPrefix=l,this.onUncaughtError=o,this.onCaughtError=r,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=d,this.incompleteTransitions=new Map}function nd(t,e,n,l,o,r,f,d,g,E,Z,H){return t=new Xm(t,e,n,f,d,g,E,H),e=1,r===!0&&(e|=24),r=Ue(3,null,null,e),t.current=r,r.stateNode=t,e=Gr(),e.refCount++,t.pooledCache=e,e.refCount++,r.memoizedState={element:l,isDehydrated:n,cache:e},Qr(r),t}function ad(t){return t?(t=cn,t):cn}function ld(t,e,n,l,o,r){o=ad(o),l.context===null?l.context=o:l.pendingContext=o,l=Wn(e),l.payload={element:n},r=r===void 0?null:r,r!==null&&(l.callback=r),n=In(t,l,e),n!==null&&(ai(n,t,e),fs(n,t,e))}function sd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function uc(t,e){sd(t,e),(t=t.alternate)&&sd(t,e)}function od(t){if(t.tag===13){var e=Vn(t,67108864);e!==null&&ai(e,t,67108864),uc(t,67108864)}}var dr=!0;function Qm(t,e,n,l){var o=D.T;D.T=null;var r=Q.p;try{Q.p=2,cc(t,e,n,l)}finally{Q.p=r,D.T=o}}function Km(t,e,n,l){var o=D.T;D.T=null;var r=Q.p;try{Q.p=8,cc(t,e,n,l)}finally{Q.p=r,D.T=o}}function cc(t,e,n,l){if(dr){var o=fc(l);if(o===null)Iu(t,e,l,mr,n),ud(t,l);else if(Wm(o,t,e,n,l))l.stopPropagation();else if(ud(t,l),e&4&&-1<Jm.indexOf(t)){for(;o!==null;){var r=Vi(o);if(r!==null)switch(r.tag){case 3:if(r=r.stateNode,r.current.memoizedState.isDehydrated){var f=Oi(r.pendingLanes);if(f!==0){var d=r;for(d.pendingLanes|=2,d.entangledLanes|=2;f;){var g=1<<31-De(f);d.entanglements[1]|=g,f&=~g}ki(r),(Nt&6)===0&&(Io=qe()+500,zs(0))}}break;case 13:d=Vn(r,2),d!==null&&ai(d,r,2),Fo(),uc(r,2)}if(r=fc(l),r===null&&Iu(t,e,l,mr,n),r===o)break;o=r}o!==null&&l.stopPropagation()}else Iu(t,e,l,null,n)}}function fc(t){return t=Dn(t),hc(t)}var mr=null;function hc(t){if(mr=null,t=Ni(t),t!==null){var e=q(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=P(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return mr=t,null}function rd(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Xa()){case Vs:return 2;case jl:return 8;case Sn:case Xs:return 32;case Hl:return 268435456;default:return 32}default:return 32}}var dc=!1,ca=null,fa=null,ha=null,Zs=new Map,js=new Map,da=[],Jm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function ud(t,e){switch(t){case"focusin":case"focusout":ca=null;break;case"dragenter":case"dragleave":fa=null;break;case"mouseover":case"mouseout":ha=null;break;case"pointerover":case"pointerout":Zs.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":js.delete(e.pointerId)}}function Hs(t,e,n,l,o,r){return t===null||t.nativeEvent!==r?(t={blockedOn:e,domEventName:n,eventSystemFlags:l,nativeEvent:r,targetContainers:[o]},e!==null&&(e=Vi(e),e!==null&&od(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,o!==null&&e.indexOf(o)===-1&&e.push(o),t)}function Wm(t,e,n,l,o){switch(e){case"focusin":return ca=Hs(ca,t,e,n,l,o),!0;case"dragenter":return fa=Hs(fa,t,e,n,l,o),!0;case"mouseover":return ha=Hs(ha,t,e,n,l,o),!0;case"pointerover":var r=o.pointerId;return Zs.set(r,Hs(Zs.get(r)||null,t,e,n,l,o)),!0;case"gotpointercapture":return r=o.pointerId,js.set(r,Hs(js.get(r)||null,t,e,n,l,o)),!0}return!1}function cd(t){var e=Ni(t.target);if(e!==null){var n=q(e);if(n!==null){if(e=n.tag,e===13){if(e=P(n),e!==null){t.blockedOn=e,kl(t.priority,function(){if(n.tag===13){var l=ni();l=ql(l);var o=Vn(n,l);o!==null&&ai(o,n,l),uc(n,l)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function _r(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=fc(t.nativeEvent);if(n===null){n=t.nativeEvent;var l=new n.constructor(n.type,n);Sa=l,n.target.dispatchEvent(l),Sa=null}else return e=Vi(n),e!==null&&od(e),t.blockedOn=n,!1;e.shift()}return!0}function fd(t,e,n){_r(t)&&n.delete(e)}function Im(){dc=!1,ca!==null&&_r(ca)&&(ca=null),fa!==null&&_r(fa)&&(fa=null),ha!==null&&_r(ha)&&(ha=null),Zs.forEach(fd),js.forEach(fd)}function pr(t,e){t.blockedOn===e&&(t.blockedOn=null,dc||(dc=!0,_.unstable_scheduleCallback(_.unstable_NormalPriority,Im)))}var vr=null;function hd(t){vr!==t&&(vr=t,_.unstable_scheduleCallback(_.unstable_NormalPriority,function(){vr===t&&(vr=null);for(var e=0;e<t.length;e+=3){var n=t[e],l=t[e+1],o=t[e+2];if(typeof l!="function"){if(hc(l||n)===null)continue;break}var r=Vi(n);r!==null&&(t.splice(e,3),e-=3,hu(r,{pending:!0,data:o,method:n.method,action:l},l,o))}}))}function Us(t){function e(g){return pr(g,t)}ca!==null&&pr(ca,t),fa!==null&&pr(fa,t),ha!==null&&pr(ha,t),Zs.forEach(e),js.forEach(e);for(var n=0;n<da.length;n++){var l=da[n];l.blockedOn===t&&(l.blockedOn=null)}for(;0<da.length&&(n=da[0],n.blockedOn===null);)cd(n),n.blockedOn===null&&da.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var o=n[l],r=n[l+1],f=o[ze]||null;if(typeof r=="function")f||hd(n);else if(f){var d=null;if(r&&r.hasAttribute("formAction")){if(o=r,f=r[ze]||null)d=f.formAction;else if(hc(o)!==null)continue}else d=f.action;typeof d=="function"?n[l+1]=d:(n.splice(l,3),l-=3),hd(n)}}}function mc(t){this._internalRoot=t}gr.prototype.render=mc.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(p(409));var n=e.current,l=ni();ld(n,l,t,e,null,null)},gr.prototype.unmount=mc.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;ld(t.current,2,null,t,null,null),Fo(),e[Mn]=null}};function gr(t){this._internalRoot=t}gr.prototype.unstable_scheduleHydration=function(t){if(t){var e=$s();t={blockedOn:null,target:t,priority:e};for(var n=0;n<da.length&&e!==0&&e<da[n].priority;n++);da.splice(n,0,t),n===0&&cd(t)}};var dd=T.version;if(dd!=="19.1.0")throw Error(p(527,dd,"19.1.0"));Q.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(p(188)):(t=Object.keys(t).join(","),Error(p(268,t)));return t=z(e),t=t!==null?A(t):null,t=t===null?null:t.stateNode,t};var $m={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:D,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var yr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!yr.isDisabled&&yr.supportsFiber)try{Yi=yr.inject($m),Le=yr}catch{}}return Ps.createRoot=function(t,e){if(!R(t))throw Error(p(299));var n=!1,l="",o=Of,r=Af,f=Nf,d=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(o=e.onUncaughtError),e.onCaughtError!==void 0&&(r=e.onCaughtError),e.onRecoverableError!==void 0&&(f=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(d=e.unstable_transitionCallbacks)),e=nd(t,1,!1,null,null,n,l,o,r,f,d,null),t[Mn]=e.current,Wu(t),new mc(e)},Ps.hydrateRoot=function(t,e,n){if(!R(t))throw Error(p(299));var l=!1,o="",r=Of,f=Af,d=Nf,g=null,E=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onUncaughtError!==void 0&&(r=n.onUncaughtError),n.onCaughtError!==void 0&&(f=n.onCaughtError),n.onRecoverableError!==void 0&&(d=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(g=n.unstable_transitionCallbacks),n.formState!==void 0&&(E=n.formState)),e=nd(t,1,!0,e,n??null,l,o,r,f,d,g,E),e.context=ad(null),n=e.current,l=ni(),l=ql(l),o=Wn(l),o.callback=null,In(n,o,l),n=l,e.current.lanes=n,wn(e,n),ki(e),t[Mn]=e.current,Wu(t),new gr(e)},Ps.version="19.1.0",Ps}var wd;function r_(){if(wd)return vc.exports;wd=1;function _(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(_)}catch(T){console.error(T)}}return _(),vc.exports=o_(),vc.exports}var u_=r_();/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c_=_=>_.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),f_=_=>_.replace(/^([A-Z])|[\s-_]+(\w)/g,(T,x,p)=>p?p.toUpperCase():x.toLowerCase()),Md=_=>{const T=f_(_);return T.charAt(0).toUpperCase()+T.slice(1)},Rd=(..._)=>_.filter((T,x,p)=>!!T&&T.trim()!==""&&p.indexOf(T)===x).join(" ").trim(),h_=_=>{for(const T in _)if(T.startsWith("aria-")||T==="role"||T==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var d_={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m_=jt.forwardRef(({color:_="currentColor",size:T=24,strokeWidth:x=2,absoluteStrokeWidth:p,className:R="",children:q,iconNode:P,...at},z)=>jt.createElement("svg",{ref:z,...d_,width:T,height:T,stroke:_,strokeWidth:p?Number(x)*24/Number(T):x,className:Rd("lucide",R),...!q&&!h_(at)&&{"aria-hidden":"true"},...at},[...P.map(([A,k])=>jt.createElement(A,k)),...Array.isArray(q)?q:[q]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ee=(_,T)=>{const x=jt.forwardRef(({className:p,...R},q)=>jt.createElement(m_,{ref:q,iconNode:T,className:Rd(`lucide-${c_(Md(_))}`,`lucide-${_}`,p),...R}));return x.displayName=Md(_),x};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const __=[["path",{d:"M16 7h.01",key:"1kdx03"}],["path",{d:"M3.4 18H12a8 8 0 0 0 8-8V7a4 4 0 0 0-7.28-2.3L2 20",key:"oj1oa8"}],["path",{d:"m20 7 2 .5-2 .5",key:"12nv4d"}],["path",{d:"M10 18v3",key:"1yea0a"}],["path",{d:"M14 17.75V21",key:"1pymcb"}],["path",{d:"M7 18a6 6 0 0 0 3.84-10.61",key:"1npnn0"}]],p_=Ee("bird",__);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v_=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],g_=Ee("chart-column",v_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y_=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]],x_=Ee("circle-question-mark",y_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b_=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],S_=Ee("eye-off",b_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T_=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Bd=Ee("eye",T_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w_=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],Ed=Ee("funnel",w_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M_=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],E_=Ee("globe",M_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L_=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]],z_=Ee("info",L_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O_=[["path",{d:"M10 8h.01",key:"1r9ogq"}],["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M14 8h.01",key:"1primd"}],["path",{d:"M16 12h.01",key:"1l6xoz"}],["path",{d:"M18 8h.01",key:"emo2bl"}],["path",{d:"M6 8h.01",key:"x9i8wu"}],["path",{d:"M7 16h10",key:"wp8him"}],["path",{d:"M8 12h.01",key:"czm47f"}],["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}]],A_=Ee("keyboard",O_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N_=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],C_=Ee("loader-circle",N_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D_=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],R_=Ee("map-pin",D_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B_=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],Z_=Ee("menu",B_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j_=[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]],H_=Ee("palette",j_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U_=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],Zd=Ee("search",U_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q_=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],P_=Ee("settings",q_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k_=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],G_=Ee("sparkles",k_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Y_=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],jd=Ee("x",Y_),V_=()=>y.jsxs("header",{className:"relative bg-gradient-to-r from-primary-600 via-primary-500 to-primary-600 shadow-lg",children:[y.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600/20 via-transparent to-blue-600/20"}),y.jsx("div",{className:"absolute inset-0 opacity-10",children:y.jsx("div",{className:"w-full h-full bg-repeat",style:{backgroundImage:`url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`}})}),y.jsx("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:y.jsxs("div",{className:"flex justify-between items-center h-16",children:[y.jsx("div",{className:"flex items-center",children:y.jsxs("div",{className:"flex-shrink-0 flex items-center group",children:[y.jsxs("div",{className:"relative",children:[y.jsx(p_,{className:"h-8 w-8 text-white group-hover:scale-110 transition-transform duration-200"}),y.jsx(G_,{className:"h-3 w-3 text-yellow-300 absolute -top-1 -right-1 animate-pulse"})]}),y.jsxs("span",{className:"ml-3 text-xl font-bold text-white tracking-tight",children:["eBird",y.jsx("span",{className:"text-blue-200 text-sm font-normal ml-1",children:"观鸟热点"})]})]})}),y.jsxs("nav",{className:"hidden md:flex space-x-1",children:[y.jsx("a",{href:"#",className:"text-white/80 hover:text-white hover:bg-white/10 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200",children:"提交记录"}),y.jsx("a",{href:"#",className:"text-white bg-white/20 px-4 py-2 text-sm font-medium rounded-lg backdrop-blur-sm border border-white/30",children:"探索"}),y.jsx("a",{href:"#",className:"text-white/80 hover:text-white hover:bg-white/10 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200",children:"我的eBird"}),y.jsx("a",{href:"#",className:"text-white/80 hover:text-white hover:bg-white/10 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200",children:"科学"}),y.jsx("a",{href:"#",className:"text-white/80 hover:text-white hover:bg-white/10 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200",children:"关于"}),y.jsx("a",{href:"#",className:"text-white/80 hover:text-white hover:bg-white/10 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200",children:"新闻"}),y.jsx("a",{href:"#",className:"text-white/80 hover:text-white hover:bg-white/10 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200",children:"帮助"})]}),y.jsxs("div",{className:"flex items-center space-x-3",children:[y.jsxs("button",{className:"flex items-center text-white/80 hover:text-white hover:bg-white/10 px-3 py-2 rounded-lg transition-all duration-200",children:[y.jsx(E_,{className:"h-4 w-4 mr-2"}),y.jsx("span",{className:"text-sm font-medium",children:"中文"})]}),y.jsx("button",{className:"bg-white text-primary-600 px-6 py-2 rounded-lg text-sm font-semibold hover:bg-blue-50 transition-all duration-200 shadow-soft hover:shadow-medium",children:"登录"}),y.jsx("button",{className:"md:hidden text-white hover:bg-white/10 p-2 rounded-lg transition-all duration-200",children:y.jsx(Z_,{className:"h-6 w-6"})})]})]})})]}),Ld=_=>{let T;const x=new Set,p=(A,k)=>{const W=typeof A=="function"?A(T):A;if(!Object.is(W,T)){const et=T;T=k??(typeof W!="object"||W===null)?W:Object.assign({},T,W),x.forEach(St=>St(T,et))}},R=()=>T,at={setState:p,getState:R,getInitialState:()=>z,subscribe:A=>(x.add(A),()=>x.delete(A))},z=T=_(p,R,at);return at},X_=_=>_?Ld(_):Ld,Q_=_=>_;function K_(_,T=Q_){const x=gd.useSyncExternalStore(_.subscribe,()=>T(_.getState()),()=>T(_.getInitialState()));return gd.useDebugValue(x),x}const zd=_=>{const T=X_(_),x=p=>K_(T,p);return Object.assign(x,T),x},J_=_=>_?zd(_):zd,bc=[{id:"sparrow",name:"麻雀",scientificName:"Passer domesticus",color:"#FF6B6B",description:"常见的城市鸟类",isVisible:!0},{id:"swallow",name:"燕子",scientificName:"Hirundo rustica",color:"#4ECDC4",description:"迁徙性鸟类",isVisible:!0},{id:"magpie",name:"喜鹊",scientificName:"Pica pica",color:"#45B7D1",description:"智慧的鸟类",isVisible:!0},{id:"pigeon",name:"鸽子",scientificName:"Columba livia",color:"#96CEB4",description:"城市适应性强的鸟类",isVisible:!0},{id:"robin",name:"知更鸟",scientificName:"Erithacus rubecula",color:"#FFEAA7",description:"小型鸣禽",isVisible:!0}],W_=()=>{const _=[],T=[{lat:39.9042,lng:116.4074,name:"北京"},{lat:31.2304,lng:121.4737,name:"上海"},{lat:23.1291,lng:113.2644,name:"广州"},{lat:30.5728,lng:104.0668,name:"成都"},{lat:29.563,lng:106.5516,name:"重庆"},{lat:38.0428,lng:114.5149,name:"石家庄"},{lat:36.0611,lng:103.8343,name:"兰州"},{lat:43.8256,lng:87.6168,name:"乌鲁木齐"}];return bc.forEach(x=>{T.forEach(p=>{for(let R=0;R<Math.random()*20+5;R++){const q=(Math.random()-.5)*2,P=(Math.random()-.5)*2;_.push({lat:p.lat+q,lng:p.lng+P,intensity:Math.random(),count:Math.floor(Math.random()*100)+1,speciesId:x.id})}})}),_},xn=J_(_=>({species:[],selectedSpecies:[],searchQuery:"",mapCenter:[39.9042,116.4074],mapZoom:5,selectedMapType:"terrain",hotspots:[],observations:[],setSearchQuery:T=>_({searchQuery:T}),toggleSpeciesVisibility:T=>_(x=>({species:x.species.map(p=>p.id===T?{...p,isVisible:!p.isVisible}:p)})),setSelectedSpecies:T=>_({selectedSpecies:T}),setMapCenter:T=>_({mapCenter:T}),setMapZoom:T=>_({mapZoom:T}),setSelectedMapType:T=>_({selectedMapType:T}),initializeData:()=>{const T=W_();_({species:bc,selectedSpecies:bc.map(x=>x.id),observations:T})}})),I_=()=>{const{searchQuery:_,setSearchQuery:T}=xn();return y.jsx("div",{className:"bg-white/95 backdrop-blur-sm border-b border-gray-200/50 px-4 py-6 shadow-soft",children:y.jsx("div",{className:"max-w-7xl mx-auto",children:y.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-center space-y-4 sm:space-y-0 sm:space-x-8",children:[y.jsxs("div",{className:"flex items-center space-x-3",children:[y.jsx("div",{className:"w-1 h-8 bg-gradient-to-b from-primary-500 to-primary-600 rounded-full"}),y.jsx("h1",{className:"text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"观鸟热点"})]}),y.jsxs("div",{className:"relative group",children:[y.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl blur opacity-20 group-hover:opacity-30 transition-opacity duration-200"}),y.jsxs("div",{className:"relative",children:[y.jsx(Zd,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 group-hover:text-primary-500 transition-colors duration-200"}),y.jsx("input",{type:"text",placeholder:"搜索鸟类物种…",value:_,onChange:x=>T(x.target.value),className:"pl-12 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 w-full sm:w-96 bg-white shadow-soft hover:shadow-medium transition-all duration-200 text-gray-900 placeholder-gray-500"})]})]})]})})})};var ks={exports:{}};/* @preserve
 * Leaflet 1.9.4, a JS library for interactive maps. https://leafletjs.com
 * (c) 2010-2023 Vladimir Agafonkin, (c) 2010-2011 CloudMade
 */var $_=ks.exports,Od;function F_(){return Od||(Od=1,function(_,T){(function(x,p){p(T)})($_,function(x){var p="1.9.4";function R(i){var a,s,u,c;for(s=1,u=arguments.length;s<u;s++){c=arguments[s];for(a in c)i[a]=c[a]}return i}var q=Object.create||function(){function i(){}return function(a){return i.prototype=a,new i}}();function P(i,a){var s=Array.prototype.slice;if(i.bind)return i.bind.apply(i,s.call(arguments,1));var u=s.call(arguments,2);return function(){return i.apply(a,u.length?u.concat(s.call(arguments)):arguments)}}var at=0;function z(i){return"_leaflet_id"in i||(i._leaflet_id=++at),i._leaflet_id}function A(i,a,s){var u,c,h,v;return v=function(){u=!1,c&&(h.apply(s,c),c=!1)},h=function(){u?c=arguments:(i.apply(s,arguments),setTimeout(v,a),u=!0)},h}function k(i,a,s){var u=a[1],c=a[0],h=u-c;return i===u&&s?i:((i-c)%h+h)%h+c}function W(){return!1}function et(i,a){if(a===!1)return i;var s=Math.pow(10,a===void 0?6:a);return Math.round(i*s)/s}function St(i){return i.trim?i.trim():i.replace(/^\s+|\s+$/g,"")}function st(i){return St(i).split(/\s+/)}function rt(i,a){Object.prototype.hasOwnProperty.call(i,"options")||(i.options=i.options?q(i.options):{});for(var s in a)i.options[s]=a[s];return i.options}function Dt(i,a,s){var u=[];for(var c in i)u.push(encodeURIComponent(s?c.toUpperCase():c)+"="+encodeURIComponent(i[c]));return(!a||a.indexOf("?")===-1?"?":"&")+u.join("&")}var ge=/\{ *([\w_ -]+) *\}/g;function $t(i,a){return i.replace(ge,function(s,u){var c=a[u];if(c===void 0)throw new Error("No value provided for variable "+s);return typeof c=="function"&&(c=c(a)),c})}var dt=Array.isArray||function(i){return Object.prototype.toString.call(i)==="[object Array]"};function Lt(i,a){for(var s=0;s<i.length;s++)if(i[s]===a)return s;return-1}var ut="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";function qt(i){return window["webkit"+i]||window["moz"+i]||window["ms"+i]}var Ht=0;function Qt(i){var a=+new Date,s=Math.max(0,16-(a-Ht));return Ht=a+s,window.setTimeout(i,s)}var Ft=window.requestAnimationFrame||qt("RequestAnimationFrame")||Qt,ae=window.cancelAnimationFrame||qt("CancelAnimationFrame")||qt("CancelRequestAnimationFrame")||function(i){window.clearTimeout(i)};function mt(i,a,s){if(s&&Ft===Qt)i.call(a);else return Ft.call(window,P(i,a))}function Mt(i){i&&ae.call(window,i)}var pi={__proto__:null,extend:R,create:q,bind:P,get lastId(){return at},stamp:z,throttle:A,wrapNum:k,falseFn:W,formatNum:et,trim:St,splitWords:st,setOptions:rt,getParamString:Dt,template:$t,isArray:dt,indexOf:Lt,emptyImageUrl:ut,requestFn:Ft,cancelFn:ae,requestAnimFrame:mt,cancelAnimFrame:Mt};function fe(){}fe.extend=function(i){var a=function(){rt(this),this.initialize&&this.initialize.apply(this,arguments),this.callInitHooks()},s=a.__super__=this.prototype,u=q(s);u.constructor=a,a.prototype=u;for(var c in this)Object.prototype.hasOwnProperty.call(this,c)&&c!=="prototype"&&c!=="__super__"&&(a[c]=this[c]);return i.statics&&R(a,i.statics),i.includes&&(le(i.includes),R.apply(null,[u].concat(i.includes))),R(u,i),delete u.statics,delete u.includes,u.options&&(u.options=s.options?q(s.options):{},R(u.options,i.options)),u._initHooks=[],u.callInitHooks=function(){if(!this._initHooksCalled){s.callInitHooks&&s.callInitHooks.call(this),this._initHooksCalled=!0;for(var h=0,v=u._initHooks.length;h<v;h++)u._initHooks[h].call(this)}},a},fe.include=function(i){var a=this.prototype.options;return R(this.prototype,i),i.options&&(this.prototype.options=a,this.mergeOptions(i.options)),this},fe.mergeOptions=function(i){return R(this.prototype.options,i),this},fe.addInitHook=function(i){var a=Array.prototype.slice.call(arguments,1),s=typeof i=="function"?i:function(){this[i].apply(this,a)};return this.prototype._initHooks=this.prototype._initHooks||[],this.prototype._initHooks.push(s),this};function le(i){if(!(typeof L>"u"||!L||!L.Mixin)){i=dt(i)?i:[i];for(var a=0;a<i.length;a++)i[a]===L.Mixin.Events&&console.warn("Deprecated include of L.Mixin.Events: this property will be removed in future releases, please inherit from L.Evented instead.",new Error().stack)}}var D={on:function(i,a,s){if(typeof i=="object")for(var u in i)this._on(u,i[u],a);else{i=st(i);for(var c=0,h=i.length;c<h;c++)this._on(i[c],a,s)}return this},off:function(i,a,s){if(!arguments.length)delete this._events;else if(typeof i=="object")for(var u in i)this._off(u,i[u],a);else{i=st(i);for(var c=arguments.length===1,h=0,v=i.length;h<v;h++)c?this._off(i[h]):this._off(i[h],a,s)}return this},_on:function(i,a,s,u){if(typeof a!="function"){console.warn("wrong listener type: "+typeof a);return}if(this._listens(i,a,s)===!1){s===this&&(s=void 0);var c={fn:a,ctx:s};u&&(c.once=!0),this._events=this._events||{},this._events[i]=this._events[i]||[],this._events[i].push(c)}},_off:function(i,a,s){var u,c,h;if(this._events&&(u=this._events[i],!!u)){if(arguments.length===1){if(this._firingCount)for(c=0,h=u.length;c<h;c++)u[c].fn=W;delete this._events[i];return}if(typeof a!="function"){console.warn("wrong listener type: "+typeof a);return}var v=this._listens(i,a,s);if(v!==!1){var w=u[v];this._firingCount&&(w.fn=W,this._events[i]=u=u.slice()),u.splice(v,1)}}},fire:function(i,a,s){if(!this.listens(i,s))return this;var u=R({},a,{type:i,target:this,sourceTarget:a&&a.sourceTarget||this});if(this._events){var c=this._events[i];if(c){this._firingCount=this._firingCount+1||1;for(var h=0,v=c.length;h<v;h++){var w=c[h],C=w.fn;w.once&&this.off(i,C,w.ctx),C.call(w.ctx||this,u)}this._firingCount--}}return s&&this._propagateEvent(u),this},listens:function(i,a,s,u){typeof i!="string"&&console.warn('"string" type argument expected');var c=a;typeof a!="function"&&(u=!!a,c=void 0,s=void 0);var h=this._events&&this._events[i];if(h&&h.length&&this._listens(i,c,s)!==!1)return!0;if(u){for(var v in this._eventParents)if(this._eventParents[v].listens(i,a,s,u))return!0}return!1},_listens:function(i,a,s){if(!this._events)return!1;var u=this._events[i]||[];if(!a)return!!u.length;s===this&&(s=void 0);for(var c=0,h=u.length;c<h;c++)if(u[c].fn===a&&u[c].ctx===s)return c;return!1},once:function(i,a,s){if(typeof i=="object")for(var u in i)this._on(u,i[u],a,!0);else{i=st(i);for(var c=0,h=i.length;c<h;c++)this._on(i[c],a,s,!0)}return this},addEventParent:function(i){return this._eventParents=this._eventParents||{},this._eventParents[z(i)]=i,this},removeEventParent:function(i){return this._eventParents&&delete this._eventParents[z(i)],this},_propagateEvent:function(i){for(var a in this._eventParents)this._eventParents[a].fire(i.type,R({layer:i.target,propagatedFrom:i.target},i),!0)}};D.addEventListener=D.on,D.removeEventListener=D.clearAllEventListeners=D.off,D.addOneTimeEventListener=D.once,D.fireEvent=D.fire,D.hasEventListeners=D.listens;var Q=fe.extend(D);function G(i,a,s){this.x=s?Math.round(i):i,this.y=s?Math.round(a):a}var At=Math.trunc||function(i){return i>0?Math.floor(i):Math.ceil(i)};G.prototype={clone:function(){return new G(this.x,this.y)},add:function(i){return this.clone()._add(m(i))},_add:function(i){return this.x+=i.x,this.y+=i.y,this},subtract:function(i){return this.clone()._subtract(m(i))},_subtract:function(i){return this.x-=i.x,this.y-=i.y,this},divideBy:function(i){return this.clone()._divideBy(i)},_divideBy:function(i){return this.x/=i,this.y/=i,this},multiplyBy:function(i){return this.clone()._multiplyBy(i)},_multiplyBy:function(i){return this.x*=i,this.y*=i,this},scaleBy:function(i){return new G(this.x*i.x,this.y*i.y)},unscaleBy:function(i){return new G(this.x/i.x,this.y/i.y)},round:function(){return this.clone()._round()},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},floor:function(){return this.clone()._floor()},_floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.clone()._ceil()},_ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},trunc:function(){return this.clone()._trunc()},_trunc:function(){return this.x=At(this.x),this.y=At(this.y),this},distanceTo:function(i){i=m(i);var a=i.x-this.x,s=i.y-this.y;return Math.sqrt(a*a+s*s)},equals:function(i){return i=m(i),i.x===this.x&&i.y===this.y},contains:function(i){return i=m(i),Math.abs(i.x)<=Math.abs(this.x)&&Math.abs(i.y)<=Math.abs(this.y)},toString:function(){return"Point("+et(this.x)+", "+et(this.y)+")"}};function m(i,a,s){return i instanceof G?i:dt(i)?new G(i[0],i[1]):i==null?i:typeof i=="object"&&"x"in i&&"y"in i?new G(i.x,i.y):new G(i,a,s)}function B(i,a){if(i)for(var s=a?[i,a]:i,u=0,c=s.length;u<c;u++)this.extend(s[u])}B.prototype={extend:function(i){var a,s;if(!i)return this;if(i instanceof G||typeof i[0]=="number"||"x"in i)a=s=m(i);else if(i=V(i),a=i.min,s=i.max,!a||!s)return this;return!this.min&&!this.max?(this.min=a.clone(),this.max=s.clone()):(this.min.x=Math.min(a.x,this.min.x),this.max.x=Math.max(s.x,this.max.x),this.min.y=Math.min(a.y,this.min.y),this.max.y=Math.max(s.y,this.max.y)),this},getCenter:function(i){return m((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,i)},getBottomLeft:function(){return m(this.min.x,this.max.y)},getTopRight:function(){return m(this.max.x,this.min.y)},getTopLeft:function(){return this.min},getBottomRight:function(){return this.max},getSize:function(){return this.max.subtract(this.min)},contains:function(i){var a,s;return typeof i[0]=="number"||i instanceof G?i=m(i):i=V(i),i instanceof B?(a=i.min,s=i.max):a=s=i,a.x>=this.min.x&&s.x<=this.max.x&&a.y>=this.min.y&&s.y<=this.max.y},intersects:function(i){i=V(i);var a=this.min,s=this.max,u=i.min,c=i.max,h=c.x>=a.x&&u.x<=s.x,v=c.y>=a.y&&u.y<=s.y;return h&&v},overlaps:function(i){i=V(i);var a=this.min,s=this.max,u=i.min,c=i.max,h=c.x>a.x&&u.x<s.x,v=c.y>a.y&&u.y<s.y;return h&&v},isValid:function(){return!!(this.min&&this.max)},pad:function(i){var a=this.min,s=this.max,u=Math.abs(a.x-s.x)*i,c=Math.abs(a.y-s.y)*i;return V(m(a.x-u,a.y-c),m(s.x+u,s.y+c))},equals:function(i){return i?(i=V(i),this.min.equals(i.getTopLeft())&&this.max.equals(i.getBottomRight())):!1}};function V(i,a){return!i||i instanceof B?i:new B(i,a)}function Y(i,a){if(i)for(var s=a?[i,a]:i,u=0,c=s.length;u<c;u++)this.extend(s[u])}Y.prototype={extend:function(i){var a=this._southWest,s=this._northEast,u,c;if(i instanceof F)u=i,c=i;else if(i instanceof Y){if(u=i._southWest,c=i._northEast,!u||!c)return this}else return i?this.extend(X(i)||J(i)):this;return!a&&!s?(this._southWest=new F(u.lat,u.lng),this._northEast=new F(c.lat,c.lng)):(a.lat=Math.min(u.lat,a.lat),a.lng=Math.min(u.lng,a.lng),s.lat=Math.max(c.lat,s.lat),s.lng=Math.max(c.lng,s.lng)),this},pad:function(i){var a=this._southWest,s=this._northEast,u=Math.abs(a.lat-s.lat)*i,c=Math.abs(a.lng-s.lng)*i;return new Y(new F(a.lat-u,a.lng-c),new F(s.lat+u,s.lng+c))},getCenter:function(){return new F((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)},getSouthWest:function(){return this._southWest},getNorthEast:function(){return this._northEast},getNorthWest:function(){return new F(this.getNorth(),this.getWest())},getSouthEast:function(){return new F(this.getSouth(),this.getEast())},getWest:function(){return this._southWest.lng},getSouth:function(){return this._southWest.lat},getEast:function(){return this._northEast.lng},getNorth:function(){return this._northEast.lat},contains:function(i){typeof i[0]=="number"||i instanceof F||"lat"in i?i=X(i):i=J(i);var a=this._southWest,s=this._northEast,u,c;return i instanceof Y?(u=i.getSouthWest(),c=i.getNorthEast()):u=c=i,u.lat>=a.lat&&c.lat<=s.lat&&u.lng>=a.lng&&c.lng<=s.lng},intersects:function(i){i=J(i);var a=this._southWest,s=this._northEast,u=i.getSouthWest(),c=i.getNorthEast(),h=c.lat>=a.lat&&u.lat<=s.lat,v=c.lng>=a.lng&&u.lng<=s.lng;return h&&v},overlaps:function(i){i=J(i);var a=this._southWest,s=this._northEast,u=i.getSouthWest(),c=i.getNorthEast(),h=c.lat>a.lat&&u.lat<s.lat,v=c.lng>a.lng&&u.lng<s.lng;return h&&v},toBBoxString:function(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")},equals:function(i,a){return i?(i=J(i),this._southWest.equals(i.getSouthWest(),a)&&this._northEast.equals(i.getNorthEast(),a)):!1},isValid:function(){return!!(this._southWest&&this._northEast)}};function J(i,a){return i instanceof Y?i:new Y(i,a)}function F(i,a,s){if(isNaN(i)||isNaN(a))throw new Error("Invalid LatLng object: ("+i+", "+a+")");this.lat=+i,this.lng=+a,s!==void 0&&(this.alt=+s)}F.prototype={equals:function(i,a){if(!i)return!1;i=X(i);var s=Math.max(Math.abs(this.lat-i.lat),Math.abs(this.lng-i.lng));return s<=(a===void 0?1e-9:a)},toString:function(i){return"LatLng("+et(this.lat,i)+", "+et(this.lng,i)+")"},distanceTo:function(i){return Et.distance(this,X(i))},wrap:function(){return Et.wrapLatLng(this)},toBounds:function(i){var a=180*i/40075017,s=a/Math.cos(Math.PI/180*this.lat);return J([this.lat-a,this.lng-s],[this.lat+a,this.lng+s])},clone:function(){return new F(this.lat,this.lng,this.alt)}};function X(i,a,s){return i instanceof F?i:dt(i)&&typeof i[0]!="object"?i.length===3?new F(i[0],i[1],i[2]):i.length===2?new F(i[0],i[1]):null:i==null?i:typeof i=="object"&&"lat"in i?new F(i.lat,"lng"in i?i.lng:i.lon,i.alt):a===void 0?null:new F(i,a,s)}var Kt={latLngToPoint:function(i,a){var s=this.projection.project(i),u=this.scale(a);return this.transformation._transform(s,u)},pointToLatLng:function(i,a){var s=this.scale(a),u=this.transformation.untransform(i,s);return this.projection.unproject(u)},project:function(i){return this.projection.project(i)},unproject:function(i){return this.projection.unproject(i)},scale:function(i){return 256*Math.pow(2,i)},zoom:function(i){return Math.log(i/256)/Math.LN2},getProjectedBounds:function(i){if(this.infinite)return null;var a=this.projection.bounds,s=this.scale(i),u=this.transformation.transform(a.min,s),c=this.transformation.transform(a.max,s);return new B(u,c)},infinite:!1,wrapLatLng:function(i){var a=this.wrapLng?k(i.lng,this.wrapLng,!0):i.lng,s=this.wrapLat?k(i.lat,this.wrapLat,!0):i.lat,u=i.alt;return new F(s,a,u)},wrapLatLngBounds:function(i){var a=i.getCenter(),s=this.wrapLatLng(a),u=a.lat-s.lat,c=a.lng-s.lng;if(u===0&&c===0)return i;var h=i.getSouthWest(),v=i.getNorthEast(),w=new F(h.lat-u,h.lng-c),C=new F(v.lat-u,v.lng-c);return new Y(w,C)}},Et=R({},Kt,{wrapLng:[-180,180],R:6371e3,distance:function(i,a){var s=Math.PI/180,u=i.lat*s,c=a.lat*s,h=Math.sin((a.lat-i.lat)*s/2),v=Math.sin((a.lng-i.lng)*s/2),w=h*h+Math.cos(u)*Math.cos(c)*v*v,C=2*Math.atan2(Math.sqrt(w),Math.sqrt(1-w));return this.R*C}}),vi=6378137,_a={R:vi,MAX_LATITUDE:85.0511287798,project:function(i){var a=Math.PI/180,s=this.MAX_LATITUDE,u=Math.max(Math.min(s,i.lat),-s),c=Math.sin(u*a);return new G(this.R*i.lng*a,this.R*Math.log((1+c)/(1-c))/2)},unproject:function(i){var a=180/Math.PI;return new F((2*Math.atan(Math.exp(i.y/this.R))-Math.PI/2)*a,i.x*a/this.R)},bounds:function(){var i=vi*Math.PI;return new B([-i,-i],[i,i])}()};function bn(i,a,s,u){if(dt(i)){this._a=i[0],this._b=i[1],this._c=i[2],this._d=i[3];return}this._a=i,this._b=a,this._c=s,this._d=u}bn.prototype={transform:function(i,a){return this._transform(i.clone(),a)},_transform:function(i,a){return a=a||1,i.x=a*(this._a*i.x+this._b),i.y=a*(this._c*i.y+this._d),i},untransform:function(i,a){return a=a||1,new G((i.x/a-this._b)/this._a,(i.y/a-this._d)/this._c)}};function Gi(i,a,s,u){return new bn(i,a,s,u)}var pa=R({},Et,{code:"EPSG:3857",projection:_a,transformation:function(){var i=.5/(Math.PI*_a.R);return Gi(i,.5,-i,.5)}()}),Zl=R({},pa,{code:"EPSG:900913"});function Gs(i){return document.createElementNS("http://www.w3.org/2000/svg",i)}function Ys(i,a){var s="",u,c,h,v,w,C;for(u=0,h=i.length;u<h;u++){for(w=i[u],c=0,v=w.length;c<v;c++)C=w[c],s+=(c?"L":"M")+C.x+" "+C.y;s+=a?it.svg?"z":"x":""}return s||"M0 0"}var qe=document.documentElement.style,Xa="ActiveXObject"in window,Vs=Xa&&!document.addEventListener,jl="msLaunchUri"in navigator&&!("documentMode"in document),Sn=Pe("webkit"),Xs=Pe("android"),Hl=Pe("android 2")||Pe("android 3"),xr=parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1],10),br=Xs&&Pe("Google")&&xr<537&&!("AudioNode"in window),Yi=!!window.opera,Le=!jl&&Pe("chrome"),gi=Pe("gecko")&&!Sn&&!Yi&&!Xa,De=!Le&&Pe("safari"),Qs=Pe("phantom"),Ks="OTransition"in qe,Sr=navigator.platform.indexOf("Win")===0,va=Xa&&"transition"in qe,Tn="WebKitCSSMatrix"in window&&"m11"in new window.WebKitCSSMatrix&&!Hl,Oi="MozPerspective"in qe,Qa=!window.L_DISABLE_3D&&(va||Tn||Oi)&&!Ks&&!Qs,yi=typeof orientation<"u"||Pe("mobile"),Tr=yi&&Sn,Js=yi&&Tn,Ul=!window.PointerEvent&&window.MSPointerEvent,Ka=!!(window.PointerEvent||Ul),wn="ontouchstart"in window||!!window.TouchEvent,wr=!window.L_NO_TOUCH&&(wn||Ka),Ws=yi&&Yi,Is=yi&&gi,ql=(window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI)>1,Pl=function(){var i=!1;try{var a=Object.defineProperty({},"passive",{get:function(){i=!0}});window.addEventListener("testPassiveEventSupport",W,a),window.removeEventListener("testPassiveEventSupport",W,a)}catch{}return i}(),$s=function(){return!!document.createElement("canvas").getContext}(),kl=!!(document.createElementNS&&Gs("svg").createSVGRect),Ai=!!kl&&function(){var i=document.createElement("div");return i.innerHTML="<svg/>",(i.firstChild&&i.firstChild.namespaceURI)==="http://www.w3.org/2000/svg"}(),he=!kl&&function(){try{var i=document.createElement("div");i.innerHTML='<v:shape adj="1"/>';var a=i.firstChild;return a.style.behavior="url(#default#VML)",a&&typeof a.adj=="object"}catch{return!1}}(),ze=navigator.platform.indexOf("Mac")===0,Mn=navigator.platform.indexOf("Linux")===0;function Pe(i){return navigator.userAgent.toLowerCase().indexOf(i)>=0}var it={ie:Xa,ielt9:Vs,edge:jl,webkit:Sn,android:Xs,android23:Hl,androidStock:br,opera:Yi,chrome:Le,gecko:gi,safari:De,phantom:Qs,opera12:Ks,win:Sr,ie3d:va,webkit3d:Tn,gecko3d:Oi,any3d:Qa,mobile:yi,mobileWebkit:Tr,mobileWebkit3d:Js,msPointer:Ul,pointer:Ka,touch:wr,touchNative:wn,mobileOpera:Ws,mobileGecko:Is,retina:ql,passiveEvents:Pl,canvas:$s,svg:kl,vml:he,inlineSvg:Ai,mac:ze,linux:Mn},Fs=it.msPointer?"MSPointerDown":"pointerdown",Gl=it.msPointer?"MSPointerMove":"pointermove",En=it.msPointer?"MSPointerUp":"pointerup",Ja=it.msPointer?"MSPointerCancel":"pointercancel",Ni={touchstart:Fs,touchmove:Gl,touchend:En,touchcancel:Ja},Vi={touchstart:io,touchmove:ga,touchend:ga,touchcancel:ga},li={},Xi=!1;function se(i,a,s){return a==="touchstart"&&Mr(),Vi[a]?(s=Vi[a].bind(this,s),i.addEventListener(Ni[a],s,!1),s):(console.warn("wrong event specified:",a),W)}function to(i,a,s){if(!Ni[a]){console.warn("wrong event specified:",a);return}i.removeEventListener(Ni[a],s,!1)}function eo(i){li[i.pointerId]=i}function Qi(i){li[i.pointerId]&&(li[i.pointerId]=i)}function Ki(i){delete li[i.pointerId]}function Mr(){Xi||(document.addEventListener(Fs,eo,!0),document.addEventListener(Gl,Qi,!0),document.addEventListener(En,Ki,!0),document.addEventListener(Ja,Ki,!0),Xi=!0)}function ga(i,a){if(a.pointerType!==(a.MSPOINTER_TYPE_MOUSE||"mouse")){a.touches=[];for(var s in li)a.touches.push(li[s]);a.changedTouches=[a],i(a)}}function io(i,a){a.MSPOINTER_TYPE_TOUCH&&a.pointerType===a.MSPOINTER_TYPE_TOUCH&&te(a),ga(i,a)}function Er(i){var a={},s,u;for(u in i)s=i[u],a[u]=s&&s.bind?s.bind(i):s;return i=a,a.type="dblclick",a.detail=2,a.isTrusted=!1,a._simulated=!0,a}var Wa=200;function Ia(i,a){i.addEventListener("dblclick",a);var s=0,u;function c(h){if(h.detail!==1){u=h.detail;return}if(!(h.pointerType==="mouse"||h.sourceCapabilities&&!h.sourceCapabilities.firesTouchEvents)){var v=Xl(h);if(!(v.some(function(C){return C instanceof HTMLLabelElement&&C.attributes.for})&&!v.some(function(C){return C instanceof HTMLInputElement||C instanceof HTMLSelectElement}))){var w=Date.now();w-s<=Wa?(u++,u===2&&a(Er(h))):u=1,s=w}}}return i.addEventListener("click",c),{dblclick:a,simDblclick:c}}function xi(i,a){i.removeEventListener("dblclick",a.dblclick),i.removeEventListener("click",a.simDblclick)}var ya=An(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),Ln=An(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),Ji=Ln==="webkitTransition"||Ln==="OTransition"?Ln+"End":"transitionend";function $a(i){return typeof i=="string"?document.getElementById(i):i}function Wi(i,a){var s=i.style[a]||i.currentStyle&&i.currentStyle[a];if((!s||s==="auto")&&document.defaultView){var u=document.defaultView.getComputedStyle(i,null);s=u?u[a]:null}return s==="auto"?null:s}function Tt(i,a,s){var u=document.createElement(i);return u.className=a||"",s&&s.appendChild(u),u}function Pt(i){var a=i.parentNode;a&&a.removeChild(i)}function ye(i){for(;i.firstChild;)i.removeChild(i.firstChild)}function Ii(i){var a=i.parentNode;a&&a.lastChild!==i&&a.appendChild(i)}function zn(i){var a=i.parentNode;a&&a.firstChild!==i&&a.insertBefore(i,a.firstChild)}function On(i,a){if(i.classList!==void 0)return i.classList.contains(a);var s=xe(i);return s.length>0&&new RegExp("(^|\\s)"+a+"(\\s|$)").test(s)}function _t(i,a){if(i.classList!==void 0)for(var s=st(a),u=0,c=s.length;u<c;u++)i.classList.add(s[u]);else if(!On(i,a)){var h=xe(i);Yl(i,(h?h+" ":"")+a)}}function kt(i,a){i.classList!==void 0?i.classList.remove(a):Yl(i,St((" "+xe(i)+" ").replace(" "+a+" "," ")))}function Yl(i,a){i.className.baseVal===void 0?i.className=a:i.className.baseVal=a}function xe(i){return i.correspondingElement&&(i=i.correspondingElement),i.className.baseVal===void 0?i.className:i.className.baseVal}function Oe(i,a){"opacity"in i.style?i.style.opacity=a:"filter"in i.style&&no(i,a)}function no(i,a){var s=!1,u="DXImageTransform.Microsoft.Alpha";try{s=i.filters.item(u)}catch{if(a===1)return}a=Math.round(a*100),s?(s.Enabled=a!==100,s.Opacity=a):i.style.filter+=" progid:"+u+"(opacity="+a+")"}function An(i){for(var a=document.documentElement.style,s=0;s<i.length;s++)if(i[s]in a)return i[s];return!1}function ke(i,a,s){var u=a||new G(0,0);i.style[ya]=(it.ie3d?"translate("+u.x+"px,"+u.y+"px)":"translate3d("+u.x+"px,"+u.y+"px,0)")+(s?" scale("+s+")":"")}function Jt(i,a){i._leaflet_pos=a,it.any3d?ke(i,a):(i.style.left=a.x+"px",i.style.top=a.y+"px")}function Ci(i){return i._leaflet_pos||new G(0,0)}var si,xa,Fa;if("onselectstart"in document)si=function(){ht(window,"selectstart",te)},xa=function(){zt(window,"selectstart",te)};else{var Nn=An(["userSelect","WebkitUserSelect","OUserSelect","MozUserSelect","msUserSelect"]);si=function(){if(Nn){var i=document.documentElement.style;Fa=i[Nn],i[Nn]="none"}},xa=function(){Nn&&(document.documentElement.style[Nn]=Fa,Fa=void 0)}}function ba(){ht(window,"dragstart",te)}function Vl(){zt(window,"dragstart",te)}var tl,Cn;function Sa(i){for(;i.tabIndex===-1;)i=i.parentNode;i.style&&(Dn(),tl=i,Cn=i.style.outlineStyle,i.style.outlineStyle="none",ht(window,"keydown",Dn))}function Dn(){tl&&(tl.style.outlineStyle=Cn,tl=void 0,Cn=void 0,zt(window,"keydown",Dn))}function $i(i){do i=i.parentNode;while((!i.offsetWidth||!i.offsetHeight)&&i!==document.body);return i}function Di(i){var a=i.getBoundingClientRect();return{x:a.width/i.offsetWidth||1,y:a.height/i.offsetHeight||1,boundingClientRect:a}}var ao={__proto__:null,TRANSFORM:ya,TRANSITION:Ln,TRANSITION_END:Ji,get:$a,getStyle:Wi,create:Tt,remove:Pt,empty:ye,toFront:Ii,toBack:zn,hasClass:On,addClass:_t,removeClass:kt,setClass:Yl,getClass:xe,setOpacity:Oe,testProp:An,setTransform:ke,setPosition:Jt,getPosition:Ci,get disableTextSelection(){return si},get enableTextSelection(){return xa},disableImageDrag:ba,enableImageDrag:Vl,preventOutline:Sa,restoreOutline:Dn,getSizedParentNode:$i,getScale:Di};function ht(i,a,s,u){if(a&&typeof a=="object")for(var c in a)Fi(i,c,a[c],s);else{a=st(a);for(var h=0,v=a.length;h<v;h++)Fi(i,a[h],s,u)}return this}var Qe="_leaflet_events";function zt(i,a,s,u){if(arguments.length===1)oi(i),delete i[Qe];else if(a&&typeof a=="object")for(var c in a)ri(i,c,a[c],s);else if(a=st(a),arguments.length===2)oi(i,function(w){return Lt(a,w)!==-1});else for(var h=0,v=a.length;h<v;h++)ri(i,a[h],s,u);return this}function oi(i,a){for(var s in i[Qe]){var u=s.split(/\d/)[0];(!a||a(u))&&ri(i,u,null,null,s)}}var Ta={mouseenter:"mouseover",mouseleave:"mouseout",wheel:!("onwheel"in window)&&"mousewheel"};function Fi(i,a,s,u){var c=a+z(s)+(u?"_"+z(u):"");if(i[Qe]&&i[Qe][c])return this;var h=function(w){return s.call(u||i,w||window.event)},v=h;!it.touchNative&&it.pointer&&a.indexOf("touch")===0?h=se(i,a,h):it.touch&&a==="dblclick"?h=Ia(i,h):"addEventListener"in i?a==="touchstart"||a==="touchmove"||a==="wheel"||a==="mousewheel"?i.addEventListener(Ta[a]||a,h,it.passiveEvents?{passive:!1}:!1):a==="mouseenter"||a==="mouseleave"?(h=function(w){w=w||window.event,en(i,w)&&v(w)},i.addEventListener(Ta[a],h,!1)):i.addEventListener(a,v,!1):i.attachEvent("on"+a,h),i[Qe]=i[Qe]||{},i[Qe][c]=h}function ri(i,a,s,u,c){c=c||a+z(s)+(u?"_"+z(u):"");var h=i[Qe]&&i[Qe][c];if(!h)return this;!it.touchNative&&it.pointer&&a.indexOf("touch")===0?to(i,a,h):it.touch&&a==="dblclick"?xi(i,h):"removeEventListener"in i?i.removeEventListener(Ta[a]||a,h,!1):i.detachEvent("on"+a,h),i[Qe][c]=null}function bi(i){return i.stopPropagation?i.stopPropagation():i.originalEvent?i.originalEvent._stopped=!0:i.cancelBubble=!0,this}function Rn(i){return Fi(i,"wheel",bi),this}function Bn(i){return ht(i,"mousedown touchstart dblclick contextmenu",bi),i._leaflet_disable_click=!0,this}function te(i){return i.preventDefault?i.preventDefault():i.returnValue=!1,this}function ui(i){return te(i),bi(i),this}function Xl(i){if(i.composedPath)return i.composedPath();for(var a=[],s=i.target;s;)a.push(s),s=s.parentNode;return a}function be(i,a){if(!a)return new G(i.clientX,i.clientY);var s=Di(a),u=s.boundingClientRect;return new G((i.clientX-u.left)/s.x-a.clientLeft,(i.clientY-u.top)/s.y-a.clientTop)}var tn=it.linux&&it.chrome?window.devicePixelRatio:it.mac?window.devicePixelRatio*3:window.devicePixelRatio>0?2*window.devicePixelRatio:1;function wa(i){return it.edge?i.wheelDeltaY/2:i.deltaY&&i.deltaMode===0?-i.deltaY/tn:i.deltaY&&i.deltaMode===1?-i.deltaY*20:i.deltaY&&i.deltaMode===2?-i.deltaY*60:i.deltaX||i.deltaZ?0:i.wheelDelta?(i.wheelDeltaY||i.wheelDelta)/2:i.detail&&Math.abs(i.detail)<32765?-i.detail*20:i.detail?i.detail/-32765*60:0}function en(i,a){var s=a.relatedTarget;if(!s)return!0;try{for(;s&&s!==i;)s=s.parentNode}catch{return!1}return s!==i}var Lr={__proto__:null,on:ht,off:zt,stopPropagation:bi,disableScrollPropagation:Rn,disableClickPropagation:Bn,preventDefault:te,stop:ui,getPropagationPath:Xl,getMousePosition:be,getWheelDelta:wa,isExternalTarget:en,addListener:ht,removeListener:zt},el=Q.extend({run:function(i,a,s,u){this.stop(),this._el=i,this._inProgress=!0,this._duration=s||.25,this._easeOutPower=1/Math.max(u||.5,.2),this._startPos=Ci(i),this._offset=a.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop:function(){this._inProgress&&(this._step(!0),this._complete())},_animate:function(){this._animId=mt(this._animate,this),this._step()},_step:function(i){var a=+new Date-this._startTime,s=this._duration*1e3;a<s?this._runFrame(this._easeOut(a/s),i):(this._runFrame(1),this._complete())},_runFrame:function(i,a){var s=this._startPos.add(this._offset.multiplyBy(i));a&&s._round(),Jt(this._el,s),this.fire("step")},_complete:function(){Mt(this._animId),this._inProgress=!1,this.fire("end")},_easeOut:function(i){return 1-Math.pow(1-i,this._easeOutPower)}}),gt=Q.extend({options:{crs:pa,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize:function(i,a){a=rt(this,a),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this._initContainer(i),this._initLayout(),this._onResize=P(this._onResize,this),this._initEvents(),a.maxBounds&&this.setMaxBounds(a.maxBounds),a.zoom!==void 0&&(this._zoom=this._limitZoom(a.zoom)),a.center&&a.zoom!==void 0&&this.setView(X(a.center),a.zoom,{reset:!0}),this.callInitHooks(),this._zoomAnimated=Ln&&it.any3d&&!it.mobileOpera&&this.options.zoomAnimation,this._zoomAnimated&&(this._createAnimProxy(),ht(this._proxy,Ji,this._catchTransitionEnd,this)),this._addLayers(this.options.layers)},setView:function(i,a,s){if(a=a===void 0?this._zoom:this._limitZoom(a),i=this._limitCenter(X(i),a,this.options.maxBounds),s=s||{},this._stop(),this._loaded&&!s.reset&&s!==!0){s.animate!==void 0&&(s.zoom=R({animate:s.animate},s.zoom),s.pan=R({animate:s.animate,duration:s.duration},s.pan));var u=this._zoom!==a?this._tryAnimatedZoom&&this._tryAnimatedZoom(i,a,s.zoom):this._tryAnimatedPan(i,s.pan);if(u)return clearTimeout(this._sizeTimer),this}return this._resetView(i,a,s.pan&&s.pan.noMoveStart),this},setZoom:function(i,a){return this._loaded?this.setView(this.getCenter(),i,{zoom:a}):(this._zoom=i,this)},zoomIn:function(i,a){return i=i||(it.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom+i,a)},zoomOut:function(i,a){return i=i||(it.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom-i,a)},setZoomAround:function(i,a,s){var u=this.getZoomScale(a),c=this.getSize().divideBy(2),h=i instanceof G?i:this.latLngToContainerPoint(i),v=h.subtract(c).multiplyBy(1-1/u),w=this.containerPointToLatLng(c.add(v));return this.setView(w,a,{zoom:s})},_getBoundsCenterZoom:function(i,a){a=a||{},i=i.getBounds?i.getBounds():J(i);var s=m(a.paddingTopLeft||a.padding||[0,0]),u=m(a.paddingBottomRight||a.padding||[0,0]),c=this.getBoundsZoom(i,!1,s.add(u));if(c=typeof a.maxZoom=="number"?Math.min(a.maxZoom,c):c,c===1/0)return{center:i.getCenter(),zoom:c};var h=u.subtract(s).divideBy(2),v=this.project(i.getSouthWest(),c),w=this.project(i.getNorthEast(),c),C=this.unproject(v.add(w).divideBy(2).add(h),c);return{center:C,zoom:c}},fitBounds:function(i,a){if(i=J(i),!i.isValid())throw new Error("Bounds are not valid.");var s=this._getBoundsCenterZoom(i,a);return this.setView(s.center,s.zoom,a)},fitWorld:function(i){return this.fitBounds([[-90,-180],[90,180]],i)},panTo:function(i,a){return this.setView(i,this._zoom,{pan:a})},panBy:function(i,a){if(i=m(i).round(),a=a||{},!i.x&&!i.y)return this.fire("moveend");if(a.animate!==!0&&!this.getSize().contains(i))return this._resetView(this.unproject(this.project(this.getCenter()).add(i)),this.getZoom()),this;if(this._panAnim||(this._panAnim=new el,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),a.noMoveStart||this.fire("movestart"),a.animate!==!1){_t(this._mapPane,"leaflet-pan-anim");var s=this._getMapPanePos().subtract(i).round();this._panAnim.run(this._mapPane,s,a.duration||.25,a.easeLinearity)}else this._rawPanBy(i),this.fire("move").fire("moveend");return this},flyTo:function(i,a,s){if(s=s||{},s.animate===!1||!it.any3d)return this.setView(i,a,s);this._stop();var u=this.project(this.getCenter()),c=this.project(i),h=this.getSize(),v=this._zoom;i=X(i),a=a===void 0?v:a;var w=Math.max(h.x,h.y),C=w*this.getZoomScale(v,a),U=c.distanceTo(u)||1,K=1.42,I=K*K;function tt(Vt){var hi=Vt?-1:1,wi=Vt?C:w,Hi=C*C-w*w+hi*I*I*U*U,Mi=2*wi*I*U,ja=Hi/Mi,ml=Math.sqrt(ja*ja+1)-ja,Ha=ml<1e-9?-18:Math.log(ml);return Ha}function ft(Vt){return(Math.exp(Vt)-Math.exp(-Vt))/2}function Gt(Vt){return(Math.exp(Vt)+Math.exp(-Vt))/2}function Wt(Vt){return ft(Vt)/Gt(Vt)}var me=tt(0);function Ge(Vt){return w*(Gt(me)/Gt(me+K*Vt))}function wo(Vt){return w*(Gt(me)*Wt(me+K*Vt)-ft(me))/I}function Mo(Vt){return 1-Math.pow(1-Vt,1.5)}var Za=Date.now(),Qn=(tt(1)-me)/K,Eo=s.duration?1e3*s.duration:1e3*Qn*.8;function Kn(){var Vt=(Date.now()-Za)/Eo,hi=Mo(Vt)*Qn;Vt<=1?(this._flyToFrame=mt(Kn,this),this._move(this.unproject(u.add(c.subtract(u).multiplyBy(wo(hi)/U)),v),this.getScaleZoom(w/Ge(hi),v),{flyTo:!0})):this._move(i,a)._moveEnd(!0)}return this._moveStart(!0,s.noMoveStart),Kn.call(this),this},flyToBounds:function(i,a){var s=this._getBoundsCenterZoom(i,a);return this.flyTo(s.center,s.zoom,a)},setMaxBounds:function(i){return i=J(i),this.listens("moveend",this._panInsideMaxBounds)&&this.off("moveend",this._panInsideMaxBounds),i.isValid()?(this.options.maxBounds=i,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this)},setMinZoom:function(i){var a=this.options.minZoom;return this.options.minZoom=i,this._loaded&&a!==i&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom)?this.setZoom(i):this},setMaxZoom:function(i){var a=this.options.maxZoom;return this.options.maxZoom=i,this._loaded&&a!==i&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom)?this.setZoom(i):this},panInsideBounds:function(i,a){this._enforcingBounds=!0;var s=this.getCenter(),u=this._limitCenter(s,this._zoom,J(i));return s.equals(u)||this.panTo(u,a),this._enforcingBounds=!1,this},panInside:function(i,a){a=a||{};var s=m(a.paddingTopLeft||a.padding||[0,0]),u=m(a.paddingBottomRight||a.padding||[0,0]),c=this.project(this.getCenter()),h=this.project(i),v=this.getPixelBounds(),w=V([v.min.add(s),v.max.subtract(u)]),C=w.getSize();if(!w.contains(h)){this._enforcingBounds=!0;var U=h.subtract(w.getCenter()),K=w.extend(h).getSize().subtract(C);c.x+=U.x<0?-K.x:K.x,c.y+=U.y<0?-K.y:K.y,this.panTo(this.unproject(c),a),this._enforcingBounds=!1}return this},invalidateSize:function(i){if(!this._loaded)return this;i=R({animate:!1,pan:!0},i===!0?{animate:!0}:i);var a=this.getSize();this._sizeChanged=!0,this._lastCenter=null;var s=this.getSize(),u=a.divideBy(2).round(),c=s.divideBy(2).round(),h=u.subtract(c);return!h.x&&!h.y?this:(i.animate&&i.pan?this.panBy(h):(i.pan&&this._rawPanBy(h),this.fire("move"),i.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(P(this.fire,this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize:a,newSize:s}))},stop:function(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate:function(i){if(i=this._locateOptions=R({timeout:1e4,watch:!1},i),!("geolocation"in navigator))return this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this;var a=P(this._handleGeolocationResponse,this),s=P(this._handleGeolocationError,this);return i.watch?this._locationWatchId=navigator.geolocation.watchPosition(a,s,i):navigator.geolocation.getCurrentPosition(a,s,i),this},stopLocate:function(){return navigator.geolocation&&navigator.geolocation.clearWatch&&navigator.geolocation.clearWatch(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError:function(i){if(this._container._leaflet_id){var a=i.code,s=i.message||(a===1?"permission denied":a===2?"position unavailable":"timeout");this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:a,message:"Geolocation error: "+s+"."})}},_handleGeolocationResponse:function(i){if(this._container._leaflet_id){var a=i.coords.latitude,s=i.coords.longitude,u=new F(a,s),c=u.toBounds(i.coords.accuracy*2),h=this._locateOptions;if(h.setView){var v=this.getBoundsZoom(c);this.setView(u,h.maxZoom?Math.min(v,h.maxZoom):v)}var w={latlng:u,bounds:c,timestamp:i.timestamp};for(var C in i.coords)typeof i.coords[C]=="number"&&(w[C]=i.coords[C]);this.fire("locationfound",w)}},addHandler:function(i,a){if(!a)return this;var s=this[i]=new a(this);return this._handlers.push(s),this.options[i]&&s.enable(),this},remove:function(){if(this._initEvents(!0),this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this._containerId!==this._container._leaflet_id)throw new Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch{this._container._leaflet_id=void 0,this._containerId=void 0}this._locationWatchId!==void 0&&this.stopLocate(),this._stop(),Pt(this._mapPane),this._clearControlPos&&this._clearControlPos(),this._resizeRequest&&(Mt(this._resizeRequest),this._resizeRequest=null),this._clearHandlers(),this._loaded&&this.fire("unload");var i;for(i in this._layers)this._layers[i].remove();for(i in this._panes)Pt(this._panes[i]);return this._layers=[],this._panes=[],delete this._mapPane,delete this._renderer,this},createPane:function(i,a){var s="leaflet-pane"+(i?" leaflet-"+i.replace("Pane","")+"-pane":""),u=Tt("div",s,a||this._mapPane);return i&&(this._panes[i]=u),u},getCenter:function(){return this._checkIfLoaded(),this._lastCenter&&!this._moved()?this._lastCenter.clone():this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom:function(){return this._zoom},getBounds:function(){var i=this.getPixelBounds(),a=this.unproject(i.getBottomLeft()),s=this.unproject(i.getTopRight());return new Y(a,s)},getMinZoom:function(){return this.options.minZoom===void 0?this._layersMinZoom||0:this.options.minZoom},getMaxZoom:function(){return this.options.maxZoom===void 0?this._layersMaxZoom===void 0?1/0:this._layersMaxZoom:this.options.maxZoom},getBoundsZoom:function(i,a,s){i=J(i),s=m(s||[0,0]);var u=this.getZoom()||0,c=this.getMinZoom(),h=this.getMaxZoom(),v=i.getNorthWest(),w=i.getSouthEast(),C=this.getSize().subtract(s),U=V(this.project(w,u),this.project(v,u)).getSize(),K=it.any3d?this.options.zoomSnap:1,I=C.x/U.x,tt=C.y/U.y,ft=a?Math.max(I,tt):Math.min(I,tt);return u=this.getScaleZoom(ft,u),K&&(u=Math.round(u/(K/100))*(K/100),u=a?Math.ceil(u/K)*K:Math.floor(u/K)*K),Math.max(c,Math.min(h,u))},getSize:function(){return(!this._size||this._sizeChanged)&&(this._size=new G(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds:function(i,a){var s=this._getTopLeftPoint(i,a);return new B(s,s.add(this.getSize()))},getPixelOrigin:function(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds:function(i){return this.options.crs.getProjectedBounds(i===void 0?this.getZoom():i)},getPane:function(i){return typeof i=="string"?this._panes[i]:i},getPanes:function(){return this._panes},getContainer:function(){return this._container},getZoomScale:function(i,a){var s=this.options.crs;return a=a===void 0?this._zoom:a,s.scale(i)/s.scale(a)},getScaleZoom:function(i,a){var s=this.options.crs;a=a===void 0?this._zoom:a;var u=s.zoom(i*s.scale(a));return isNaN(u)?1/0:u},project:function(i,a){return a=a===void 0?this._zoom:a,this.options.crs.latLngToPoint(X(i),a)},unproject:function(i,a){return a=a===void 0?this._zoom:a,this.options.crs.pointToLatLng(m(i),a)},layerPointToLatLng:function(i){var a=m(i).add(this.getPixelOrigin());return this.unproject(a)},latLngToLayerPoint:function(i){var a=this.project(X(i))._round();return a._subtract(this.getPixelOrigin())},wrapLatLng:function(i){return this.options.crs.wrapLatLng(X(i))},wrapLatLngBounds:function(i){return this.options.crs.wrapLatLngBounds(J(i))},distance:function(i,a){return this.options.crs.distance(X(i),X(a))},containerPointToLayerPoint:function(i){return m(i).subtract(this._getMapPanePos())},layerPointToContainerPoint:function(i){return m(i).add(this._getMapPanePos())},containerPointToLatLng:function(i){var a=this.containerPointToLayerPoint(m(i));return this.layerPointToLatLng(a)},latLngToContainerPoint:function(i){return this.layerPointToContainerPoint(this.latLngToLayerPoint(X(i)))},mouseEventToContainerPoint:function(i){return be(i,this._container)},mouseEventToLayerPoint:function(i){return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(i))},mouseEventToLatLng:function(i){return this.layerPointToLatLng(this.mouseEventToLayerPoint(i))},_initContainer:function(i){var a=this._container=$a(i);if(a){if(a._leaflet_id)throw new Error("Map container is already initialized.")}else throw new Error("Map container not found.");ht(a,"scroll",this._onScroll,this),this._containerId=z(a)},_initLayout:function(){var i=this._container;this._fadeAnimated=this.options.fadeAnimation&&it.any3d,_t(i,"leaflet-container"+(it.touch?" leaflet-touch":"")+(it.retina?" leaflet-retina":"")+(it.ielt9?" leaflet-oldie":"")+(it.safari?" leaflet-safari":"")+(this._fadeAnimated?" leaflet-fade-anim":""));var a=Wi(i,"position");a!=="absolute"&&a!=="relative"&&a!=="fixed"&&a!=="sticky"&&(i.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes:function(){var i=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),Jt(this._mapPane,new G(0,0)),this.createPane("tilePane"),this.createPane("overlayPane"),this.createPane("shadowPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(_t(i.markerPane,"leaflet-zoom-hide"),_t(i.shadowPane,"leaflet-zoom-hide"))},_resetView:function(i,a,s){Jt(this._mapPane,new G(0,0));var u=!this._loaded;this._loaded=!0,a=this._limitZoom(a),this.fire("viewprereset");var c=this._zoom!==a;this._moveStart(c,s)._move(i,a)._moveEnd(c),this.fire("viewreset"),u&&this.fire("load")},_moveStart:function(i,a){return i&&this.fire("zoomstart"),a||this.fire("movestart"),this},_move:function(i,a,s,u){a===void 0&&(a=this._zoom);var c=this._zoom!==a;return this._zoom=a,this._lastCenter=i,this._pixelOrigin=this._getNewPixelOrigin(i),u?s&&s.pinch&&this.fire("zoom",s):((c||s&&s.pinch)&&this.fire("zoom",s),this.fire("move",s)),this},_moveEnd:function(i){return i&&this.fire("zoomend"),this.fire("moveend")},_stop:function(){return Mt(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy:function(i){Jt(this._mapPane,this._getMapPanePos().subtract(i))},_getZoomSpan:function(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds:function(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded:function(){if(!this._loaded)throw new Error("Set map center and zoom first.")},_initEvents:function(i){this._targets={},this._targets[z(this._container)]=this;var a=i?zt:ht;a(this._container,"click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress keydown keyup",this._handleDOMEvent,this),this.options.trackResize&&a(window,"resize",this._onResize,this),it.any3d&&this.options.transform3DLimit&&(i?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize:function(){Mt(this._resizeRequest),this._resizeRequest=mt(function(){this.invalidateSize({debounceMoveend:!0})},this)},_onScroll:function(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd:function(){var i=this._getMapPanePos();Math.max(Math.abs(i.x),Math.abs(i.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets:function(i,a){for(var s=[],u,c=a==="mouseout"||a==="mouseover",h=i.target||i.srcElement,v=!1;h;){if(u=this._targets[z(h)],u&&(a==="click"||a==="preclick")&&this._draggableMoved(u)){v=!0;break}if(u&&u.listens(a,!0)&&(c&&!en(h,i)||(s.push(u),c))||h===this._container)break;h=h.parentNode}return!s.length&&!v&&!c&&this.listens(a,!0)&&(s=[this]),s},_isClickDisabled:function(i){for(;i&&i!==this._container;){if(i._leaflet_disable_click)return!0;i=i.parentNode}},_handleDOMEvent:function(i){var a=i.target||i.srcElement;if(!(!this._loaded||a._leaflet_disable_events||i.type==="click"&&this._isClickDisabled(a))){var s=i.type;s==="mousedown"&&Sa(a),this._fireDOMEvent(i,s)}},_mouseEvents:["click","dblclick","mouseover","mouseout","contextmenu"],_fireDOMEvent:function(i,a,s){if(i.type==="click"){var u=R({},i);u.type="preclick",this._fireDOMEvent(u,u.type,s)}var c=this._findEventTargets(i,a);if(s){for(var h=[],v=0;v<s.length;v++)s[v].listens(a,!0)&&h.push(s[v]);c=h.concat(c)}if(c.length){a==="contextmenu"&&te(i);var w=c[0],C={originalEvent:i};if(i.type!=="keypress"&&i.type!=="keydown"&&i.type!=="keyup"){var U=w.getLatLng&&(!w._radius||w._radius<=10);C.containerPoint=U?this.latLngToContainerPoint(w.getLatLng()):this.mouseEventToContainerPoint(i),C.layerPoint=this.containerPointToLayerPoint(C.containerPoint),C.latlng=U?w.getLatLng():this.layerPointToLatLng(C.layerPoint)}for(v=0;v<c.length;v++)if(c[v].fire(a,C,!0),C.originalEvent._stopped||c[v].options.bubblingMouseEvents===!1&&Lt(this._mouseEvents,a)!==-1)return}},_draggableMoved:function(i){return i=i.dragging&&i.dragging.enabled()?i:this,i.dragging&&i.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers:function(){for(var i=0,a=this._handlers.length;i<a;i++)this._handlers[i].disable()},whenReady:function(i,a){return this._loaded?i.call(a||this,{target:this}):this.on("load",i,a),this},_getMapPanePos:function(){return Ci(this._mapPane)||new G(0,0)},_moved:function(){var i=this._getMapPanePos();return i&&!i.equals([0,0])},_getTopLeftPoint:function(i,a){var s=i&&a!==void 0?this._getNewPixelOrigin(i,a):this.getPixelOrigin();return s.subtract(this._getMapPanePos())},_getNewPixelOrigin:function(i,a){var s=this.getSize()._divideBy(2);return this.project(i,a)._subtract(s)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint:function(i,a,s){var u=this._getNewPixelOrigin(s,a);return this.project(i,a)._subtract(u)},_latLngBoundsToNewLayerBounds:function(i,a,s){var u=this._getNewPixelOrigin(s,a);return V([this.project(i.getSouthWest(),a)._subtract(u),this.project(i.getNorthWest(),a)._subtract(u),this.project(i.getSouthEast(),a)._subtract(u),this.project(i.getNorthEast(),a)._subtract(u)])},_getCenterLayerPoint:function(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset:function(i){return this.latLngToLayerPoint(i).subtract(this._getCenterLayerPoint())},_limitCenter:function(i,a,s){if(!s)return i;var u=this.project(i,a),c=this.getSize().divideBy(2),h=new B(u.subtract(c),u.add(c)),v=this._getBoundsOffset(h,s,a);return Math.abs(v.x)<=1&&Math.abs(v.y)<=1?i:this.unproject(u.add(v),a)},_limitOffset:function(i,a){if(!a)return i;var s=this.getPixelBounds(),u=new B(s.min.add(i),s.max.add(i));return i.add(this._getBoundsOffset(u,a))},_getBoundsOffset:function(i,a,s){var u=V(this.project(a.getNorthEast(),s),this.project(a.getSouthWest(),s)),c=u.min.subtract(i.min),h=u.max.subtract(i.max),v=this._rebound(c.x,-h.x),w=this._rebound(c.y,-h.y);return new G(v,w)},_rebound:function(i,a){return i+a>0?Math.round(i-a)/2:Math.max(0,Math.ceil(i))-Math.max(0,Math.floor(a))},_limitZoom:function(i){var a=this.getMinZoom(),s=this.getMaxZoom(),u=it.any3d?this.options.zoomSnap:1;return u&&(i=Math.round(i/u)*u),Math.max(a,Math.min(s,i))},_onPanTransitionStep:function(){this.fire("move")},_onPanTransitionEnd:function(){kt(this._mapPane,"leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan:function(i,a){var s=this._getCenterOffset(i)._trunc();return(a&&a.animate)!==!0&&!this.getSize().contains(s)?!1:(this.panBy(s,a),!0)},_createAnimProxy:function(){var i=this._proxy=Tt("div","leaflet-proxy leaflet-zoom-animated");this._panes.mapPane.appendChild(i),this.on("zoomanim",function(a){var s=ya,u=this._proxy.style[s];ke(this._proxy,this.project(a.center,a.zoom),this.getZoomScale(a.zoom,1)),u===this._proxy.style[s]&&this._animatingZoom&&this._onZoomTransitionEnd()},this),this.on("load moveend",this._animMoveEnd,this),this._on("unload",this._destroyAnimProxy,this)},_destroyAnimProxy:function(){Pt(this._proxy),this.off("load moveend",this._animMoveEnd,this),delete this._proxy},_animMoveEnd:function(){var i=this.getCenter(),a=this.getZoom();ke(this._proxy,this.project(i,a),this.getZoomScale(a,1))},_catchTransitionEnd:function(i){this._animatingZoom&&i.propertyName.indexOf("transform")>=0&&this._onZoomTransitionEnd()},_nothingToAnimate:function(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom:function(i,a,s){if(this._animatingZoom)return!0;if(s=s||{},!this._zoomAnimated||s.animate===!1||this._nothingToAnimate()||Math.abs(a-this._zoom)>this.options.zoomAnimationThreshold)return!1;var u=this.getZoomScale(a),c=this._getCenterOffset(i)._divideBy(1-1/u);return s.animate!==!0&&!this.getSize().contains(c)?!1:(mt(function(){this._moveStart(!0,s.noMoveStart||!1)._animateZoom(i,a,!0)},this),!0)},_animateZoom:function(i,a,s,u){this._mapPane&&(s&&(this._animatingZoom=!0,this._animateToCenter=i,this._animateToZoom=a,_t(this._mapPane,"leaflet-zoom-anim")),this.fire("zoomanim",{center:i,zoom:a,noUpdate:u}),this._tempFireZoomEvent||(this._tempFireZoomEvent=this._zoom!==this._animateToZoom),this._move(this._animateToCenter,this._animateToZoom,void 0,!0),setTimeout(P(this._onZoomTransitionEnd,this),250))},_onZoomTransitionEnd:function(){this._animatingZoom&&(this._mapPane&&kt(this._mapPane,"leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._tempFireZoomEvent&&this.fire("zoom"),delete this._tempFireZoomEvent,this.fire("move"),this._moveEnd(!0))}});function Ma(i,a){return new gt(i,a)}var Ae=fe.extend({options:{position:"topright"},initialize:function(i){rt(this,i)},getPosition:function(){return this.options.position},setPosition:function(i){var a=this._map;return a&&a.removeControl(this),this.options.position=i,a&&a.addControl(this),this},getContainer:function(){return this._container},addTo:function(i){this.remove(),this._map=i;var a=this._container=this.onAdd(i),s=this.getPosition(),u=i._controlCorners[s];return _t(a,"leaflet-control"),s.indexOf("bottom")!==-1?u.insertBefore(a,u.firstChild):u.appendChild(a),this._map.on("unload",this.remove,this),this},remove:function(){return this._map?(Pt(this._container),this.onRemove&&this.onRemove(this._map),this._map.off("unload",this.remove,this),this._map=null,this):this},_refocusOnMap:function(i){this._map&&i&&i.screenX>0&&i.screenY>0&&this._map.getContainer().focus()}}),Zn=function(i){return new Ae(i)};gt.include({addControl:function(i){return i.addTo(this),this},removeControl:function(i){return i.remove(),this},_initControlPos:function(){var i=this._controlCorners={},a="leaflet-",s=this._controlContainer=Tt("div",a+"control-container",this._container);function u(c,h){var v=a+c+" "+a+h;i[c+h]=Tt("div",v,s)}u("top","left"),u("top","right"),u("bottom","left"),u("bottom","right")},_clearControlPos:function(){for(var i in this._controlCorners)Pt(this._controlCorners[i]);Pt(this._controlContainer),delete this._controlCorners,delete this._controlContainer}});var lo=Ae.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction:function(i,a,s,u){return s<u?-1:u<s?1:0}},initialize:function(i,a,s){rt(this,s),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,this._preventClick=!1;for(var u in i)this._addLayer(i[u],u);for(u in a)this._addLayer(a[u],u,!0)},onAdd:function(i){this._initLayout(),this._update(),this._map=i,i.on("zoomend",this._checkDisabledLayers,this);for(var a=0;a<this._layers.length;a++)this._layers[a].layer.on("add remove",this._onLayerChange,this);return this._container},addTo:function(i){return Ae.prototype.addTo.call(this,i),this._expandIfNotCollapsed()},onRemove:function(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var i=0;i<this._layers.length;i++)this._layers[i].layer.off("add remove",this._onLayerChange,this)},addBaseLayer:function(i,a){return this._addLayer(i,a),this._map?this._update():this},addOverlay:function(i,a){return this._addLayer(i,a,!0),this._map?this._update():this},removeLayer:function(i){i.off("add remove",this._onLayerChange,this);var a=this._getLayer(z(i));return a&&this._layers.splice(this._layers.indexOf(a),1),this._map?this._update():this},expand:function(){_t(this._container,"leaflet-control-layers-expanded"),this._section.style.height=null;var i=this._map.getSize().y-(this._container.offsetTop+50);return i<this._section.clientHeight?(_t(this._section,"leaflet-control-layers-scrollbar"),this._section.style.height=i+"px"):kt(this._section,"leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse:function(){return kt(this._container,"leaflet-control-layers-expanded"),this},_initLayout:function(){var i="leaflet-control-layers",a=this._container=Tt("div",i),s=this.options.collapsed;a.setAttribute("aria-haspopup",!0),Bn(a),Rn(a);var u=this._section=Tt("section",i+"-list");s&&(this._map.on("click",this.collapse,this),ht(a,{mouseenter:this._expandSafely,mouseleave:this.collapse},this));var c=this._layersLink=Tt("a",i+"-toggle",a);c.href="#",c.title="Layers",c.setAttribute("role","button"),ht(c,{keydown:function(h){h.keyCode===13&&this._expandSafely()},click:function(h){te(h),this._expandSafely()}},this),s||this.expand(),this._baseLayersList=Tt("div",i+"-base",u),this._separator=Tt("div",i+"-separator",u),this._overlaysList=Tt("div",i+"-overlays",u),a.appendChild(u)},_getLayer:function(i){for(var a=0;a<this._layers.length;a++)if(this._layers[a]&&z(this._layers[a].layer)===i)return this._layers[a]},_addLayer:function(i,a,s){this._map&&i.on("add remove",this._onLayerChange,this),this._layers.push({layer:i,name:a,overlay:s}),this.options.sortLayers&&this._layers.sort(P(function(u,c){return this.options.sortFunction(u.layer,c.layer,u.name,c.name)},this)),this.options.autoZIndex&&i.setZIndex&&(this._lastZIndex++,i.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update:function(){if(!this._container)return this;ye(this._baseLayersList),ye(this._overlaysList),this._layerControlInputs=[];var i,a,s,u,c=0;for(s=0;s<this._layers.length;s++)u=this._layers[s],this._addItem(u),a=a||u.overlay,i=i||!u.overlay,c+=u.overlay?0:1;return this.options.hideSingleBase&&(i=i&&c>1,this._baseLayersList.style.display=i?"":"none"),this._separator.style.display=a&&i?"":"none",this},_onLayerChange:function(i){this._handlingClick||this._update();var a=this._getLayer(z(i.target)),s=a.overlay?i.type==="add"?"overlayadd":"overlayremove":i.type==="add"?"baselayerchange":null;s&&this._map.fire(s,a)},_createRadioElement:function(i,a){var s='<input type="radio" class="leaflet-control-layers-selector" name="'+i+'"'+(a?' checked="checked"':"")+"/>",u=document.createElement("div");return u.innerHTML=s,u.firstChild},_addItem:function(i){var a=document.createElement("label"),s=this._map.hasLayer(i.layer),u;i.overlay?(u=document.createElement("input"),u.type="checkbox",u.className="leaflet-control-layers-selector",u.defaultChecked=s):u=this._createRadioElement("leaflet-base-layers_"+z(this),s),this._layerControlInputs.push(u),u.layerId=z(i.layer),ht(u,"click",this._onInputClick,this);var c=document.createElement("span");c.innerHTML=" "+i.name;var h=document.createElement("span");a.appendChild(h),h.appendChild(u),h.appendChild(c);var v=i.overlay?this._overlaysList:this._baseLayersList;return v.appendChild(a),this._checkDisabledLayers(),a},_onInputClick:function(){if(!this._preventClick){var i=this._layerControlInputs,a,s,u=[],c=[];this._handlingClick=!0;for(var h=i.length-1;h>=0;h--)a=i[h],s=this._getLayer(a.layerId).layer,a.checked?u.push(s):a.checked||c.push(s);for(h=0;h<c.length;h++)this._map.hasLayer(c[h])&&this._map.removeLayer(c[h]);for(h=0;h<u.length;h++)this._map.hasLayer(u[h])||this._map.addLayer(u[h]);this._handlingClick=!1,this._refocusOnMap()}},_checkDisabledLayers:function(){for(var i=this._layerControlInputs,a,s,u=this._map.getZoom(),c=i.length-1;c>=0;c--)a=i[c],s=this._getLayer(a.layerId).layer,a.disabled=s.options.minZoom!==void 0&&u<s.options.minZoom||s.options.maxZoom!==void 0&&u>s.options.maxZoom},_expandIfNotCollapsed:function(){return this._map&&!this.options.collapsed&&this.expand(),this},_expandSafely:function(){var i=this._section;this._preventClick=!0,ht(i,"click",te),this.expand();var a=this;setTimeout(function(){zt(i,"click",te),a._preventClick=!1})}}),zr=function(i,a,s){return new lo(i,a,s)},Ql=Ae.extend({options:{position:"topleft",zoomInText:'<span aria-hidden="true">+</span>',zoomInTitle:"Zoom in",zoomOutText:'<span aria-hidden="true">&#x2212;</span>',zoomOutTitle:"Zoom out"},onAdd:function(i){var a="leaflet-control-zoom",s=Tt("div",a+" leaflet-bar"),u=this.options;return this._zoomInButton=this._createButton(u.zoomInText,u.zoomInTitle,a+"-in",s,this._zoomIn),this._zoomOutButton=this._createButton(u.zoomOutText,u.zoomOutTitle,a+"-out",s,this._zoomOut),this._updateDisabled(),i.on("zoomend zoomlevelschange",this._updateDisabled,this),s},onRemove:function(i){i.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable:function(){return this._disabled=!0,this._updateDisabled(),this},enable:function(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn:function(i){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(i.shiftKey?3:1))},_zoomOut:function(i){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(i.shiftKey?3:1))},_createButton:function(i,a,s,u,c){var h=Tt("a",s,u);return h.innerHTML=i,h.href="#",h.title=a,h.setAttribute("role","button"),h.setAttribute("aria-label",a),Bn(h),ht(h,"click",ui),ht(h,"click",c,this),ht(h,"click",this._refocusOnMap,this),h},_updateDisabled:function(){var i=this._map,a="leaflet-disabled";kt(this._zoomInButton,a),kt(this._zoomOutButton,a),this._zoomInButton.setAttribute("aria-disabled","false"),this._zoomOutButton.setAttribute("aria-disabled","false"),(this._disabled||i._zoom===i.getMinZoom())&&(_t(this._zoomOutButton,a),this._zoomOutButton.setAttribute("aria-disabled","true")),(this._disabled||i._zoom===i.getMaxZoom())&&(_t(this._zoomInButton,a),this._zoomInButton.setAttribute("aria-disabled","true"))}});gt.mergeOptions({zoomControl:!0}),gt.addInitHook(function(){this.options.zoomControl&&(this.zoomControl=new Ql,this.addControl(this.zoomControl))});var Kl=function(i){return new Ql(i)},so=Ae.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd:function(i){var a="leaflet-control-scale",s=Tt("div",a),u=this.options;return this._addScales(u,a+"-line",s),i.on(u.updateWhenIdle?"moveend":"move",this._update,this),i.whenReady(this._update,this),s},onRemove:function(i){i.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales:function(i,a,s){i.metric&&(this._mScale=Tt("div",a,s)),i.imperial&&(this._iScale=Tt("div",a,s))},_update:function(){var i=this._map,a=i.getSize().y/2,s=i.distance(i.containerPointToLatLng([0,a]),i.containerPointToLatLng([this.options.maxWidth,a]));this._updateScales(s)},_updateScales:function(i){this.options.metric&&i&&this._updateMetric(i),this.options.imperial&&i&&this._updateImperial(i)},_updateMetric:function(i){var a=this._getRoundNum(i),s=a<1e3?a+" m":a/1e3+" km";this._updateScale(this._mScale,s,a/i)},_updateImperial:function(i){var a=i*3.2808399,s,u,c;a>5280?(s=a/5280,u=this._getRoundNum(s),this._updateScale(this._iScale,u+" mi",u/s)):(c=this._getRoundNum(a),this._updateScale(this._iScale,c+" ft",c/a))},_updateScale:function(i,a,s){i.style.width=Math.round(this.options.maxWidth*s)+"px",i.innerHTML=a},_getRoundNum:function(i){var a=Math.pow(10,(Math.floor(i)+"").length-1),s=i/a;return s=s>=10?10:s>=5?5:s>=3?3:s>=2?2:1,a*s}}),Or=function(i){return new so(i)},Ar='<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" class="leaflet-attribution-flag"><path fill="#4C7BE1" d="M0 0h12v4H0z"/><path fill="#FFD500" d="M0 4h12v3H0z"/><path fill="#E0BC00" d="M0 7h12v1H0z"/></svg>',Jl=Ae.extend({options:{position:"bottomright",prefix:'<a href="https://leafletjs.com" title="A JavaScript library for interactive maps">'+(it.inlineSvg?Ar+" ":"")+"Leaflet</a>"},initialize:function(i){rt(this,i),this._attributions={}},onAdd:function(i){i.attributionControl=this,this._container=Tt("div","leaflet-control-attribution"),Bn(this._container);for(var a in i._layers)i._layers[a].getAttribution&&this.addAttribution(i._layers[a].getAttribution());return this._update(),i.on("layeradd",this._addAttribution,this),this._container},onRemove:function(i){i.off("layeradd",this._addAttribution,this)},_addAttribution:function(i){i.layer.getAttribution&&(this.addAttribution(i.layer.getAttribution()),i.layer.once("remove",function(){this.removeAttribution(i.layer.getAttribution())},this))},setPrefix:function(i){return this.options.prefix=i,this._update(),this},addAttribution:function(i){return i?(this._attributions[i]||(this._attributions[i]=0),this._attributions[i]++,this._update(),this):this},removeAttribution:function(i){return i?(this._attributions[i]&&(this._attributions[i]--,this._update()),this):this},_update:function(){if(this._map){var i=[];for(var a in this._attributions)this._attributions[a]&&i.push(a);var s=[];this.options.prefix&&s.push(this.options.prefix),i.length&&s.push(i.join(", ")),this._container.innerHTML=s.join(' <span aria-hidden="true">|</span> ')}}});gt.mergeOptions({attributionControl:!0}),gt.addInitHook(function(){this.options.attributionControl&&new Jl().addTo(this)});var Nr=function(i){return new Jl(i)};Ae.Layers=lo,Ae.Zoom=Ql,Ae.Scale=so,Ae.Attribution=Jl,Zn.layers=zr,Zn.zoom=Kl,Zn.scale=Or,Zn.attribution=Nr;var Ke=fe.extend({initialize:function(i){this._map=i},enable:function(){return this._enabled?this:(this._enabled=!0,this.addHooks(),this)},disable:function(){return this._enabled?(this._enabled=!1,this.removeHooks(),this):this},enabled:function(){return!!this._enabled}});Ke.addTo=function(i,a){return i.addHandler(a,this),this};var Cr={Events:D},oo=it.touch?"touchstart mousedown":"mousedown",Ri=Q.extend({options:{clickTolerance:3},initialize:function(i,a,s,u){rt(this,u),this._element=i,this._dragStartTarget=a||i,this._preventOutline=s},enable:function(){this._enabled||(ht(this._dragStartTarget,oo,this._onDown,this),this._enabled=!0)},disable:function(){this._enabled&&(Ri._dragging===this&&this.finishDrag(!0),zt(this._dragStartTarget,oo,this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown:function(i){if(this._enabled&&(this._moved=!1,!On(this._element,"leaflet-zoom-anim"))){if(i.touches&&i.touches.length!==1){Ri._dragging===this&&this.finishDrag();return}if(!(Ri._dragging||i.shiftKey||i.which!==1&&i.button!==1&&!i.touches)&&(Ri._dragging=this,this._preventOutline&&Sa(this._element),ba(),si(),!this._moving)){this.fire("down");var a=i.touches?i.touches[0]:i,s=$i(this._element);this._startPoint=new G(a.clientX,a.clientY),this._startPos=Ci(this._element),this._parentScale=Di(s);var u=i.type==="mousedown";ht(document,u?"mousemove":"touchmove",this._onMove,this),ht(document,u?"mouseup":"touchend touchcancel",this._onUp,this)}}},_onMove:function(i){if(this._enabled){if(i.touches&&i.touches.length>1){this._moved=!0;return}var a=i.touches&&i.touches.length===1?i.touches[0]:i,s=new G(a.clientX,a.clientY)._subtract(this._startPoint);!s.x&&!s.y||Math.abs(s.x)+Math.abs(s.y)<this.options.clickTolerance||(s.x/=this._parentScale.x,s.y/=this._parentScale.y,te(i),this._moved||(this.fire("dragstart"),this._moved=!0,_t(document.body,"leaflet-dragging"),this._lastTarget=i.target||i.srcElement,window.SVGElementInstance&&this._lastTarget instanceof window.SVGElementInstance&&(this._lastTarget=this._lastTarget.correspondingUseElement),_t(this._lastTarget,"leaflet-drag-target")),this._newPos=this._startPos.add(s),this._moving=!0,this._lastEvent=i,this._updatePosition())}},_updatePosition:function(){var i={originalEvent:this._lastEvent};this.fire("predrag",i),Jt(this._element,this._newPos),this.fire("drag",i)},_onUp:function(){this._enabled&&this.finishDrag()},finishDrag:function(i){kt(document.body,"leaflet-dragging"),this._lastTarget&&(kt(this._lastTarget,"leaflet-drag-target"),this._lastTarget=null),zt(document,"mousemove touchmove",this._onMove,this),zt(document,"mouseup touchend touchcancel",this._onUp,this),Vl(),xa();var a=this._moved&&this._moving;this._moving=!1,Ri._dragging=!1,a&&this.fire("dragend",{noInertia:i,distance:this._newPos.distanceTo(this._startPos)})}});function ro(i,a,s){var u,c=[1,4,2,8],h,v,w,C,U,K,I,tt;for(h=0,K=i.length;h<K;h++)i[h]._code=nn(i[h],a);for(w=0;w<4;w++){for(I=c[w],u=[],h=0,K=i.length,v=K-1;h<K;v=h++)C=i[h],U=i[v],C._code&I?U._code&I||(tt=nl(U,C,I,a,s),tt._code=nn(tt,a),u.push(tt)):(U._code&I&&(tt=nl(U,C,I,a,s),tt._code=nn(tt,a),u.push(tt)),u.push(C));i=u}return i}function il(i,a){var s,u,c,h,v,w,C,U,K;if(!i||i.length===0)throw new Error("latlngs not passed");de(i)||(console.warn("latlngs are not flat! Only the first ring will be used"),i=i[0]);var I=X([0,0]),tt=J(i),ft=tt.getNorthWest().distanceTo(tt.getSouthWest())*tt.getNorthEast().distanceTo(tt.getNorthWest());ft<1700&&(I=Wl(i));var Gt=i.length,Wt=[];for(s=0;s<Gt;s++){var me=X(i[s]);Wt.push(a.project(X([me.lat-I.lat,me.lng-I.lng])))}for(w=C=U=0,s=0,u=Gt-1;s<Gt;u=s++)c=Wt[s],h=Wt[u],v=c.y*h.x-h.y*c.x,C+=(c.x+h.x)*v,U+=(c.y+h.y)*v,w+=v*3;w===0?K=Wt[0]:K=[C/w,U/w];var Ge=a.unproject(m(K));return X([Ge.lat+I.lat,Ge.lng+I.lng])}function Wl(i){for(var a=0,s=0,u=0,c=0;c<i.length;c++){var h=X(i[c]);a+=h.lat,s+=h.lng,u++}return X([a/u,s/u])}var Dr={__proto__:null,clipPolygon:ro,polygonCenter:il,centroid:Wl};function uo(i,a){if(!a||!i.length)return i.slice();var s=a*a;return i=Zr(i,s),i=Br(i,s),i}function Il(i,a,s){return Math.sqrt(an(i,a,s,!0))}function Rr(i,a,s){return an(i,a,s)}function Br(i,a){var s=i.length,u=typeof Uint8Array<"u"?Uint8Array:Array,c=new u(s);c[0]=c[s-1]=1,$l(i,c,a,0,s-1);var h,v=[];for(h=0;h<s;h++)c[h]&&v.push(i[h]);return v}function $l(i,a,s,u,c){var h=0,v,w,C;for(w=u+1;w<=c-1;w++)C=an(i[w],i[u],i[c],!0),C>h&&(v=w,h=C);h>s&&(a[v]=1,$l(i,a,s,u,v),$l(i,a,s,v,c))}function Zr(i,a){for(var s=[i[0]],u=1,c=0,h=i.length;u<h;u++)jr(i[u],i[c])>a&&(s.push(i[u]),c=u);return c<h-1&&s.push(i[h-1]),s}var co;function fo(i,a,s,u,c){var h=u?co:nn(i,s),v=nn(a,s),w,C,U;for(co=v;;){if(!(h|v))return[i,a];if(h&v)return!1;w=h||v,C=nl(i,a,w,s,c),U=nn(C,s),w===h?(i=C,h=U):(a=C,v=U)}}function nl(i,a,s,u,c){var h=a.x-i.x,v=a.y-i.y,w=u.min,C=u.max,U,K;return s&8?(U=i.x+h*(C.y-i.y)/v,K=C.y):s&4?(U=i.x+h*(w.y-i.y)/v,K=w.y):s&2?(U=C.x,K=i.y+v*(C.x-i.x)/h):s&1&&(U=w.x,K=i.y+v*(w.x-i.x)/h),new G(U,K,c)}function nn(i,a){var s=0;return i.x<a.min.x?s|=1:i.x>a.max.x&&(s|=2),i.y<a.min.y?s|=4:i.y>a.max.y&&(s|=8),s}function jr(i,a){var s=a.x-i.x,u=a.y-i.y;return s*s+u*u}function an(i,a,s,u){var c=a.x,h=a.y,v=s.x-c,w=s.y-h,C=v*v+w*w,U;return C>0&&(U=((i.x-c)*v+(i.y-h)*w)/C,U>1?(c=s.x,h=s.y):U>0&&(c+=v*U,h+=w*U)),v=i.x-c,w=i.y-h,u?v*v+w*w:new G(c,h)}function de(i){return!dt(i[0])||typeof i[0][0]!="object"&&typeof i[0][0]<"u"}function ho(i){return console.warn("Deprecated use of _flat, please use L.LineUtil.isFlat instead."),de(i)}function Fl(i,a){var s,u,c,h,v,w,C,U;if(!i||i.length===0)throw new Error("latlngs not passed");de(i)||(console.warn("latlngs are not flat! Only the first ring will be used"),i=i[0]);var K=X([0,0]),I=J(i),tt=I.getNorthWest().distanceTo(I.getSouthWest())*I.getNorthEast().distanceTo(I.getNorthWest());tt<1700&&(K=Wl(i));var ft=i.length,Gt=[];for(s=0;s<ft;s++){var Wt=X(i[s]);Gt.push(a.project(X([Wt.lat-K.lat,Wt.lng-K.lng])))}for(s=0,u=0;s<ft-1;s++)u+=Gt[s].distanceTo(Gt[s+1])/2;if(u===0)U=Gt[0];else for(s=0,h=0;s<ft-1;s++)if(v=Gt[s],w=Gt[s+1],c=v.distanceTo(w),h+=c,h>u){C=(h-u)/c,U=[w.x-C*(w.x-v.x),w.y-C*(w.y-v.y)];break}var me=a.unproject(m(U));return X([me.lat+K.lat,me.lng+K.lng])}var mo={__proto__:null,simplify:uo,pointToSegmentDistance:Il,closestPointOnSegment:Rr,clipSegment:fo,_getEdgeIntersection:nl,_getBitCode:nn,_sqClosestPointOnSegment:an,isFlat:de,_flat:ho,polylineCenter:Fl},al={project:function(i){return new G(i.lng,i.lat)},unproject:function(i){return new F(i.y,i.x)},bounds:new B([-180,-90],[180,90])},ll={R:6378137,R_MINOR:6356752314245179e-9,bounds:new B([-2003750834279e-5,-1549657073972e-5],[2003750834279e-5,1876465623138e-5]),project:function(i){var a=Math.PI/180,s=this.R,u=i.lat*a,c=this.R_MINOR/s,h=Math.sqrt(1-c*c),v=h*Math.sin(u),w=Math.tan(Math.PI/4-u/2)/Math.pow((1-v)/(1+v),h/2);return u=-s*Math.log(Math.max(w,1e-10)),new G(i.lng*a*s,u)},unproject:function(i){for(var a=180/Math.PI,s=this.R,u=this.R_MINOR/s,c=Math.sqrt(1-u*u),h=Math.exp(-i.y/s),v=Math.PI/2-2*Math.atan(h),w=0,C=.1,U;w<15&&Math.abs(C)>1e-7;w++)U=c*Math.sin(v),U=Math.pow((1-U)/(1+U),c/2),C=Math.PI/2-2*Math.atan(h*U)-v,v+=C;return new F(v*a,i.x*a/s)}},_o={__proto__:null,LonLat:al,Mercator:ll,SphericalMercator:_a},jn=R({},Et,{code:"EPSG:3395",projection:ll,transformation:function(){var i=.5/(Math.PI*ll.R);return Gi(i,.5,-i,.5)}()}),po=R({},Et,{code:"EPSG:4326",projection:al,transformation:Gi(1/180,1,-1/180,.5)}),Hr=R({},Kt,{projection:al,transformation:Gi(1,0,-1,0),scale:function(i){return Math.pow(2,i)},zoom:function(i){return Math.log(i)/Math.LN2},distance:function(i,a){var s=a.lng-i.lng,u=a.lat-i.lat;return Math.sqrt(s*s+u*u)},infinite:!0});Kt.Earth=Et,Kt.EPSG3395=jn,Kt.EPSG3857=pa,Kt.EPSG900913=Zl,Kt.EPSG4326=po,Kt.Simple=Hr;var Je=Q.extend({options:{pane:"overlayPane",attribution:null,bubblingMouseEvents:!0},addTo:function(i){return i.addLayer(this),this},remove:function(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom:function(i){return i&&i.removeLayer(this),this},getPane:function(i){return this._map.getPane(i?this.options[i]||i:this.options.pane)},addInteractiveTarget:function(i){return this._map._targets[z(i)]=this,this},removeInteractiveTarget:function(i){return delete this._map._targets[z(i)],this},getAttribution:function(){return this.options.attribution},_layerAdd:function(i){var a=i.target;if(a.hasLayer(this)){if(this._map=a,this._zoomAnimated=a._zoomAnimated,this.getEvents){var s=this.getEvents();a.on(s,this),this.once("remove",function(){a.off(s,this)},this)}this.onAdd(a),this.fire("add"),a.fire("layeradd",{layer:this})}}});gt.include({addLayer:function(i){if(!i._layerAdd)throw new Error("The provided object is not a Layer.");var a=z(i);return this._layers[a]?this:(this._layers[a]=i,i._mapToAdd=this,i.beforeAdd&&i.beforeAdd(this),this.whenReady(i._layerAdd,i),this)},removeLayer:function(i){var a=z(i);return this._layers[a]?(this._loaded&&i.onRemove(this),delete this._layers[a],this._loaded&&(this.fire("layerremove",{layer:i}),i.fire("remove")),i._map=i._mapToAdd=null,this):this},hasLayer:function(i){return z(i)in this._layers},eachLayer:function(i,a){for(var s in this._layers)i.call(a,this._layers[s]);return this},_addLayers:function(i){i=i?dt(i)?i:[i]:[];for(var a=0,s=i.length;a<s;a++)this.addLayer(i[a])},_addZoomLimit:function(i){(!isNaN(i.options.maxZoom)||!isNaN(i.options.minZoom))&&(this._zoomBoundLayers[z(i)]=i,this._updateZoomLevels())},_removeZoomLimit:function(i){var a=z(i);this._zoomBoundLayers[a]&&(delete this._zoomBoundLayers[a],this._updateZoomLevels())},_updateZoomLevels:function(){var i=1/0,a=-1/0,s=this._getZoomSpan();for(var u in this._zoomBoundLayers){var c=this._zoomBoundLayers[u].options;i=c.minZoom===void 0?i:Math.min(i,c.minZoom),a=c.maxZoom===void 0?a:Math.max(a,c.maxZoom)}this._layersMaxZoom=a===-1/0?void 0:a,this._layersMinZoom=i===1/0?void 0:i,s!==this._getZoomSpan()&&this.fire("zoomlevelschange"),this.options.maxZoom===void 0&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),this.options.minZoom===void 0&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}});var ln=Je.extend({initialize:function(i,a){rt(this,a),this._layers={};var s,u;if(i)for(s=0,u=i.length;s<u;s++)this.addLayer(i[s])},addLayer:function(i){var a=this.getLayerId(i);return this._layers[a]=i,this._map&&this._map.addLayer(i),this},removeLayer:function(i){var a=i in this._layers?i:this.getLayerId(i);return this._map&&this._layers[a]&&this._map.removeLayer(this._layers[a]),delete this._layers[a],this},hasLayer:function(i){var a=typeof i=="number"?i:this.getLayerId(i);return a in this._layers},clearLayers:function(){return this.eachLayer(this.removeLayer,this)},invoke:function(i){var a=Array.prototype.slice.call(arguments,1),s,u;for(s in this._layers)u=this._layers[s],u[i]&&u[i].apply(u,a);return this},onAdd:function(i){this.eachLayer(i.addLayer,i)},onRemove:function(i){this.eachLayer(i.removeLayer,i)},eachLayer:function(i,a){for(var s in this._layers)i.call(a,this._layers[s]);return this},getLayer:function(i){return this._layers[i]},getLayers:function(){var i=[];return this.eachLayer(i.push,i),i},setZIndex:function(i){return this.invoke("setZIndex",i)},getLayerId:function(i){return z(i)}}),vo=function(i,a){return new ln(i,a)},Re=ln.extend({addLayer:function(i){return this.hasLayer(i)?this:(i.addEventParent(this),ln.prototype.addLayer.call(this,i),this.fire("layeradd",{layer:i}))},removeLayer:function(i){return this.hasLayer(i)?(i in this._layers&&(i=this._layers[i]),i.removeEventParent(this),ln.prototype.removeLayer.call(this,i),this.fire("layerremove",{layer:i})):this},setStyle:function(i){return this.invoke("setStyle",i)},bringToFront:function(){return this.invoke("bringToFront")},bringToBack:function(){return this.invoke("bringToBack")},getBounds:function(){var i=new Y;for(var a in this._layers){var s=this._layers[a];i.extend(s.getBounds?s.getBounds():s.getLatLng())}return i}}),Ea=function(i,a){return new Re(i,a)},Hn=fe.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0],crossOrigin:!1},initialize:function(i){rt(this,i)},createIcon:function(i){return this._createIcon("icon",i)},createShadow:function(i){return this._createIcon("shadow",i)},_createIcon:function(i,a){var s=this._getIconUrl(i);if(!s){if(i==="icon")throw new Error("iconUrl not set in Icon options (see the docs).");return null}var u=this._createImg(s,a&&a.tagName==="IMG"?a:null);return this._setIconStyles(u,i),(this.options.crossOrigin||this.options.crossOrigin==="")&&(u.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),u},_setIconStyles:function(i,a){var s=this.options,u=s[a+"Size"];typeof u=="number"&&(u=[u,u]);var c=m(u),h=m(a==="shadow"&&s.shadowAnchor||s.iconAnchor||c&&c.divideBy(2,!0));i.className="leaflet-marker-"+a+" "+(s.className||""),h&&(i.style.marginLeft=-h.x+"px",i.style.marginTop=-h.y+"px"),c&&(i.style.width=c.x+"px",i.style.height=c.y+"px")},_createImg:function(i,a){return a=a||document.createElement("img"),a.src=i,a},_getIconUrl:function(i){return it.retina&&this.options[i+"RetinaUrl"]||this.options[i+"Url"]}});function sl(i){return new Hn(i)}var Un=Hn.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl:function(i){return typeof Un.imagePath!="string"&&(Un.imagePath=this._detectIconPath()),(this.options.imagePath||Un.imagePath)+Hn.prototype._getIconUrl.call(this,i)},_stripUrl:function(i){var a=function(s,u,c){var h=u.exec(s);return h&&h[c]};return i=a(i,/^url\((['"])?(.+)\1\)$/,2),i&&a(i,/^(.*)marker-icon\.png$/,1)},_detectIconPath:function(){var i=Tt("div","leaflet-default-icon-path",document.body),a=Wi(i,"background-image")||Wi(i,"backgroundImage");if(document.body.removeChild(i),a=this._stripUrl(a),a)return a;var s=document.querySelector('link[href$="leaflet.css"]');return s?s.href.substring(0,s.href.length-11-1):""}}),ts=Ke.extend({initialize:function(i){this._marker=i},addHooks:function(){var i=this._marker._icon;this._draggable||(this._draggable=new Ri(i,i,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),_t(i,"leaflet-marker-draggable")},removeHooks:function(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&kt(this._marker._icon,"leaflet-marker-draggable")},moved:function(){return this._draggable&&this._draggable._moved},_adjustPan:function(i){var a=this._marker,s=a._map,u=this._marker.options.autoPanSpeed,c=this._marker.options.autoPanPadding,h=Ci(a._icon),v=s.getPixelBounds(),w=s.getPixelOrigin(),C=V(v.min._subtract(w).add(c),v.max._subtract(w).subtract(c));if(!C.contains(h)){var U=m((Math.max(C.max.x,h.x)-C.max.x)/(v.max.x-C.max.x)-(Math.min(C.min.x,h.x)-C.min.x)/(v.min.x-C.min.x),(Math.max(C.max.y,h.y)-C.max.y)/(v.max.y-C.max.y)-(Math.min(C.min.y,h.y)-C.min.y)/(v.min.y-C.min.y)).multiplyBy(u);s.panBy(U,{animate:!1}),this._draggable._newPos._add(U),this._draggable._startPos._add(U),Jt(a._icon,this._draggable._newPos),this._onDrag(i),this._panRequest=mt(this._adjustPan.bind(this,i))}},_onDragStart:function(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup&&this._marker.closePopup(),this._marker.fire("movestart").fire("dragstart")},_onPreDrag:function(i){this._marker.options.autoPan&&(Mt(this._panRequest),this._panRequest=mt(this._adjustPan.bind(this,i)))},_onDrag:function(i){var a=this._marker,s=a._shadow,u=Ci(a._icon),c=a._map.layerPointToLatLng(u);s&&Jt(s,u),a._latlng=c,i.latlng=c,i.oldLatLng=this._oldLatLng,a.fire("move",i).fire("drag",i)},_onDragEnd:function(i){Mt(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",i)}}),qn=Je.extend({options:{icon:new Un,interactive:!0,keyboard:!0,title:"",alt:"Marker",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",shadowPane:"shadowPane",bubblingMouseEvents:!1,autoPanOnFocus:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10},initialize:function(i,a){rt(this,a),this._latlng=X(i)},onAdd:function(i){this._zoomAnimated=this._zoomAnimated&&i.options.markerZoomAnimation,this._zoomAnimated&&i.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove:function(i){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&i.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents:function(){return{zoom:this.update,viewreset:this.update}},getLatLng:function(){return this._latlng},setLatLng:function(i){var a=this._latlng;return this._latlng=X(i),this.update(),this.fire("move",{oldLatLng:a,latlng:this._latlng})},setZIndexOffset:function(i){return this.options.zIndexOffset=i,this.update()},getIcon:function(){return this.options.icon},setIcon:function(i){return this.options.icon=i,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement:function(){return this._icon},update:function(){if(this._icon&&this._map){var i=this._map.latLngToLayerPoint(this._latlng).round();this._setPos(i)}return this},_initIcon:function(){var i=this.options,a="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),s=i.icon.createIcon(this._icon),u=!1;s!==this._icon&&(this._icon&&this._removeIcon(),u=!0,i.title&&(s.title=i.title),s.tagName==="IMG"&&(s.alt=i.alt||"")),_t(s,a),i.keyboard&&(s.tabIndex="0",s.setAttribute("role","button")),this._icon=s,i.riseOnHover&&this.on({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&ht(s,"focus",this._panOnFocus,this);var c=i.icon.createShadow(this._shadow),h=!1;c!==this._shadow&&(this._removeShadow(),h=!0),c&&(_t(c,a),c.alt=""),this._shadow=c,i.opacity<1&&this._updateOpacity(),u&&this.getPane().appendChild(this._icon),this._initInteraction(),c&&h&&this.getPane(i.shadowPane).appendChild(this._shadow)},_removeIcon:function(){this.options.riseOnHover&&this.off({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&zt(this._icon,"focus",this._panOnFocus,this),Pt(this._icon),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow:function(){this._shadow&&Pt(this._shadow),this._shadow=null},_setPos:function(i){this._icon&&Jt(this._icon,i),this._shadow&&Jt(this._shadow,i),this._zIndex=i.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex:function(i){this._icon&&(this._icon.style.zIndex=this._zIndex+i)},_animateZoom:function(i){var a=this._map._latLngToNewLayerPoint(this._latlng,i.zoom,i.center).round();this._setPos(a)},_initInteraction:function(){if(this.options.interactive&&(_t(this._icon,"leaflet-interactive"),this.addInteractiveTarget(this._icon),ts)){var i=this.options.draggable;this.dragging&&(i=this.dragging.enabled(),this.dragging.disable()),this.dragging=new ts(this),i&&this.dragging.enable()}},setOpacity:function(i){return this.options.opacity=i,this._map&&this._updateOpacity(),this},_updateOpacity:function(){var i=this.options.opacity;this._icon&&Oe(this._icon,i),this._shadow&&Oe(this._shadow,i)},_bringToFront:function(){this._updateZIndex(this.options.riseOffset)},_resetZIndex:function(){this._updateZIndex(0)},_panOnFocus:function(){var i=this._map;if(i){var a=this.options.icon.options,s=a.iconSize?m(a.iconSize):m(0,0),u=a.iconAnchor?m(a.iconAnchor):m(0,0);i.panInside(this._latlng,{paddingTopLeft:u,paddingBottomRight:s.subtract(u)})}},_getPopupAnchor:function(){return this.options.icon.options.popupAnchor},_getTooltipAnchor:function(){return this.options.icon.options.tooltipAnchor}});function es(i,a){return new qn(i,a)}var Si=Je.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingMouseEvents:!0},beforeAdd:function(i){this._renderer=i.getRenderer(this)},onAdd:function(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove:function(){this._renderer._removePath(this)},redraw:function(){return this._map&&this._renderer._updatePath(this),this},setStyle:function(i){return rt(this,i),this._renderer&&(this._renderer._updateStyle(this),this.options.stroke&&i&&Object.prototype.hasOwnProperty.call(i,"weight")&&this._updateBounds()),this},bringToFront:function(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack:function(){return this._renderer&&this._renderer._bringToBack(this),this},getElement:function(){return this._path},_reset:function(){this._project(),this._update()},_clickTolerance:function(){return(this.options.stroke?this.options.weight/2:0)+(this._renderer.options.tolerance||0)}}),La=Si.extend({options:{fill:!0,radius:10},initialize:function(i,a){rt(this,a),this._latlng=X(i),this._radius=this.options.radius},setLatLng:function(i){var a=this._latlng;return this._latlng=X(i),this.redraw(),this.fire("move",{oldLatLng:a,latlng:this._latlng})},getLatLng:function(){return this._latlng},setRadius:function(i){return this.options.radius=this._radius=i,this.redraw()},getRadius:function(){return this._radius},setStyle:function(i){var a=i&&i.radius||this._radius;return Si.prototype.setStyle.call(this,i),this.setRadius(a),this},_project:function(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds:function(){var i=this._radius,a=this._radiusY||i,s=this._clickTolerance(),u=[i+s,a+s];this._pxBounds=new B(this._point.subtract(u),this._point.add(u))},_update:function(){this._map&&this._updatePath()},_updatePath:function(){this._renderer._updateCircle(this)},_empty:function(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint:function(i){return i.distanceTo(this._point)<=this._radius+this._clickTolerance()}});function go(i,a){return new La(i,a)}var is=La.extend({initialize:function(i,a,s){if(typeof a=="number"&&(a=R({},s,{radius:a})),rt(this,a),this._latlng=X(i),isNaN(this.options.radius))throw new Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius:function(i){return this._mRadius=i,this.redraw()},getRadius:function(){return this._mRadius},getBounds:function(){var i=[this._radius,this._radiusY||this._radius];return new Y(this._map.layerPointToLatLng(this._point.subtract(i)),this._map.layerPointToLatLng(this._point.add(i)))},setStyle:Si.prototype.setStyle,_project:function(){var i=this._latlng.lng,a=this._latlng.lat,s=this._map,u=s.options.crs;if(u.distance===Et.distance){var c=Math.PI/180,h=this._mRadius/Et.R/c,v=s.project([a+h,i]),w=s.project([a-h,i]),C=v.add(w).divideBy(2),U=s.unproject(C).lat,K=Math.acos((Math.cos(h*c)-Math.sin(a*c)*Math.sin(U*c))/(Math.cos(a*c)*Math.cos(U*c)))/c;(isNaN(K)||K===0)&&(K=h/Math.cos(Math.PI/180*a)),this._point=C.subtract(s.getPixelOrigin()),this._radius=isNaN(K)?0:C.x-s.project([U,i-K]).x,this._radiusY=C.y-v.y}else{var I=u.unproject(u.project(this._latlng).subtract([this._mRadius,0]));this._point=s.latLngToLayerPoint(this._latlng),this._radius=this._point.x-s.latLngToLayerPoint(I).x}this._updateBounds()}});function Ur(i,a,s){return new is(i,a,s)}var Ti=Si.extend({options:{smoothFactor:1,noClip:!1},initialize:function(i,a){rt(this,a),this._setLatLngs(i)},getLatLngs:function(){return this._latlngs},setLatLngs:function(i){return this._setLatLngs(i),this.redraw()},isEmpty:function(){return!this._latlngs.length},closestLayerPoint:function(i){for(var a=1/0,s=null,u=an,c,h,v=0,w=this._parts.length;v<w;v++)for(var C=this._parts[v],U=1,K=C.length;U<K;U++){c=C[U-1],h=C[U];var I=u(i,c,h,!0);I<a&&(a=I,s=u(i,c,h))}return s&&(s.distance=Math.sqrt(a)),s},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return Fl(this._defaultShape(),this._map.options.crs)},getBounds:function(){return this._bounds},addLatLng:function(i,a){return a=a||this._defaultShape(),i=X(i),a.push(i),this._bounds.extend(i),this.redraw()},_setLatLngs:function(i){this._bounds=new Y,this._latlngs=this._convertLatLngs(i)},_defaultShape:function(){return de(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs:function(i){for(var a=[],s=de(i),u=0,c=i.length;u<c;u++)s?(a[u]=X(i[u]),this._bounds.extend(a[u])):a[u]=this._convertLatLngs(i[u]);return a},_project:function(){var i=new B;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,i),this._bounds.isValid()&&i.isValid()&&(this._rawPxBounds=i,this._updateBounds())},_updateBounds:function(){var i=this._clickTolerance(),a=new G(i,i);this._rawPxBounds&&(this._pxBounds=new B([this._rawPxBounds.min.subtract(a),this._rawPxBounds.max.add(a)]))},_projectLatlngs:function(i,a,s){var u=i[0]instanceof F,c=i.length,h,v;if(u){for(v=[],h=0;h<c;h++)v[h]=this._map.latLngToLayerPoint(i[h]),s.extend(v[h]);a.push(v)}else for(h=0;h<c;h++)this._projectLatlngs(i[h],a,s)},_clipPoints:function(){var i=this._renderer._bounds;if(this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(i))){if(this.options.noClip){this._parts=this._rings;return}var a=this._parts,s,u,c,h,v,w,C;for(s=0,c=0,h=this._rings.length;s<h;s++)for(C=this._rings[s],u=0,v=C.length;u<v-1;u++)w=fo(C[u],C[u+1],i,u,!0),w&&(a[c]=a[c]||[],a[c].push(w[0]),(w[1]!==C[u+1]||u===v-2)&&(a[c].push(w[1]),c++))}},_simplifyPoints:function(){for(var i=this._parts,a=this.options.smoothFactor,s=0,u=i.length;s<u;s++)i[s]=uo(i[s],a)},_update:function(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath:function(){this._renderer._updatePoly(this)},_containsPoint:function(i,a){var s,u,c,h,v,w,C=this._clickTolerance();if(!this._pxBounds||!this._pxBounds.contains(i))return!1;for(s=0,h=this._parts.length;s<h;s++)for(w=this._parts[s],u=0,v=w.length,c=v-1;u<v;c=u++)if(!(!a&&u===0)&&Il(i,w[c],w[u])<=C)return!0;return!1}});function qr(i,a){return new Ti(i,a)}Ti._flat=ho;var Pn=Ti.extend({options:{fill:!0},isEmpty:function(){return!this._latlngs.length||!this._latlngs[0].length},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return il(this._defaultShape(),this._map.options.crs)},_convertLatLngs:function(i){var a=Ti.prototype._convertLatLngs.call(this,i),s=a.length;return s>=2&&a[0]instanceof F&&a[0].equals(a[s-1])&&a.pop(),a},_setLatLngs:function(i){Ti.prototype._setLatLngs.call(this,i),de(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape:function(){return de(this._latlngs[0])?this._latlngs[0]:this._latlngs[0][0]},_clipPoints:function(){var i=this._renderer._bounds,a=this.options.weight,s=new G(a,a);if(i=new B(i.min.subtract(s),i.max.add(s)),this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(i))){if(this.options.noClip){this._parts=this._rings;return}for(var u=0,c=this._rings.length,h;u<c;u++)h=ro(this._rings[u],i,!0),h.length&&this._parts.push(h)}},_updatePath:function(){this._renderer._updatePoly(this,!0)},_containsPoint:function(i){var a=!1,s,u,c,h,v,w,C,U;if(!this._pxBounds||!this._pxBounds.contains(i))return!1;for(h=0,C=this._parts.length;h<C;h++)for(s=this._parts[h],v=0,U=s.length,w=U-1;v<U;w=v++)u=s[v],c=s[w],u.y>i.y!=c.y>i.y&&i.x<(c.x-u.x)*(i.y-u.y)/(c.y-u.y)+u.x&&(a=!a);return a||Ti.prototype._containsPoint.call(this,i,!0)}});function Be(i,a){return new Pn(i,a)}var Ze=Re.extend({initialize:function(i,a){rt(this,a),this._layers={},i&&this.addData(i)},addData:function(i){var a=dt(i)?i:i.features,s,u,c;if(a){for(s=0,u=a.length;s<u;s++)c=a[s],(c.geometries||c.geometry||c.features||c.coordinates)&&this.addData(c);return this}var h=this.options;if(h.filter&&!h.filter(i))return this;var v=za(i,h);return v?(v.feature=kn(i),v.defaultOptions=v.options,this.resetStyle(v),h.onEachFeature&&h.onEachFeature(i,v),this.addLayer(v)):this},resetStyle:function(i){return i===void 0?this.eachLayer(this.resetStyle,this):(i.options=R({},i.defaultOptions),this._setLayerStyle(i,this.options.style),this)},setStyle:function(i){return this.eachLayer(function(a){this._setLayerStyle(a,i)},this)},_setLayerStyle:function(i,a){i.setStyle&&(typeof a=="function"&&(a=a(i.feature)),i.setStyle(a))}});function za(i,a){var s=i.type==="Feature"?i.geometry:i,u=s?s.coordinates:null,c=[],h=a&&a.pointToLayer,v=a&&a.coordsToLatLng||ol,w,C,U,K;if(!u&&!s)return null;switch(s.type){case"Point":return w=v(u),ns(h,i,w,a);case"MultiPoint":for(U=0,K=u.length;U<K;U++)w=v(u[U]),c.push(ns(h,i,w,a));return new Re(c);case"LineString":case"MultiLineString":return C=Oa(u,s.type==="LineString"?0:1,v),new Ti(C,a);case"Polygon":case"MultiPolygon":return C=Oa(u,s.type==="Polygon"?1:2,v),new Pn(C,a);case"GeometryCollection":for(U=0,K=s.geometries.length;U<K;U++){var I=za({geometry:s.geometries[U],type:"Feature",properties:i.properties},a);I&&c.push(I)}return new Re(c);case"FeatureCollection":for(U=0,K=s.features.length;U<K;U++){var tt=za(s.features[U],a);tt&&c.push(tt)}return new Re(c);default:throw new Error("Invalid GeoJSON object.")}}function ns(i,a,s,u){return i?i(a,s):new qn(s,u&&u.markersInheritOptions&&u)}function ol(i){return new F(i[1],i[0],i[2])}function Oa(i,a,s){for(var u=[],c=0,h=i.length,v;c<h;c++)v=a?Oa(i[c],a-1,s):(s||ol)(i[c]),u.push(v);return u}function Aa(i,a){return i=X(i),i.alt!==void 0?[et(i.lng,a),et(i.lat,a),et(i.alt,a)]:[et(i.lng,a),et(i.lat,a)]}function rl(i,a,s,u){for(var c=[],h=0,v=i.length;h<v;h++)c.push(a?rl(i[h],de(i[h])?0:a-1,s,u):Aa(i[h],u));return!a&&s&&c.length>0&&c.push(c[0].slice()),c}function We(i,a){return i.feature?R({},i.feature,{geometry:a}):kn(a)}function kn(i){return i.type==="Feature"||i.type==="FeatureCollection"?i:{type:"Feature",properties:{},geometry:i}}var sn={toGeoJSON:function(i){return We(this,{type:"Point",coordinates:Aa(this.getLatLng(),i)})}};qn.include(sn),is.include(sn),La.include(sn),Ti.include({toGeoJSON:function(i){var a=!de(this._latlngs),s=rl(this._latlngs,a?1:0,!1,i);return We(this,{type:(a?"Multi":"")+"LineString",coordinates:s})}}),Pn.include({toGeoJSON:function(i){var a=!de(this._latlngs),s=a&&!de(this._latlngs[0]),u=rl(this._latlngs,s?2:a?1:0,!0,i);return a||(u=[u]),We(this,{type:(s?"Multi":"")+"Polygon",coordinates:u})}}),ln.include({toMultiPoint:function(i){var a=[];return this.eachLayer(function(s){a.push(s.toGeoJSON(i).geometry.coordinates)}),We(this,{type:"MultiPoint",coordinates:a})},toGeoJSON:function(i){var a=this.feature&&this.feature.geometry&&this.feature.geometry.type;if(a==="MultiPoint")return this.toMultiPoint(i);var s=a==="GeometryCollection",u=[];return this.eachLayer(function(c){if(c.toGeoJSON){var h=c.toGeoJSON(i);if(s)u.push(h.geometry);else{var v=kn(h);v.type==="FeatureCollection"?u.push.apply(u,v.features):u.push(v)}}}),s?We(this,{geometries:u,type:"GeometryCollection"}):{type:"FeatureCollection",features:u}}});function ul(i,a){return new Ze(i,a)}var yo=ul,ci=Je.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:""},initialize:function(i,a,s){this._url=i,this._bounds=J(a),rt(this,s)},onAdd:function(){this._image||(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(_t(this._image,"leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove:function(){Pt(this._image),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity:function(i){return this.options.opacity=i,this._image&&this._updateOpacity(),this},setStyle:function(i){return i.opacity&&this.setOpacity(i.opacity),this},bringToFront:function(){return this._map&&Ii(this._image),this},bringToBack:function(){return this._map&&zn(this._image),this},setUrl:function(i){return this._url=i,this._image&&(this._image.src=i),this},setBounds:function(i){return this._bounds=J(i),this._map&&this._reset(),this},getEvents:function(){var i={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(i.zoomanim=this._animateZoom),i},setZIndex:function(i){return this.options.zIndex=i,this._updateZIndex(),this},getBounds:function(){return this._bounds},getElement:function(){return this._image},_initImage:function(){var i=this._url.tagName==="IMG",a=this._image=i?this._url:Tt("img");if(_t(a,"leaflet-image-layer"),this._zoomAnimated&&_t(a,"leaflet-zoom-animated"),this.options.className&&_t(a,this.options.className),a.onselectstart=W,a.onmousemove=W,a.onload=P(this.fire,this,"load"),a.onerror=P(this._overlayOnError,this,"error"),(this.options.crossOrigin||this.options.crossOrigin==="")&&(a.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),this.options.zIndex&&this._updateZIndex(),i){this._url=a.src;return}a.src=this._url,a.alt=this.options.alt},_animateZoom:function(i){var a=this._map.getZoomScale(i.zoom),s=this._map._latLngBoundsToNewLayerBounds(this._bounds,i.zoom,i.center).min;ke(this._image,s,a)},_reset:function(){var i=this._image,a=new B(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),s=a.getSize();Jt(i,a.min),i.style.width=s.x+"px",i.style.height=s.y+"px"},_updateOpacity:function(){Oe(this._image,this.options.opacity)},_updateZIndex:function(){this._image&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError:function(){this.fire("error");var i=this.options.errorOverlayUrl;i&&this._url!==i&&(this._url=i,this._image.src=i)},getCenter:function(){return this._bounds.getCenter()}}),Gn=function(i,a,s){return new ci(i,a,s)},cl=ci.extend({options:{autoplay:!0,loop:!0,keepAspectRatio:!0,muted:!1,playsInline:!0},_initImage:function(){var i=this._url.tagName==="VIDEO",a=this._image=i?this._url:Tt("video");if(_t(a,"leaflet-image-layer"),this._zoomAnimated&&_t(a,"leaflet-zoom-animated"),this.options.className&&_t(a,this.options.className),a.onselectstart=W,a.onmousemove=W,a.onloadeddata=P(this.fire,this,"load"),i){for(var s=a.getElementsByTagName("source"),u=[],c=0;c<s.length;c++)u.push(s[c].src);this._url=s.length>0?u:[a.src];return}dt(this._url)||(this._url=[this._url]),!this.options.keepAspectRatio&&Object.prototype.hasOwnProperty.call(a.style,"objectFit")&&(a.style.objectFit="fill"),a.autoplay=!!this.options.autoplay,a.loop=!!this.options.loop,a.muted=!!this.options.muted,a.playsInline=!!this.options.playsInline;for(var h=0;h<this._url.length;h++){var v=Tt("source");v.src=this._url[h],a.appendChild(v)}}});function xo(i,a,s){return new cl(i,a,s)}var Bi=ci.extend({_initImage:function(){var i=this._image=this._url;_t(i,"leaflet-image-layer"),this._zoomAnimated&&_t(i,"leaflet-zoom-animated"),this.options.className&&_t(i,this.options.className),i.onselectstart=W,i.onmousemove=W}});function bo(i,a,s){return new Bi(i,a,s)}var Ie=Je.extend({options:{interactive:!1,offset:[0,0],className:"",pane:void 0,content:""},initialize:function(i,a){i&&(i instanceof F||dt(i))?(this._latlng=X(i),rt(this,a)):(rt(this,i),this._source=a),this.options.content&&(this._content=this.options.content)},openOn:function(i){return i=arguments.length?i:this._source._map,i.hasLayer(this)||i.addLayer(this),this},close:function(){return this._map&&this._map.removeLayer(this),this},toggle:function(i){return this._map?this.close():(arguments.length?this._source=i:i=this._source,this._prepareOpen(),this.openOn(i._map)),this},onAdd:function(i){this._zoomAnimated=i._zoomAnimated,this._container||this._initLayout(),i._fadeAnimated&&Oe(this._container,0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),i._fadeAnimated&&Oe(this._container,1),this.bringToFront(),this.options.interactive&&(_t(this._container,"leaflet-interactive"),this.addInteractiveTarget(this._container))},onRemove:function(i){i._fadeAnimated?(Oe(this._container,0),this._removeTimeout=setTimeout(P(Pt,void 0,this._container),200)):Pt(this._container),this.options.interactive&&(kt(this._container,"leaflet-interactive"),this.removeInteractiveTarget(this._container))},getLatLng:function(){return this._latlng},setLatLng:function(i){return this._latlng=X(i),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent:function(){return this._content},setContent:function(i){return this._content=i,this.update(),this},getElement:function(){return this._container},update:function(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents:function(){var i={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(i.zoomanim=this._animateZoom),i},isOpen:function(){return!!this._map&&this._map.hasLayer(this)},bringToFront:function(){return this._map&&Ii(this._container),this},bringToBack:function(){return this._map&&zn(this._container),this},_prepareOpen:function(i){var a=this._source;if(!a._map)return!1;if(a instanceof Re){a=null;var s=this._source._layers;for(var u in s)if(s[u]._map){a=s[u];break}if(!a)return!1;this._source=a}if(!i)if(a.getCenter)i=a.getCenter();else if(a.getLatLng)i=a.getLatLng();else if(a.getBounds)i=a.getBounds().getCenter();else throw new Error("Unable to get source layer LatLng.");return this.setLatLng(i),this._map&&this.update(),!0},_updateContent:function(){if(this._content){var i=this._contentNode,a=typeof this._content=="function"?this._content(this._source||this):this._content;if(typeof a=="string")i.innerHTML=a;else{for(;i.hasChildNodes();)i.removeChild(i.firstChild);i.appendChild(a)}this.fire("contentupdate")}},_updatePosition:function(){if(this._map){var i=this._map.latLngToLayerPoint(this._latlng),a=m(this.options.offset),s=this._getAnchor();this._zoomAnimated?Jt(this._container,i.add(s)):a=a.add(i).add(s);var u=this._containerBottom=-a.y,c=this._containerLeft=-Math.round(this._containerWidth/2)+a.x;this._container.style.bottom=u+"px",this._container.style.left=c+"px"}},_getAnchor:function(){return[0,0]}});gt.include({_initOverlay:function(i,a,s,u){var c=a;return c instanceof i||(c=new i(u).setContent(a)),s&&c.setLatLng(s),c}}),Je.include({_initOverlay:function(i,a,s,u){var c=s;return c instanceof i?(rt(c,u),c._source=this):(c=a&&!u?a:new i(u,this),c.setContent(s)),c}});var Na=Ie.extend({options:{pane:"popupPane",offset:[0,7],maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,autoClose:!0,closeOnEscapeKey:!0,className:""},openOn:function(i){return i=arguments.length?i:this._source._map,!i.hasLayer(this)&&i._popup&&i._popup.options.autoClose&&i.removeLayer(i._popup),i._popup=this,Ie.prototype.openOn.call(this,i)},onAdd:function(i){Ie.prototype.onAdd.call(this,i),i.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof Si||this._source.on("preclick",bi))},onRemove:function(i){Ie.prototype.onRemove.call(this,i),i.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof Si||this._source.off("preclick",bi))},getEvents:function(){var i=Ie.prototype.getEvents.call(this);return(this.options.closeOnClick!==void 0?this.options.closeOnClick:this._map.options.closePopupOnClick)&&(i.preclick=this.close),this.options.keepInView&&(i.moveend=this._adjustPan),i},_initLayout:function(){var i="leaflet-popup",a=this._container=Tt("div",i+" "+(this.options.className||"")+" leaflet-zoom-animated"),s=this._wrapper=Tt("div",i+"-content-wrapper",a);if(this._contentNode=Tt("div",i+"-content",s),Bn(a),Rn(this._contentNode),ht(a,"contextmenu",bi),this._tipContainer=Tt("div",i+"-tip-container",a),this._tip=Tt("div",i+"-tip",this._tipContainer),this.options.closeButton){var u=this._closeButton=Tt("a",i+"-close-button",a);u.setAttribute("role","button"),u.setAttribute("aria-label","Close popup"),u.href="#close",u.innerHTML='<span aria-hidden="true">&#215;</span>',ht(u,"click",function(c){te(c),this.close()},this)}},_updateLayout:function(){var i=this._contentNode,a=i.style;a.width="",a.whiteSpace="nowrap";var s=i.offsetWidth;s=Math.min(s,this.options.maxWidth),s=Math.max(s,this.options.minWidth),a.width=s+1+"px",a.whiteSpace="",a.height="";var u=i.offsetHeight,c=this.options.maxHeight,h="leaflet-popup-scrolled";c&&u>c?(a.height=c+"px",_t(i,h)):kt(i,h),this._containerWidth=this._container.offsetWidth},_animateZoom:function(i){var a=this._map._latLngToNewLayerPoint(this._latlng,i.zoom,i.center),s=this._getAnchor();Jt(this._container,a.add(s))},_adjustPan:function(){if(this.options.autoPan){if(this._map._panAnim&&this._map._panAnim.stop(),this._autopanning){this._autopanning=!1;return}var i=this._map,a=parseInt(Wi(this._container,"marginBottom"),10)||0,s=this._container.offsetHeight+a,u=this._containerWidth,c=new G(this._containerLeft,-s-this._containerBottom);c._add(Ci(this._container));var h=i.layerPointToContainerPoint(c),v=m(this.options.autoPanPadding),w=m(this.options.autoPanPaddingTopLeft||v),C=m(this.options.autoPanPaddingBottomRight||v),U=i.getSize(),K=0,I=0;h.x+u+C.x>U.x&&(K=h.x+u-U.x+C.x),h.x-K-w.x<0&&(K=h.x-w.x),h.y+s+C.y>U.y&&(I=h.y+s-U.y+C.y),h.y-I-w.y<0&&(I=h.y-w.y),(K||I)&&(this.options.keepInView&&(this._autopanning=!0),i.fire("autopanstart").panBy([K,I]))}},_getAnchor:function(){return m(this._source&&this._source._getPopupAnchor?this._source._getPopupAnchor():[0,0])}}),Pr=function(i,a){return new Na(i,a)};gt.mergeOptions({closePopupOnClick:!0}),gt.include({openPopup:function(i,a,s){return this._initOverlay(Na,i,a,s).openOn(this),this},closePopup:function(i){return i=arguments.length?i:this._popup,i&&i.close(),this}}),Je.include({bindPopup:function(i,a){return this._popup=this._initOverlay(Na,this._popup,i,a),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup:function(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup:function(i){return this._popup&&(this instanceof Re||(this._popup._source=this),this._popup._prepareOpen(i||this._latlng)&&this._popup.openOn(this._map)),this},closePopup:function(){return this._popup&&this._popup.close(),this},togglePopup:function(){return this._popup&&this._popup.toggle(this),this},isPopupOpen:function(){return this._popup?this._popup.isOpen():!1},setPopupContent:function(i){return this._popup&&this._popup.setContent(i),this},getPopup:function(){return this._popup},_openPopup:function(i){if(!(!this._popup||!this._map)){ui(i);var a=i.layer||i.target;if(this._popup._source===a&&!(a instanceof Si)){this._map.hasLayer(this._popup)?this.closePopup():this.openPopup(i.latlng);return}this._popup._source=a,this.openPopup(i.latlng)}},_movePopup:function(i){this._popup.setLatLng(i.latlng)},_onKeyPress:function(i){i.originalEvent.keyCode===13&&this._openPopup(i)}});var fl=Ie.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,opacity:.9},onAdd:function(i){Ie.prototype.onAdd.call(this,i),this.setOpacity(this.options.opacity),i.fire("tooltipopen",{tooltip:this}),this._source&&(this.addEventParent(this._source),this._source.fire("tooltipopen",{tooltip:this},!0))},onRemove:function(i){Ie.prototype.onRemove.call(this,i),i.fire("tooltipclose",{tooltip:this}),this._source&&(this.removeEventParent(this._source),this._source.fire("tooltipclose",{tooltip:this},!0))},getEvents:function(){var i=Ie.prototype.getEvents.call(this);return this.options.permanent||(i.preclick=this.close),i},_initLayout:function(){var i="leaflet-tooltip",a=i+" "+(this.options.className||"")+" leaflet-zoom-"+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=Tt("div",a),this._container.setAttribute("role","tooltip"),this._container.setAttribute("id","leaflet-tooltip-"+z(this))},_updateLayout:function(){},_adjustPan:function(){},_setPosition:function(i){var a,s,u=this._map,c=this._container,h=u.latLngToContainerPoint(u.getCenter()),v=u.layerPointToContainerPoint(i),w=this.options.direction,C=c.offsetWidth,U=c.offsetHeight,K=m(this.options.offset),I=this._getAnchor();w==="top"?(a=C/2,s=U):w==="bottom"?(a=C/2,s=0):w==="center"?(a=C/2,s=U/2):w==="right"?(a=0,s=U/2):w==="left"?(a=C,s=U/2):v.x<h.x?(w="right",a=0,s=U/2):(w="left",a=C+(K.x+I.x)*2,s=U/2),i=i.subtract(m(a,s,!0)).add(K).add(I),kt(c,"leaflet-tooltip-right"),kt(c,"leaflet-tooltip-left"),kt(c,"leaflet-tooltip-top"),kt(c,"leaflet-tooltip-bottom"),_t(c,"leaflet-tooltip-"+w),Jt(c,i)},_updatePosition:function(){var i=this._map.latLngToLayerPoint(this._latlng);this._setPosition(i)},setOpacity:function(i){this.options.opacity=i,this._container&&Oe(this._container,i)},_animateZoom:function(i){var a=this._map._latLngToNewLayerPoint(this._latlng,i.zoom,i.center);this._setPosition(a)},_getAnchor:function(){return m(this._source&&this._source._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}}),kr=function(i,a){return new fl(i,a)};gt.include({openTooltip:function(i,a,s){return this._initOverlay(fl,i,a,s).openOn(this),this},closeTooltip:function(i){return i.close(),this}}),Je.include({bindTooltip:function(i,a){return this._tooltip&&this.isTooltipOpen()&&this.unbindTooltip(),this._tooltip=this._initOverlay(fl,this._tooltip,i,a),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip:function(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions:function(i){if(!(!i&&this._tooltipHandlersAdded)){var a=i?"off":"on",s={remove:this.closeTooltip,move:this._moveTooltip};this._tooltip.options.permanent?s.add=this._openTooltip:(s.mouseover=this._openTooltip,s.mouseout=this.closeTooltip,s.click=this._openTooltip,this._map?this._addFocusListeners():s.add=this._addFocusListeners),this._tooltip.options.sticky&&(s.mousemove=this._moveTooltip),this[a](s),this._tooltipHandlersAdded=!i}},openTooltip:function(i){return this._tooltip&&(this instanceof Re||(this._tooltip._source=this),this._tooltip._prepareOpen(i)&&(this._tooltip.openOn(this._map),this.getElement?this._setAriaDescribedByOnLayer(this):this.eachLayer&&this.eachLayer(this._setAriaDescribedByOnLayer,this))),this},closeTooltip:function(){if(this._tooltip)return this._tooltip.close()},toggleTooltip:function(){return this._tooltip&&this._tooltip.toggle(this),this},isTooltipOpen:function(){return this._tooltip.isOpen()},setTooltipContent:function(i){return this._tooltip&&this._tooltip.setContent(i),this},getTooltip:function(){return this._tooltip},_addFocusListeners:function(){this.getElement?this._addFocusListenersOnLayer(this):this.eachLayer&&this.eachLayer(this._addFocusListenersOnLayer,this)},_addFocusListenersOnLayer:function(i){var a=typeof i.getElement=="function"&&i.getElement();a&&(ht(a,"focus",function(){this._tooltip._source=i,this.openTooltip()},this),ht(a,"blur",this.closeTooltip,this))},_setAriaDescribedByOnLayer:function(i){var a=typeof i.getElement=="function"&&i.getElement();a&&a.setAttribute("aria-describedby",this._tooltip._container.id)},_openTooltip:function(i){if(!(!this._tooltip||!this._map)){if(this._map.dragging&&this._map.dragging.moving()&&!this._openOnceFlag){this._openOnceFlag=!0;var a=this;this._map.once("moveend",function(){a._openOnceFlag=!1,a._openTooltip(i)});return}this._tooltip._source=i.layer||i.target,this.openTooltip(this._tooltip.options.sticky?i.latlng:void 0)}},_moveTooltip:function(i){var a=i.latlng,s,u;this._tooltip.options.sticky&&i.originalEvent&&(s=this._map.mouseEventToContainerPoint(i.originalEvent),u=this._map.containerPointToLayerPoint(s),a=this._map.layerPointToLatLng(u)),this._tooltip.setLatLng(a)}});var as=Hn.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon:function(i){var a=i&&i.tagName==="DIV"?i:document.createElement("div"),s=this.options;if(s.html instanceof Element?(ye(a),a.appendChild(s.html)):a.innerHTML=s.html!==!1?s.html:"",s.bgPos){var u=m(s.bgPos);a.style.backgroundPosition=-u.x+"px "+-u.y+"px"}return this._setIconStyles(a,"icon"),a},createShadow:function(){return null}});function So(i){return new as(i)}Hn.Default=Un;var on=Je.extend({options:{tileSize:256,opacity:1,updateWhenIdle:it.mobile,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize:function(i){rt(this,i)},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._resetView()},beforeAdd:function(i){i._addZoomLimit(this)},onRemove:function(i){this._removeAllTiles(),Pt(this._container),i._removeZoomLimit(this),this._container=null,this._tileZoom=void 0},bringToFront:function(){return this._map&&(Ii(this._container),this._setAutoZIndex(Math.max)),this},bringToBack:function(){return this._map&&(zn(this._container),this._setAutoZIndex(Math.min)),this},getContainer:function(){return this._container},setOpacity:function(i){return this.options.opacity=i,this._updateOpacity(),this},setZIndex:function(i){return this.options.zIndex=i,this._updateZIndex(),this},isLoading:function(){return this._loading},redraw:function(){if(this._map){this._removeAllTiles();var i=this._clampZoom(this._map.getZoom());i!==this._tileZoom&&(this._tileZoom=i,this._updateLevels()),this._update()}return this},getEvents:function(){var i={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=A(this._onMoveEnd,this.options.updateInterval,this)),i.move=this._onMove),this._zoomAnimated&&(i.zoomanim=this._animateZoom),i},createTile:function(){return document.createElement("div")},getTileSize:function(){var i=this.options.tileSize;return i instanceof G?i:new G(i,i)},_updateZIndex:function(){this._container&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex:function(i){for(var a=this.getPane().children,s=-i(-1/0,1/0),u=0,c=a.length,h;u<c;u++)h=a[u].style.zIndex,a[u]!==this._container&&h&&(s=i(s,+h));isFinite(s)&&(this.options.zIndex=s+i(-1,1),this._updateZIndex())},_updateOpacity:function(){if(this._map&&!it.ielt9){Oe(this._container,this.options.opacity);var i=+new Date,a=!1,s=!1;for(var u in this._tiles){var c=this._tiles[u];if(!(!c.current||!c.loaded)){var h=Math.min(1,(i-c.loaded)/200);Oe(c.el,h),h<1?a=!0:(c.active?s=!0:this._onOpaqueTile(c),c.active=!0)}}s&&!this._noPrune&&this._pruneTiles(),a&&(Mt(this._fadeFrame),this._fadeFrame=mt(this._updateOpacity,this))}},_onOpaqueTile:W,_initContainer:function(){this._container||(this._container=Tt("div","leaflet-layer "+(this.options.className||"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels:function(){var i=this._tileZoom,a=this.options.maxZoom;if(i!==void 0){for(var s in this._levels)s=Number(s),this._levels[s].el.children.length||s===i?(this._levels[s].el.style.zIndex=a-Math.abs(i-s),this._onUpdateLevel(s)):(Pt(this._levels[s].el),this._removeTilesAtZoom(s),this._onRemoveLevel(s),delete this._levels[s]);var u=this._levels[i],c=this._map;return u||(u=this._levels[i]={},u.el=Tt("div","leaflet-tile-container leaflet-zoom-animated",this._container),u.el.style.zIndex=a,u.origin=c.project(c.unproject(c.getPixelOrigin()),i).round(),u.zoom=i,this._setZoomTransform(u,c.getCenter(),c.getZoom()),W(u.el.offsetWidth),this._onCreateLevel(u)),this._level=u,u}},_onUpdateLevel:W,_onRemoveLevel:W,_onCreateLevel:W,_pruneTiles:function(){if(this._map){var i,a,s=this._map.getZoom();if(s>this.options.maxZoom||s<this.options.minZoom){this._removeAllTiles();return}for(i in this._tiles)a=this._tiles[i],a.retain=a.current;for(i in this._tiles)if(a=this._tiles[i],a.current&&!a.active){var u=a.coords;this._retainParent(u.x,u.y,u.z,u.z-5)||this._retainChildren(u.x,u.y,u.z,u.z+2)}for(i in this._tiles)this._tiles[i].retain||this._removeTile(i)}},_removeTilesAtZoom:function(i){for(var a in this._tiles)this._tiles[a].coords.z===i&&this._removeTile(a)},_removeAllTiles:function(){for(var i in this._tiles)this._removeTile(i)},_invalidateAll:function(){for(var i in this._levels)Pt(this._levels[i].el),this._onRemoveLevel(Number(i)),delete this._levels[i];this._removeAllTiles(),this._tileZoom=void 0},_retainParent:function(i,a,s,u){var c=Math.floor(i/2),h=Math.floor(a/2),v=s-1,w=new G(+c,+h);w.z=+v;var C=this._tileCoordsToKey(w),U=this._tiles[C];return U&&U.active?(U.retain=!0,!0):(U&&U.loaded&&(U.retain=!0),v>u?this._retainParent(c,h,v,u):!1)},_retainChildren:function(i,a,s,u){for(var c=2*i;c<2*i+2;c++)for(var h=2*a;h<2*a+2;h++){var v=new G(c,h);v.z=s+1;var w=this._tileCoordsToKey(v),C=this._tiles[w];if(C&&C.active){C.retain=!0;continue}else C&&C.loaded&&(C.retain=!0);s+1<u&&this._retainChildren(c,h,s+1,u)}},_resetView:function(i){var a=i&&(i.pinch||i.flyTo);this._setView(this._map.getCenter(),this._map.getZoom(),a,a)},_animateZoom:function(i){this._setView(i.center,i.zoom,!0,i.noUpdate)},_clampZoom:function(i){var a=this.options;return a.minNativeZoom!==void 0&&i<a.minNativeZoom?a.minNativeZoom:a.maxNativeZoom!==void 0&&a.maxNativeZoom<i?a.maxNativeZoom:i},_setView:function(i,a,s,u){var c=Math.round(a);this.options.maxZoom!==void 0&&c>this.options.maxZoom||this.options.minZoom!==void 0&&c<this.options.minZoom?c=void 0:c=this._clampZoom(c);var h=this.options.updateWhenZooming&&c!==this._tileZoom;(!u||h)&&(this._tileZoom=c,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),c!==void 0&&this._update(i),s||this._pruneTiles(),this._noPrune=!!s),this._setZoomTransforms(i,a)},_setZoomTransforms:function(i,a){for(var s in this._levels)this._setZoomTransform(this._levels[s],i,a)},_setZoomTransform:function(i,a,s){var u=this._map.getZoomScale(s,i.zoom),c=i.origin.multiplyBy(u).subtract(this._map._getNewPixelOrigin(a,s)).round();it.any3d?ke(i.el,c,u):Jt(i.el,c)},_resetGrid:function(){var i=this._map,a=i.options.crs,s=this._tileSize=this.getTileSize(),u=this._tileZoom,c=this._map.getPixelWorldBounds(this._tileZoom);c&&(this._globalTileRange=this._pxBoundsToTileRange(c)),this._wrapX=a.wrapLng&&!this.options.noWrap&&[Math.floor(i.project([0,a.wrapLng[0]],u).x/s.x),Math.ceil(i.project([0,a.wrapLng[1]],u).x/s.y)],this._wrapY=a.wrapLat&&!this.options.noWrap&&[Math.floor(i.project([a.wrapLat[0],0],u).y/s.x),Math.ceil(i.project([a.wrapLat[1],0],u).y/s.y)]},_onMoveEnd:function(){!this._map||this._map._animatingZoom||this._update()},_getTiledPixelBounds:function(i){var a=this._map,s=a._animatingZoom?Math.max(a._animateToZoom,a.getZoom()):a.getZoom(),u=a.getZoomScale(s,this._tileZoom),c=a.project(i,this._tileZoom).floor(),h=a.getSize().divideBy(u*2);return new B(c.subtract(h),c.add(h))},_update:function(i){var a=this._map;if(a){var s=this._clampZoom(a.getZoom());if(i===void 0&&(i=a.getCenter()),this._tileZoom!==void 0){var u=this._getTiledPixelBounds(i),c=this._pxBoundsToTileRange(u),h=c.getCenter(),v=[],w=this.options.keepBuffer,C=new B(c.getBottomLeft().subtract([w,-w]),c.getTopRight().add([w,-w]));if(!(isFinite(c.min.x)&&isFinite(c.min.y)&&isFinite(c.max.x)&&isFinite(c.max.y)))throw new Error("Attempted to load an infinite number of tiles");for(var U in this._tiles){var K=this._tiles[U].coords;(K.z!==this._tileZoom||!C.contains(new G(K.x,K.y)))&&(this._tiles[U].current=!1)}if(Math.abs(s-this._tileZoom)>1){this._setView(i,s);return}for(var I=c.min.y;I<=c.max.y;I++)for(var tt=c.min.x;tt<=c.max.x;tt++){var ft=new G(tt,I);if(ft.z=this._tileZoom,!!this._isValidTile(ft)){var Gt=this._tiles[this._tileCoordsToKey(ft)];Gt?Gt.current=!0:v.push(ft)}}if(v.sort(function(me,Ge){return me.distanceTo(h)-Ge.distanceTo(h)}),v.length!==0){this._loading||(this._loading=!0,this.fire("loading"));var Wt=document.createDocumentFragment();for(tt=0;tt<v.length;tt++)this._addTile(v[tt],Wt);this._level.el.appendChild(Wt)}}}},_isValidTile:function(i){var a=this._map.options.crs;if(!a.infinite){var s=this._globalTileRange;if(!a.wrapLng&&(i.x<s.min.x||i.x>s.max.x)||!a.wrapLat&&(i.y<s.min.y||i.y>s.max.y))return!1}if(!this.options.bounds)return!0;var u=this._tileCoordsToBounds(i);return J(this.options.bounds).overlaps(u)},_keyToBounds:function(i){return this._tileCoordsToBounds(this._keyToTileCoords(i))},_tileCoordsToNwSe:function(i){var a=this._map,s=this.getTileSize(),u=i.scaleBy(s),c=u.add(s),h=a.unproject(u,i.z),v=a.unproject(c,i.z);return[h,v]},_tileCoordsToBounds:function(i){var a=this._tileCoordsToNwSe(i),s=new Y(a[0],a[1]);return this.options.noWrap||(s=this._map.wrapLatLngBounds(s)),s},_tileCoordsToKey:function(i){return i.x+":"+i.y+":"+i.z},_keyToTileCoords:function(i){var a=i.split(":"),s=new G(+a[0],+a[1]);return s.z=+a[2],s},_removeTile:function(i){var a=this._tiles[i];a&&(Pt(a.el),delete this._tiles[i],this.fire("tileunload",{tile:a.el,coords:this._keyToTileCoords(i)}))},_initTile:function(i){_t(i,"leaflet-tile");var a=this.getTileSize();i.style.width=a.x+"px",i.style.height=a.y+"px",i.onselectstart=W,i.onmousemove=W,it.ielt9&&this.options.opacity<1&&Oe(i,this.options.opacity)},_addTile:function(i,a){var s=this._getTilePos(i),u=this._tileCoordsToKey(i),c=this.createTile(this._wrapCoords(i),P(this._tileReady,this,i));this._initTile(c),this.createTile.length<2&&mt(P(this._tileReady,this,i,null,c)),Jt(c,s),this._tiles[u]={el:c,coords:i,current:!0},a.appendChild(c),this.fire("tileloadstart",{tile:c,coords:i})},_tileReady:function(i,a,s){a&&this.fire("tileerror",{error:a,tile:s,coords:i});var u=this._tileCoordsToKey(i);s=this._tiles[u],s&&(s.loaded=+new Date,this._map._fadeAnimated?(Oe(s.el,0),Mt(this._fadeFrame),this._fadeFrame=mt(this._updateOpacity,this)):(s.active=!0,this._pruneTiles()),a||(_t(s.el,"leaflet-tile-loaded"),this.fire("tileload",{tile:s.el,coords:i})),this._noTilesToLoad()&&(this._loading=!1,this.fire("load"),it.ielt9||!this._map._fadeAnimated?mt(this._pruneTiles,this):setTimeout(P(this._pruneTiles,this),250)))},_getTilePos:function(i){return i.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords:function(i){var a=new G(this._wrapX?k(i.x,this._wrapX):i.x,this._wrapY?k(i.y,this._wrapY):i.y);return a.z=i.z,a},_pxBoundsToTileRange:function(i){var a=this.getTileSize();return new B(i.min.unscaleBy(a).floor(),i.max.unscaleBy(a).ceil().subtract([1,1]))},_noTilesToLoad:function(){for(var i in this._tiles)if(!this._tiles[i].loaded)return!1;return!0}});function $e(i){return new on(i)}var rn=on.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1,referrerPolicy:!1},initialize:function(i,a){this._url=i,a=rt(this,a),a.detectRetina&&it.retina&&a.maxZoom>0?(a.tileSize=Math.floor(a.tileSize/2),a.zoomReverse?(a.zoomOffset--,a.minZoom=Math.min(a.maxZoom,a.minZoom+1)):(a.zoomOffset++,a.maxZoom=Math.max(a.minZoom,a.maxZoom-1)),a.minZoom=Math.max(0,a.minZoom)):a.zoomReverse?a.minZoom=Math.min(a.maxZoom,a.minZoom):a.maxZoom=Math.max(a.minZoom,a.maxZoom),typeof a.subdomains=="string"&&(a.subdomains=a.subdomains.split("")),this.on("tileunload",this._onTileRemove)},setUrl:function(i,a){return this._url===i&&a===void 0&&(a=!0),this._url=i,a||this.redraw(),this},createTile:function(i,a){var s=document.createElement("img");return ht(s,"load",P(this._tileOnLoad,this,a,s)),ht(s,"error",P(this._tileOnError,this,a,s)),(this.options.crossOrigin||this.options.crossOrigin==="")&&(s.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),typeof this.options.referrerPolicy=="string"&&(s.referrerPolicy=this.options.referrerPolicy),s.alt="",s.src=this.getTileUrl(i),s},getTileUrl:function(i){var a={r:it.retina?"@2x":"",s:this._getSubdomain(i),x:i.x,y:i.y,z:this._getZoomForUrl()};if(this._map&&!this._map.options.crs.infinite){var s=this._globalTileRange.max.y-i.y;this.options.tms&&(a.y=s),a["-y"]=s}return $t(this._url,R(a,this.options))},_tileOnLoad:function(i,a){it.ielt9?setTimeout(P(i,this,null,a),0):i(null,a)},_tileOnError:function(i,a,s){var u=this.options.errorTileUrl;u&&a.getAttribute("src")!==u&&(a.src=u),i(s,a)},_onTileRemove:function(i){i.tile.onload=null},_getZoomForUrl:function(){var i=this._tileZoom,a=this.options.maxZoom,s=this.options.zoomReverse,u=this.options.zoomOffset;return s&&(i=a-i),i+u},_getSubdomain:function(i){var a=Math.abs(i.x+i.y)%this.options.subdomains.length;return this.options.subdomains[a]},_abortLoading:function(){var i,a;for(i in this._tiles)if(this._tiles[i].coords.z!==this._tileZoom&&(a=this._tiles[i].el,a.onload=W,a.onerror=W,!a.complete)){a.src=ut;var s=this._tiles[i].coords;Pt(a),delete this._tiles[i],this.fire("tileabort",{tile:a,coords:s})}},_removeTile:function(i){var a=this._tiles[i];if(a)return a.el.setAttribute("src",ut),on.prototype._removeTile.call(this,i)},_tileReady:function(i,a,s){if(!(!this._map||s&&s.getAttribute("src")===ut))return on.prototype._tileReady.call(this,i,a,s)}});function je(i,a){return new rn(i,a)}var He=rn.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize:function(i,a){this._url=i;var s=R({},this.defaultWmsParams);for(var u in a)u in this.options||(s[u]=a[u]);a=rt(this,a);var c=a.detectRetina&&it.retina?2:1,h=this.getTileSize();s.width=h.x*c,s.height=h.y*c,this.wmsParams=s},onAdd:function(i){this._crs=this.options.crs||i.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var a=this._wmsVersion>=1.3?"crs":"srs";this.wmsParams[a]=this._crs.code,rn.prototype.onAdd.call(this,i)},getTileUrl:function(i){var a=this._tileCoordsToNwSe(i),s=this._crs,u=V(s.project(a[0]),s.project(a[1])),c=u.min,h=u.max,v=(this._wmsVersion>=1.3&&this._crs===po?[c.y,c.x,h.y,h.x]:[c.x,c.y,h.x,h.y]).join(","),w=rn.prototype.getTileUrl.call(this,i);return w+Dt(this.wmsParams,w,this.options.uppercase)+(this.options.uppercase?"&BBOX=":"&bbox=")+v},setParams:function(i,a){return R(this.wmsParams,i),a||this.redraw(),this}});function Yn(i,a){return new He(i,a)}rn.WMS=He,je.wms=Yn;var Fe=Je.extend({options:{padding:.1},initialize:function(i){rt(this,i),z(this),this._layers=this._layers||{}},onAdd:function(){this._container||(this._initContainer(),_t(this._container,"leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._update(),this.on("update",this._updatePaths,this)},onRemove:function(){this.off("update",this._updatePaths,this),this._destroyContainer()},getEvents:function(){var i={viewreset:this._reset,zoom:this._onZoom,moveend:this._update,zoomend:this._onZoomEnd};return this._zoomAnimated&&(i.zoomanim=this._onAnimZoom),i},_onAnimZoom:function(i){this._updateTransform(i.center,i.zoom)},_onZoom:function(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform:function(i,a){var s=this._map.getZoomScale(a,this._zoom),u=this._map.getSize().multiplyBy(.5+this.options.padding),c=this._map.project(this._center,a),h=u.multiplyBy(-s).add(c).subtract(this._map._getNewPixelOrigin(i,a));it.any3d?ke(this._container,h,s):Jt(this._container,h)},_reset:function(){this._update(),this._updateTransform(this._center,this._zoom);for(var i in this._layers)this._layers[i]._reset()},_onZoomEnd:function(){for(var i in this._layers)this._layers[i]._project()},_updatePaths:function(){for(var i in this._layers)this._layers[i]._update()},_update:function(){var i=this.options.padding,a=this._map.getSize(),s=this._map.containerPointToLayerPoint(a.multiplyBy(-i)).round();this._bounds=new B(s,s.add(a.multiplyBy(1+i*2)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom()}}),Ca=Fe.extend({options:{tolerance:0},getEvents:function(){var i=Fe.prototype.getEvents.call(this);return i.viewprereset=this._onViewPreReset,i},_onViewPreReset:function(){this._postponeUpdatePaths=!0},onAdd:function(){Fe.prototype.onAdd.call(this),this._draw()},_initContainer:function(){var i=this._container=document.createElement("canvas");ht(i,"mousemove",this._onMouseMove,this),ht(i,"click dblclick mousedown mouseup contextmenu",this._onClick,this),ht(i,"mouseout",this._handleMouseOut,this),i._leaflet_disable_events=!0,this._ctx=i.getContext("2d")},_destroyContainer:function(){Mt(this._redrawRequest),delete this._ctx,Pt(this._container),zt(this._container),delete this._container},_updatePaths:function(){if(!this._postponeUpdatePaths){var i;this._redrawBounds=null;for(var a in this._layers)i=this._layers[a],i._update();this._redraw()}},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){Fe.prototype._update.call(this);var i=this._bounds,a=this._container,s=i.getSize(),u=it.retina?2:1;Jt(a,i.min),a.width=u*s.x,a.height=u*s.y,a.style.width=s.x+"px",a.style.height=s.y+"px",it.retina&&this._ctx.scale(2,2),this._ctx.translate(-i.min.x,-i.min.y),this.fire("update")}},_reset:function(){Fe.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath:function(i){this._updateDashArray(i),this._layers[z(i)]=i;var a=i._order={layer:i,prev:this._drawLast,next:null};this._drawLast&&(this._drawLast.next=a),this._drawLast=a,this._drawFirst=this._drawFirst||this._drawLast},_addPath:function(i){this._requestRedraw(i)},_removePath:function(i){var a=i._order,s=a.next,u=a.prev;s?s.prev=u:this._drawLast=u,u?u.next=s:this._drawFirst=s,delete i._order,delete this._layers[z(i)],this._requestRedraw(i)},_updatePath:function(i){this._extendRedrawBounds(i),i._project(),i._update(),this._requestRedraw(i)},_updateStyle:function(i){this._updateDashArray(i),this._requestRedraw(i)},_updateDashArray:function(i){if(typeof i.options.dashArray=="string"){var a=i.options.dashArray.split(/[, ]+/),s=[],u,c;for(c=0;c<a.length;c++){if(u=Number(a[c]),isNaN(u))return;s.push(u)}i.options._dashArray=s}else i.options._dashArray=i.options.dashArray},_requestRedraw:function(i){this._map&&(this._extendRedrawBounds(i),this._redrawRequest=this._redrawRequest||mt(this._redraw,this))},_extendRedrawBounds:function(i){if(i._pxBounds){var a=(i.options.weight||0)+1;this._redrawBounds=this._redrawBounds||new B,this._redrawBounds.extend(i._pxBounds.min.subtract([a,a])),this._redrawBounds.extend(i._pxBounds.max.add([a,a]))}},_redraw:function(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear:function(){var i=this._redrawBounds;if(i){var a=i.getSize();this._ctx.clearRect(i.min.x,i.min.y,a.x,a.y)}else this._ctx.save(),this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._container.width,this._container.height),this._ctx.restore()},_draw:function(){var i,a=this._redrawBounds;if(this._ctx.save(),a){var s=a.getSize();this._ctx.beginPath(),this._ctx.rect(a.min.x,a.min.y,s.x,s.y),this._ctx.clip()}this._drawing=!0;for(var u=this._drawFirst;u;u=u.next)i=u.layer,(!a||i._pxBounds&&i._pxBounds.intersects(a))&&i._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly:function(i,a){if(this._drawing){var s,u,c,h,v=i._parts,w=v.length,C=this._ctx;if(w){for(C.beginPath(),s=0;s<w;s++){for(u=0,c=v[s].length;u<c;u++)h=v[s][u],C[u?"lineTo":"moveTo"](h.x,h.y);a&&C.closePath()}this._fillStroke(C,i)}}},_updateCircle:function(i){if(!(!this._drawing||i._empty())){var a=i._point,s=this._ctx,u=Math.max(Math.round(i._radius),1),c=(Math.max(Math.round(i._radiusY),1)||u)/u;c!==1&&(s.save(),s.scale(1,c)),s.beginPath(),s.arc(a.x,a.y/c,u,0,Math.PI*2,!1),c!==1&&s.restore(),this._fillStroke(s,i)}},_fillStroke:function(i,a){var s=a.options;s.fill&&(i.globalAlpha=s.fillOpacity,i.fillStyle=s.fillColor||s.color,i.fill(s.fillRule||"evenodd")),s.stroke&&s.weight!==0&&(i.setLineDash&&i.setLineDash(a.options&&a.options._dashArray||[]),i.globalAlpha=s.opacity,i.lineWidth=s.weight,i.strokeStyle=s.color,i.lineCap=s.lineCap,i.lineJoin=s.lineJoin,i.stroke())},_onClick:function(i){for(var a=this._map.mouseEventToLayerPoint(i),s,u,c=this._drawFirst;c;c=c.next)s=c.layer,s.options.interactive&&s._containsPoint(a)&&(!(i.type==="click"||i.type==="preclick")||!this._map._draggableMoved(s))&&(u=s);this._fireEvent(u?[u]:!1,i)},_onMouseMove:function(i){if(!(!this._map||this._map.dragging.moving()||this._map._animatingZoom)){var a=this._map.mouseEventToLayerPoint(i);this._handleMouseHover(i,a)}},_handleMouseOut:function(i){var a=this._hoveredLayer;a&&(kt(this._container,"leaflet-interactive"),this._fireEvent([a],i,"mouseout"),this._hoveredLayer=null,this._mouseHoverThrottled=!1)},_handleMouseHover:function(i,a){if(!this._mouseHoverThrottled){for(var s,u,c=this._drawFirst;c;c=c.next)s=c.layer,s.options.interactive&&s._containsPoint(a)&&(u=s);u!==this._hoveredLayer&&(this._handleMouseOut(i),u&&(_t(this._container,"leaflet-interactive"),this._fireEvent([u],i,"mouseover"),this._hoveredLayer=u)),this._fireEvent(this._hoveredLayer?[this._hoveredLayer]:!1,i),this._mouseHoverThrottled=!0,setTimeout(P(function(){this._mouseHoverThrottled=!1},this),32)}},_fireEvent:function(i,a,s){this._map._fireDOMEvent(a,s||a.type,i)},_bringToFront:function(i){var a=i._order;if(a){var s=a.next,u=a.prev;if(s)s.prev=u;else return;u?u.next=s:s&&(this._drawFirst=s),a.prev=this._drawLast,this._drawLast.next=a,a.next=null,this._drawLast=a,this._requestRedraw(i)}},_bringToBack:function(i){var a=i._order;if(a){var s=a.next,u=a.prev;if(u)u.next=s;else return;s?s.prev=u:u&&(this._drawLast=u),a.prev=null,a.next=this._drawFirst,this._drawFirst.prev=a,this._drawFirst=a,this._requestRedraw(i)}}});function Da(i){return it.canvas?new Ca(i):null}var un=function(){try{return document.namespaces.add("lvml","urn:schemas-microsoft-com:vml"),function(i){return document.createElement("<lvml:"+i+' class="lvml">')}}catch{}return function(i){return document.createElement("<"+i+' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')}}(),Vn={_initContainer:function(){this._container=Tt("div","leaflet-vml-container")},_update:function(){this._map._animatingZoom||(Fe.prototype._update.call(this),this.fire("update"))},_initPath:function(i){var a=i._container=un("shape");_t(a,"leaflet-vml-shape "+(this.options.className||"")),a.coordsize="1 1",i._path=un("path"),a.appendChild(i._path),this._updateStyle(i),this._layers[z(i)]=i},_addPath:function(i){var a=i._container;this._container.appendChild(a),i.options.interactive&&i.addInteractiveTarget(a)},_removePath:function(i){var a=i._container;Pt(a),i.removeInteractiveTarget(a),delete this._layers[z(i)]},_updateStyle:function(i){var a=i._stroke,s=i._fill,u=i.options,c=i._container;c.stroked=!!u.stroke,c.filled=!!u.fill,u.stroke?(a||(a=i._stroke=un("stroke")),c.appendChild(a),a.weight=u.weight+"px",a.color=u.color,a.opacity=u.opacity,u.dashArray?a.dashStyle=dt(u.dashArray)?u.dashArray.join(" "):u.dashArray.replace(/( *, *)/g," "):a.dashStyle="",a.endcap=u.lineCap.replace("butt","flat"),a.joinstyle=u.lineJoin):a&&(c.removeChild(a),i._stroke=null),u.fill?(s||(s=i._fill=un("fill")),c.appendChild(s),s.color=u.fillColor||u.color,s.opacity=u.fillOpacity):s&&(c.removeChild(s),i._fill=null)},_updateCircle:function(i){var a=i._point.round(),s=Math.round(i._radius),u=Math.round(i._radiusY||s);this._setPath(i,i._empty()?"M0 0":"AL "+a.x+","+a.y+" "+s+","+u+" 0,"+65535*360)},_setPath:function(i,a){i._path.v=a},_bringToFront:function(i){Ii(i._container)},_bringToBack:function(i){zn(i._container)}},Ra=it.vml?un:Gs,Zi=Fe.extend({_initContainer:function(){this._container=Ra("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=Ra("g"),this._container.appendChild(this._rootGroup)},_destroyContainer:function(){Pt(this._container),zt(this._container),delete this._container,delete this._rootGroup,delete this._svgSize},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){Fe.prototype._update.call(this);var i=this._bounds,a=i.getSize(),s=this._container;(!this._svgSize||!this._svgSize.equals(a))&&(this._svgSize=a,s.setAttribute("width",a.x),s.setAttribute("height",a.y)),Jt(s,i.min),s.setAttribute("viewBox",[i.min.x,i.min.y,a.x,a.y].join(" ")),this.fire("update")}},_initPath:function(i){var a=i._path=Ra("path");i.options.className&&_t(a,i.options.className),i.options.interactive&&_t(a,"leaflet-interactive"),this._updateStyle(i),this._layers[z(i)]=i},_addPath:function(i){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(i._path),i.addInteractiveTarget(i._path)},_removePath:function(i){Pt(i._path),i.removeInteractiveTarget(i._path),delete this._layers[z(i)]},_updatePath:function(i){i._project(),i._update()},_updateStyle:function(i){var a=i._path,s=i.options;a&&(s.stroke?(a.setAttribute("stroke",s.color),a.setAttribute("stroke-opacity",s.opacity),a.setAttribute("stroke-width",s.weight),a.setAttribute("stroke-linecap",s.lineCap),a.setAttribute("stroke-linejoin",s.lineJoin),s.dashArray?a.setAttribute("stroke-dasharray",s.dashArray):a.removeAttribute("stroke-dasharray"),s.dashOffset?a.setAttribute("stroke-dashoffset",s.dashOffset):a.removeAttribute("stroke-dashoffset")):a.setAttribute("stroke","none"),s.fill?(a.setAttribute("fill",s.fillColor||s.color),a.setAttribute("fill-opacity",s.fillOpacity),a.setAttribute("fill-rule",s.fillRule||"evenodd")):a.setAttribute("fill","none"))},_updatePoly:function(i,a){this._setPath(i,Ys(i._parts,a))},_updateCircle:function(i){var a=i._point,s=Math.max(Math.round(i._radius),1),u=Math.max(Math.round(i._radiusY),1)||s,c="a"+s+","+u+" 0 1,0 ",h=i._empty()?"M0 0":"M"+(a.x-s)+","+a.y+c+s*2+",0 "+c+-s*2+",0 ";this._setPath(i,h)},_setPath:function(i,a){i._path.setAttribute("d",a)},_bringToFront:function(i){Ii(i._path)},_bringToBack:function(i){zn(i._path)}});it.vml&&Zi.include(Vn);function cn(i){return it.svg||it.vml?new Zi(i):null}gt.include({getRenderer:function(i){var a=i.options.renderer||this._getPaneRenderer(i.options.pane)||this.options.renderer||this._renderer;return a||(a=this._renderer=this._createRenderer()),this.hasLayer(a)||this.addLayer(a),a},_getPaneRenderer:function(i){if(i==="overlayPane"||i===void 0)return!1;var a=this._paneRenderers[i];return a===void 0&&(a=this._createRenderer({pane:i}),this._paneRenderers[i]=a),a},_createRenderer:function(i){return this.options.preferCanvas&&Da(i)||cn(i)}});var To=Pn.extend({initialize:function(i,a){Pn.prototype.initialize.call(this,this._boundsToLatLngs(i),a)},setBounds:function(i){return this.setLatLngs(this._boundsToLatLngs(i))},_boundsToLatLngs:function(i){return i=J(i),[i.getSouthWest(),i.getNorthWest(),i.getNorthEast(),i.getSouthEast()]}});function Ue(i,a){return new To(i,a)}Zi.create=Ra,Zi.pointsToPath=Ys,Ze.geometryToLayer=za,Ze.coordsToLatLng=ol,Ze.coordsToLatLngs=Oa,Ze.latLngToCoords=Aa,Ze.latLngsToCoords=rl,Ze.getFeature=We,Ze.asFeature=kn,gt.mergeOptions({boxZoom:!0});var hl=Ke.extend({initialize:function(i){this._map=i,this._container=i._container,this._pane=i._panes.overlayPane,this._resetStateTimeout=0,i.on("unload",this._destroy,this)},addHooks:function(){ht(this._container,"mousedown",this._onMouseDown,this)},removeHooks:function(){zt(this._container,"mousedown",this._onMouseDown,this)},moved:function(){return this._moved},_destroy:function(){Pt(this._pane),delete this._pane},_resetState:function(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState:function(){this._resetStateTimeout!==0&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onMouseDown:function(i){if(!i.shiftKey||i.which!==1&&i.button!==1)return!1;this._clearDeferredResetState(),this._resetState(),si(),ba(),this._startPoint=this._map.mouseEventToContainerPoint(i),ht(document,{contextmenu:ui,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseMove:function(i){this._moved||(this._moved=!0,this._box=Tt("div","leaflet-zoom-box",this._container),_t(this._container,"leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.mouseEventToContainerPoint(i);var a=new B(this._point,this._startPoint),s=a.getSize();Jt(this._box,a.min),this._box.style.width=s.x+"px",this._box.style.height=s.y+"px"},_finish:function(){this._moved&&(Pt(this._box),kt(this._container,"leaflet-crosshair")),xa(),Vl(),zt(document,{contextmenu:ui,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseUp:function(i){if(!(i.which!==1&&i.button!==1)&&(this._finish(),!!this._moved)){this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(P(this._resetState,this),0);var a=new Y(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point));this._map.fitBounds(a).fire("boxzoomend",{boxZoomBounds:a})}},_onKeyDown:function(i){i.keyCode===27&&(this._finish(),this._clearDeferredResetState(),this._resetState())}});gt.addInitHook("addHandler","boxZoom",hl),gt.mergeOptions({doubleClickZoom:!0});var fi=Ke.extend({addHooks:function(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks:function(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick:function(i){var a=this._map,s=a.getZoom(),u=a.options.zoomDelta,c=i.originalEvent.shiftKey?s-u:s+u;a.options.doubleClickZoom==="center"?a.setZoom(c):a.setZoomAround(i.containerPoint,c)}});gt.addInitHook("addHandler","doubleClickZoom",fi),gt.mergeOptions({dragging:!0,inertia:!0,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0});var ls=Ke.extend({addHooks:function(){if(!this._draggable){var i=this._map;this._draggable=new Ri(i._mapPane,i._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),i.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),i.on("zoomend",this._onZoomEnd,this),i.whenReady(this._onZoomEnd,this))}_t(this._map._container,"leaflet-grab leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks:function(){kt(this._map._container,"leaflet-grab"),kt(this._map._container,"leaflet-touch-drag"),this._draggable.disable()},moved:function(){return this._draggable&&this._draggable._moved},moving:function(){return this._draggable&&this._draggable._moving},_onDragStart:function(){var i=this._map;if(i._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity){var a=J(this._map.options.maxBounds);this._offsetLimit=V(this._map.latLngToContainerPoint(a.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(a.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))}else this._offsetLimit=null;i.fire("movestart").fire("dragstart"),i.options.inertia&&(this._positions=[],this._times=[])},_onDrag:function(i){if(this._map.options.inertia){var a=this._lastTime=+new Date,s=this._lastPos=this._draggable._absPos||this._draggable._newPos;this._positions.push(s),this._times.push(a),this._prunePositions(a)}this._map.fire("move",i).fire("drag",i)},_prunePositions:function(i){for(;this._positions.length>1&&i-this._times[0]>50;)this._positions.shift(),this._times.shift()},_onZoomEnd:function(){var i=this._map.getSize().divideBy(2),a=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=a.subtract(i).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit:function(i,a){return i-(i-a)*this._viscosity},_onPreDragLimit:function(){if(!(!this._viscosity||!this._offsetLimit)){var i=this._draggable._newPos.subtract(this._draggable._startPos),a=this._offsetLimit;i.x<a.min.x&&(i.x=this._viscousLimit(i.x,a.min.x)),i.y<a.min.y&&(i.y=this._viscousLimit(i.y,a.min.y)),i.x>a.max.x&&(i.x=this._viscousLimit(i.x,a.max.x)),i.y>a.max.y&&(i.y=this._viscousLimit(i.y,a.max.y)),this._draggable._newPos=this._draggable._startPos.add(i)}},_onPreDragWrap:function(){var i=this._worldWidth,a=Math.round(i/2),s=this._initialWorldOffset,u=this._draggable._newPos.x,c=(u-a+s)%i+a-s,h=(u+a+s)%i-a-s,v=Math.abs(c+s)<Math.abs(h+s)?c:h;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=v},_onDragEnd:function(i){var a=this._map,s=a.options,u=!s.inertia||i.noInertia||this._times.length<2;if(a.fire("dragend",i),u)a.fire("moveend");else{this._prunePositions(+new Date);var c=this._lastPos.subtract(this._positions[0]),h=(this._lastTime-this._times[0])/1e3,v=s.easeLinearity,w=c.multiplyBy(v/h),C=w.distanceTo([0,0]),U=Math.min(s.inertiaMaxSpeed,C),K=w.multiplyBy(U/C),I=U/(s.inertiaDeceleration*v),tt=K.multiplyBy(-I/2).round();!tt.x&&!tt.y?a.fire("moveend"):(tt=a._limitOffset(tt,a.options.maxBounds),mt(function(){a.panBy(tt,{duration:I,easeLinearity:v,noMoveStart:!0,animate:!0})}))}}});gt.addInitHook("addHandler","dragging",ls),gt.mergeOptions({keyboard:!0,keyboardPanDelta:80});var Ba=Ke.extend({keyCodes:{left:[37],right:[39],down:[40],up:[38],zoomIn:[187,107,61,171],zoomOut:[189,109,54,173]},initialize:function(i){this._map=i,this._setPanDelta(i.options.keyboardPanDelta),this._setZoomDelta(i.options.zoomDelta)},addHooks:function(){var i=this._map._container;i.tabIndex<=0&&(i.tabIndex="0"),ht(i,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks:function(){this._removeHooks(),zt(this._map._container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onMouseDown:function(){if(!this._focused){var i=document.body,a=document.documentElement,s=i.scrollTop||a.scrollTop,u=i.scrollLeft||a.scrollLeft;this._map._container.focus(),window.scrollTo(u,s)}},_onFocus:function(){this._focused=!0,this._map.fire("focus")},_onBlur:function(){this._focused=!1,this._map.fire("blur")},_setPanDelta:function(i){var a=this._panKeys={},s=this.keyCodes,u,c;for(u=0,c=s.left.length;u<c;u++)a[s.left[u]]=[-1*i,0];for(u=0,c=s.right.length;u<c;u++)a[s.right[u]]=[i,0];for(u=0,c=s.down.length;u<c;u++)a[s.down[u]]=[0,i];for(u=0,c=s.up.length;u<c;u++)a[s.up[u]]=[0,-1*i]},_setZoomDelta:function(i){var a=this._zoomKeys={},s=this.keyCodes,u,c;for(u=0,c=s.zoomIn.length;u<c;u++)a[s.zoomIn[u]]=i;for(u=0,c=s.zoomOut.length;u<c;u++)a[s.zoomOut[u]]=-i},_addHooks:function(){ht(document,"keydown",this._onKeyDown,this)},_removeHooks:function(){zt(document,"keydown",this._onKeyDown,this)},_onKeyDown:function(i){if(!(i.altKey||i.ctrlKey||i.metaKey)){var a=i.keyCode,s=this._map,u;if(a in this._panKeys){if(!s._panAnim||!s._panAnim._inProgress)if(u=this._panKeys[a],i.shiftKey&&(u=m(u).multiplyBy(3)),s.options.maxBounds&&(u=s._limitOffset(m(u),s.options.maxBounds)),s.options.worldCopyJump){var c=s.wrapLatLng(s.unproject(s.project(s.getCenter()).add(u)));s.panTo(c)}else s.panBy(u)}else if(a in this._zoomKeys)s.setZoom(s.getZoom()+(i.shiftKey?3:1)*this._zoomKeys[a]);else if(a===27&&s._popup&&s._popup.options.closeOnEscapeKey)s.closePopup();else return;ui(i)}}});gt.addInitHook("addHandler","keyboard",Ba),gt.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60});var ji=Ke.extend({addHooks:function(){ht(this._map._container,"wheel",this._onWheelScroll,this),this._delta=0},removeHooks:function(){zt(this._map._container,"wheel",this._onWheelScroll,this)},_onWheelScroll:function(i){var a=wa(i),s=this._map.options.wheelDebounceTime;this._delta+=a,this._lastMousePos=this._map.mouseEventToContainerPoint(i),this._startTime||(this._startTime=+new Date);var u=Math.max(s-(+new Date-this._startTime),0);clearTimeout(this._timer),this._timer=setTimeout(P(this._performZoom,this),u),ui(i)},_performZoom:function(){var i=this._map,a=i.getZoom(),s=this._map.options.zoomSnap||0;i._stop();var u=this._delta/(this._map.options.wheelPxPerZoomLevel*4),c=4*Math.log(2/(1+Math.exp(-Math.abs(u))))/Math.LN2,h=s?Math.ceil(c/s)*s:c,v=i._limitZoom(a+(this._delta>0?h:-h))-a;this._delta=0,this._startTime=null,v&&(i.options.scrollWheelZoom==="center"?i.setZoom(a+v):i.setZoomAround(this._lastMousePos,a+v))}});gt.addInitHook("addHandler","scrollWheelZoom",ji);var ss=600;gt.mergeOptions({tapHold:it.touchNative&&it.safari&&it.mobile,tapTolerance:15});var dl=Ke.extend({addHooks:function(){ht(this._map._container,"touchstart",this._onDown,this)},removeHooks:function(){zt(this._map._container,"touchstart",this._onDown,this)},_onDown:function(i){if(clearTimeout(this._holdTimeout),i.touches.length===1){var a=i.touches[0];this._startPos=this._newPos=new G(a.clientX,a.clientY),this._holdTimeout=setTimeout(P(function(){this._cancel(),this._isTapValid()&&(ht(document,"touchend",te),ht(document,"touchend touchcancel",this._cancelClickPrevent),this._simulateEvent("contextmenu",a))},this),ss),ht(document,"touchend touchcancel contextmenu",this._cancel,this),ht(document,"touchmove",this._onMove,this)}},_cancelClickPrevent:function i(){zt(document,"touchend",te),zt(document,"touchend touchcancel",i)},_cancel:function(){clearTimeout(this._holdTimeout),zt(document,"touchend touchcancel contextmenu",this._cancel,this),zt(document,"touchmove",this._onMove,this)},_onMove:function(i){var a=i.touches[0];this._newPos=new G(a.clientX,a.clientY)},_isTapValid:function(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_simulateEvent:function(i,a){var s=new MouseEvent(i,{bubbles:!0,cancelable:!0,view:window,screenX:a.screenX,screenY:a.screenY,clientX:a.clientX,clientY:a.clientY});s._simulated=!0,a.target.dispatchEvent(s)}});gt.addInitHook("addHandler","tapHold",dl),gt.mergeOptions({touchZoom:it.touch,bounceAtZoomLimits:!0});var fn=Ke.extend({addHooks:function(){_t(this._map._container,"leaflet-touch-zoom"),ht(this._map._container,"touchstart",this._onTouchStart,this)},removeHooks:function(){kt(this._map._container,"leaflet-touch-zoom"),zt(this._map._container,"touchstart",this._onTouchStart,this)},_onTouchStart:function(i){var a=this._map;if(!(!i.touches||i.touches.length!==2||a._animatingZoom||this._zooming)){var s=a.mouseEventToContainerPoint(i.touches[0]),u=a.mouseEventToContainerPoint(i.touches[1]);this._centerPoint=a.getSize()._divideBy(2),this._startLatLng=a.containerPointToLatLng(this._centerPoint),a.options.touchZoom!=="center"&&(this._pinchStartLatLng=a.containerPointToLatLng(s.add(u)._divideBy(2))),this._startDist=s.distanceTo(u),this._startZoom=a.getZoom(),this._moved=!1,this._zooming=!0,a._stop(),ht(document,"touchmove",this._onTouchMove,this),ht(document,"touchend touchcancel",this._onTouchEnd,this),te(i)}},_onTouchMove:function(i){if(!(!i.touches||i.touches.length!==2||!this._zooming)){var a=this._map,s=a.mouseEventToContainerPoint(i.touches[0]),u=a.mouseEventToContainerPoint(i.touches[1]),c=s.distanceTo(u)/this._startDist;if(this._zoom=a.getScaleZoom(c,this._startZoom),!a.options.bounceAtZoomLimits&&(this._zoom<a.getMinZoom()&&c<1||this._zoom>a.getMaxZoom()&&c>1)&&(this._zoom=a._limitZoom(this._zoom)),a.options.touchZoom==="center"){if(this._center=this._startLatLng,c===1)return}else{var h=s._add(u)._divideBy(2)._subtract(this._centerPoint);if(c===1&&h.x===0&&h.y===0)return;this._center=a.unproject(a.project(this._pinchStartLatLng,this._zoom).subtract(h),this._zoom)}this._moved||(a._moveStart(!0,!1),this._moved=!0),Mt(this._animRequest);var v=P(a._move,a,this._center,this._zoom,{pinch:!0,round:!1},void 0);this._animRequest=mt(v,this,!0),te(i)}},_onTouchEnd:function(){if(!this._moved||!this._zooming){this._zooming=!1;return}this._zooming=!1,Mt(this._animRequest),zt(document,"touchmove",this._onTouchMove,this),zt(document,"touchend touchcancel",this._onTouchEnd,this),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))}});gt.addInitHook("addHandler","touchZoom",fn),gt.BoxZoom=hl,gt.DoubleClickZoom=fi,gt.Drag=ls,gt.Keyboard=Ba,gt.ScrollWheelZoom=ji,gt.TapHold=dl,gt.TouchZoom=fn,x.Bounds=B,x.Browser=it,x.CRS=Kt,x.Canvas=Ca,x.Circle=is,x.CircleMarker=La,x.Class=fe,x.Control=Ae,x.DivIcon=as,x.DivOverlay=Ie,x.DomEvent=Lr,x.DomUtil=ao,x.Draggable=Ri,x.Evented=Q,x.FeatureGroup=Re,x.GeoJSON=Ze,x.GridLayer=on,x.Handler=Ke,x.Icon=Hn,x.ImageOverlay=ci,x.LatLng=F,x.LatLngBounds=Y,x.Layer=Je,x.LayerGroup=ln,x.LineUtil=mo,x.Map=gt,x.Marker=qn,x.Mixin=Cr,x.Path=Si,x.Point=G,x.PolyUtil=Dr,x.Polygon=Pn,x.Polyline=Ti,x.Popup=Na,x.PosAnimation=el,x.Projection=_o,x.Rectangle=To,x.Renderer=Fe,x.SVG=Zi,x.SVGOverlay=Bi,x.TileLayer=rn,x.Tooltip=fl,x.Transformation=bn,x.Util=pi,x.VideoOverlay=cl,x.bind=P,x.bounds=V,x.canvas=Da,x.circle=Ur,x.circleMarker=go,x.control=Zn,x.divIcon=So,x.extend=R,x.featureGroup=Ea,x.geoJSON=ul,x.geoJson=yo,x.gridLayer=$e,x.icon=sl,x.imageOverlay=Gn,x.latLng=X,x.latLngBounds=J,x.layerGroup=vo,x.map=Ma,x.marker=es,x.point=m,x.polygon=Be,x.polyline=qr,x.popup=Pr,x.rectangle=Ue,x.setOptions=rt,x.stamp=z,x.svg=cn,x.svgOverlay=bo,x.tileLayer=je,x.tooltip=kr,x.transformation=Gi,x.version=p,x.videoOverlay=xo;var Xn=window.L;x.noConflict=function(){return window.L=Xn,this},window.L=x})}(ks,ks.exports)),ks.exports}var tp=F_();const zi=Dd(tp),ep=({map:_})=>{const[T,x]=jt.useState(!0),{selectedMapType:p,setSelectedMapType:R,species:q,toggleSpeciesVisibility:P}=xn(),at=[{id:"terrain",name:"地形图",url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"},{id:"streets",name:"街道图",url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"},{id:"satellite",name:"卫星图",url:"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"},{id:"hybrid",name:"混合图",url:"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"}],z=[{range:"600+",color:"bg-red-700",count:"600+"},{range:"500-600",color:"bg-red-600",count:"500 - 600"},{range:"400-500",color:"bg-red-500",count:"400 - 500"},{range:"300-400",color:"bg-orange-500",count:"300 - 400"},{range:"250-300",color:"bg-orange-400",count:"250 - 300"},{range:"200-250",color:"bg-yellow-500",count:"200 - 250"},{range:"150-200",color:"bg-yellow-400",count:"150 - 200"},{range:"100-150",color:"bg-yellow-300",count:"100 - 150"},{range:"50-100",color:"bg-green-300",count:"50 - 100"},{range:"15-50",color:"bg-green-200",count:"15 - 50"},{range:"0-15",color:"bg-green-100",count:"0 - 15"}],A=k=>{_&&(R(k.id),_.eachLayer(W=>{W instanceof zi.TileLayer&&_.removeLayer(W)}),zi.tileLayer(k.url,{attribution:k.id==="satellite"||k.id==="hybrid"?"© Esri":"© OpenStreetMap contributors",maxZoom:18}).addTo(_))};return y.jsx("div",{className:"absolute top-4 right-4 z-[1000]",children:y.jsxs("div",{className:"map-controls",children:[y.jsx("button",{onClick:()=>x(!T),className:"flex items-center justify-center w-8 h-8 bg-white rounded-md shadow-md hover:bg-gray-50 mb-2",children:y.jsx(P_,{className:"h-4 w-4 text-gray-600"})}),T&&y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("h6",{className:"text-sm font-semibold text-gray-900 mb-2",children:"地图类型"}),y.jsx("div",{className:"space-y-1",children:at.map(k=>y.jsxs("label",{className:"flex items-center cursor-pointer",children:[y.jsx("input",{type:"radio",name:"mapType",value:k.id,checked:p===k.id,onChange:()=>A(k),className:"mr-2 text-blue-600"}),y.jsx("span",{className:"text-sm text-gray-700",children:k.name})]},k.id))})]}),y.jsxs("div",{children:[y.jsx("div",{className:"flex items-center mb-2",children:y.jsx("h5",{className:"text-sm font-semibold text-gray-900",children:"物种图层"})}),y.jsx("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:q.map(k=>y.jsxs("div",{className:"flex items-center justify-between",children:[y.jsxs("div",{className:"flex items-center",children:[y.jsx("div",{className:"w-3 h-3 rounded-full mr-2 border border-gray-300",style:{backgroundColor:k.color}}),y.jsx("span",{className:"text-xs text-gray-700",children:k.name})]}),y.jsx("button",{onClick:()=>P(k.id),className:"p-1 hover:bg-gray-100 rounded",children:k.isVisible?y.jsx(Bd,{className:"h-3 w-3 text-gray-600"}):y.jsx(S_,{className:"h-3 w-3 text-gray-400"})})]},k.id))})]}),y.jsxs("div",{children:[y.jsxs("div",{className:"flex items-center mb-2",children:[y.jsx("h5",{className:"text-sm font-semibold text-gray-900",children:"观察到的鸟种"}),y.jsx("button",{className:"ml-2",children:y.jsx(z_,{className:"h-3 w-3 text-gray-500"})})]}),y.jsx("div",{className:"space-y-1",children:z.map(k=>y.jsxs("div",{className:"flex items-center",children:[y.jsx("div",{className:`w-4 h-3 ${k.color} mr-2 border border-gray-300`}),y.jsx("span",{className:"text-xs text-gray-700",children:k.count})]},k.range))})]})]})]})})};function ip(_){return _<=4?2:_<=6?1:_<=8?.5:_<=10?.2:_<=12?.1:_<=14?.05:.02}const Me={POINTS_THRESHOLD:12,HIGH_DETAIL_THRESHOLD:15,ULTRA_DETAIL_THRESHOLD:18,MAX_POINTS_DISPLAY:1200,MAX_POINTS_HIGH_DETAIL:2500,MAX_POINTS_ULTRA_DETAIL:4500,VIEWPORT_PRIORITY_POINTS:600};function wc(_,T=0){const p=5e8/Math.pow(2,_),R=Math.cos(T*Math.PI/180);return Math.round(p*R)}function Sc(_,T,x){const p=Me.POINTS_THRESHOLD;return _>=p||T!==void 0&&wc(_,T)<=5e4||_>=p-1?"points":"grid"}function Ad(_){return _>=Me.HIGH_DETAIL_THRESHOLD}function np(_){return _>=Me.ULTRA_DETAIL_THRESHOLD}function ap(_){return _>=Me.ULTRA_DETAIL_THRESHOLD?"ultra":_>=Me.HIGH_DETAIL_THRESHOLD?"high":_>=Me.POINTS_THRESHOLD?"points":"grid"}function lp(_,T,x){return{gridLat:Math.floor(_/x)*x,gridLng:Math.floor(T/x)*x}}function sp(_,T,x){return{north:_+x,south:_,east:T+x,west:T}}function op(_,T){return`${_.toFixed(6)},${T.toFixed(6)}`}function rp(_,T,x){const p=new Map;return(x?_.filter(q=>x.includes(q.speciesId)):_).forEach(q=>{const{gridLat:P,gridLng:at}=lp(q.lat,q.lng,T),z=op(P,at);p.has(z)||p.set(z,{gridLat:P,gridLng:at,bounds:sp(P,at,T),totalCount:0,observationCount:0,averageIntensity:0,speciesIds:[],observations:[]});const A=p.get(z);A.totalCount+=q.count,A.observationCount+=1,A.observations.push(q),A.speciesIds.includes(q.speciesId)||A.speciesIds.push(q.speciesId)}),p.forEach(q=>{q.observationCount>0&&(q.averageIntensity=q.observations.reduce((P,at)=>P+at.intensity,0)/q.observationCount)}),p}function up(_,T){const x=new Map;return _.forEach((p,R)=>{p.bounds.north>=T.south&&p.bounds.south<=T.north&&p.bounds.east>=T.west&&p.bounds.west<=T.east&&x.set(R,p)}),x}function cp(_,T){const x=T[0],p=T[1],R=_.gridLat+(_.bounds.north-_.bounds.south)/2,q=_.gridLng+(_.bounds.east-_.bounds.west)/2,P=Math.sqrt(Math.pow(R-x,2)+Math.pow(q-p,2));return _.totalCount/(P+.1)}function fp(_){const T=_.speciesIds.length,x=(_.averageIntensity*100).toFixed(1);return`
    <div class="text-sm p-2">
      <div class="font-bold text-gray-800 mb-2">网格区域统计</div>
      <div class="space-y-1">
        <div class="flex justify-between">
          <span class="text-gray-700">观察总数:</span>
          <span class="font-bold text-blue-600">${_.totalCount}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-700">观察点数:</span>
          <span class="font-medium text-green-600">${_.observationCount}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-700">物种数量:</span>
          <span class="font-medium text-purple-600">${T}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-700">平均强度:</span>
          <span class="font-medium text-orange-600">${x}%</span>
        </div>
      </div>
    </div>
  `}function hp(_,T=1e3,x){if(_.size<=T)return _;const p=Array.from(_.entries()).map(([q,P])=>({key:q,cell:P,priority:cp(P,x)})).sort((q,P)=>P.priority-q.priority).slice(0,T),R=new Map;return p.forEach(({key:q,cell:P})=>{R.set(q,P)}),R}function Nd(_,T){return _.lat>=T.south&&_.lat<=T.north&&_.lng>=T.west&&_.lng<=T.east}function dp(_,T,x,p=Me.VIEWPORT_PRIORITY_POINTS){const R=[],q=[],P=T.north-T.south,at=T.east-T.west,z={north:T.north+P*.5,south:T.south-P*.5,east:T.east+at*.5,west:T.west-at*.5};return _.forEach(A=>{Nd(A,T)?R.push(A):Nd(A,z)&&q.push(A)}),q.sort((A,k)=>{const W=Cd(A,x);return Cd(k,x)-W}),{inViewport:R,nearViewport:q.slice(0,p)}}function Cd(_,T){const p=1/(1+Math.sqrt(Math.pow(_.lat-T[0],2)+Math.pow(_.lng-T[1],2))),R=Math.log10(Math.max(1,_.count)),q=_.intensity;return p*.4+R*.4+q*.2}const Mc=[{min:0,max:15,color:"#e8f5e8",label:"0-15"},{min:16,max:50,color:"#c8e6c9",label:"16-50"},{min:51,max:100,color:"#a5d6a7",label:"51-100"},{min:101,max:150,color:"#81c784",label:"101-150"},{min:151,max:200,color:"#66bb6a",label:"151-200"},{min:201,max:300,color:"#4caf50",label:"201-300"},{min:301,max:400,color:"#43a047",label:"301-400"},{min:401,max:500,color:"#388e3c",label:"401-500"},{min:501,max:600,color:"#2e7d32",label:"501-600"},{min:601,max:1/0,color:"#1b5e20",label:"600+"}];function Hd(_,T=Mc){const x=T.find(p=>_>=p.min&&_<=p.max);return x?x.color:T[T.length-1].color}function mp(_,T=15,x=6,p=20){const R=Math.max(.5,Math.min(2,(T-12)/6)),q=Math.round(x*R),P=Math.round(p*R),at=Math.log10(Math.max(1,_)),z=Math.log10(1e3),A=Math.min(at/z,1);return Math.round(q+(P-q)*A)}function Ud(_,T=.6,x=.9){const p=Math.log10(Math.max(1,_)),R=Math.log10(1e3),q=Math.min(p/R,1);return T+(x-T)*q}function _p(_,T=.5,x=Mc){const p=Hd(_,x),R=Ud(_),q=Math.max(.4,Math.min(1,R*(.7+T*.3))),P=pp(p,.2);return{color:p,opacity:q,borderColor:P}}function pp(_,T){const x=_.replace("#",""),p=parseInt(x.substr(0,2),16),R=parseInt(x.substr(2,2),16),q=parseInt(x.substr(4,2),16),P=Math.round(p*(1-T)),at=Math.round(R*(1-T)),z=Math.round(q*(1-T));return`#${P.toString(16).padStart(2,"0")}${at.toString(16).padStart(2,"0")}${z.toString(16).padStart(2,"0")}`}const vp=({map:_})=>{const T=jt.useRef(null),x=jt.useRef(5),p=jt.useRef("grid"),{species:R,observations:q,searchQuery:P,mapZoom:at}=xn(),z=jt.useCallback(()=>{T.current&&_&&(_.hasLayer(T.current)&&_.removeLayer(T.current),T.current.clearLayers())},[_]),A=jt.useCallback((st,rt)=>{const Dt=[[st.bounds.south,st.bounds.west],[st.bounds.north,st.bounds.east]],ge=Hd(st.totalCount),$t=Ud(st.totalCount),dt=zi.rectangle(Dt,{fillColor:ge,fillOpacity:$t,color:"#ffffff",weight:1,opacity:.8});return dt.bindPopup(fp(st),{maxWidth:250,className:"grid-popup"}),dt.on("mouseover",function(){this.setStyle({weight:2,opacity:1,fillOpacity:Math.min($t+.2,1)})}),dt.on("mouseout",function(){this.setStyle({weight:1,opacity:.8,fillOpacity:$t})}),dt},[]),k=jt.useCallback((st,rt,Dt)=>{const ge=Ad(Dt),$t=_p(st.count,st.intensity),dt=mp(st.count,Dt),Lt=ge?2:1,ut=ge?.4:.3,qt=zi.divIcon({className:"observation-point-marker square-marker",html:`<div class="observation-square" style="
        width: ${dt}px;
        height: ${dt}px;
        background-color: ${$t.color};
        opacity: ${$t.opacity};
        border: ${Lt}px solid ${$t.borderColor};
        border-radius: 2px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, ${ut});
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
      "></div>`,iconSize:[dt+Lt*2,dt+Lt*2],iconAnchor:[(dt+Lt*2)/2,(dt+Lt*2)/2],popupAnchor:[0,-(dt+Lt*2)/2]}),Ht=zi.marker([st.lat,st.lng],{icon:qt,riseOnHover:!0});Ht.on("mouseover",function(){const ae=this.getElement();if(ae){const mt=ae.querySelector(".observation-square");mt&&(mt.style.transform="scale(1.3)",mt.style.boxShadow=`0 4px 8px rgba(0, 0, 0, ${ut+.2})`,mt.style.zIndex="1000",mt.style.borderWidth=`${Lt+1}px`)}}),Ht.on("mouseout",function(){const ae=this.getElement();if(ae){const mt=ae.querySelector(".observation-square");mt&&(mt.style.transform="scale(1)",mt.style.boxShadow=`0 2px 4px rgba(0, 0, 0, ${ut})`,mt.style.zIndex="auto",mt.style.borderWidth=`${Lt}px`)}});const Qt=new Date().toLocaleDateString("zh-CN"),Ft=wc(Dt,st.lat);return Ht.bindPopup(`
      <div class="text-sm p-4 min-w-[280px] max-w-[320px]">
        <div class="border-b pb-3 mb-3">
          <div class="font-bold text-gray-800 text-base mb-1">${rt.name}</div>
          <div class="text-gray-600 italic text-sm">${rt.scientificName}</div>
        </div>

        <div class="space-y-3">
          <div class="grid grid-cols-2 gap-3">
            <div class="bg-blue-50 p-2 rounded">
              <div class="text-xs text-gray-500 mb-1">观察数量</div>
              <div class="font-bold text-blue-600 text-lg">${st.count}</div>
            </div>
            <div class="bg-green-50 p-2 rounded">
              <div class="text-xs text-gray-500 mb-1">观察强度</div>
              <div class="font-bold text-green-600 text-lg">${(st.intensity*100).toFixed(1)}%</div>
            </div>
          </div>

          <div class="border-t pt-3">
            <div class="text-xs text-gray-500 mb-2 font-medium">精确坐标信息</div>
            <div class="space-y-1">
              <div class="flex items-center justify-between text-xs">
                <span class="text-gray-600">纬度:</span>
                <span class="font-mono text-gray-800 bg-gray-100 px-2 py-1 rounded">${st.lat.toFixed(6)}°</span>
              </div>
              <div class="flex items-center justify-between text-xs">
                <span class="text-gray-600">经度:</span>
                <span class="font-mono text-gray-800 bg-gray-100 px-2 py-1 rounded">${st.lng.toFixed(6)}°</span>
              </div>
              <div class="flex items-center justify-between text-xs">
                <span class="text-gray-600">缩放级别:</span>
                <span class="font-mono text-gray-800">${Dt}</span>
              </div>
              <div class="flex items-center justify-between text-xs">
                <span class="text-gray-600">地图比例尺:</span>
                <span class="font-mono text-gray-800">1:${Ft.toLocaleString()}</span>
              </div>
            </div>
          </div>

          <div class="border-t pt-2">
            <div class="text-xs text-gray-400 text-center">
              数据更新时间: ${Qt}
            </div>
          </div>
        </div>
      </div>
    `,{maxWidth:350,className:"enhanced-observation-popup",closeButton:!0,autoPan:!0}),Ht},[]),W=jt.useCallback(()=>{if(!_)return;z();const st=zi.layerGroup();T.current=st;const rt=_.getZoom(),Dt=_.getCenter(),ge=Sc(rt,Dt.lat),$t=ip(rt),dt=R.filter(Lt=>{if(!Lt.isVisible)return!1;if(P.trim()){const ut=P.toLowerCase();return Lt.name.toLowerCase().includes(ut)||Lt.scientificName.toLowerCase().includes(ut)}return!0});if(ge==="points"){const Lt={north:_.getBounds().getNorth(),south:_.getBounds().getSouth(),east:_.getBounds().getEast(),west:_.getBounds().getWest()},ut=Ad(rt),qt=np(rt);let Ht=Me.MAX_POINTS_DISPLAY;qt?Ht=Me.MAX_POINTS_ULTRA_DETAIL:ut&&(Ht=Me.MAX_POINTS_HIGH_DETAIL),dt.forEach(Qt=>{let Ft=q.filter(ae=>ae.speciesId===Qt.id);if(Ft.length>0){const{inViewport:ae,nearViewport:mt}=dp(Ft,Lt,[Dt.lat,Dt.lng],Me.VIEWPORT_PRIORITY_POINTS),Mt=[...ae,...mt],pi=Math.ceil(Ht/dt.length);Mt.slice(0,pi).forEach(le=>{const D=k(le,Qt,rt);st.addLayer(D)})}})}else{const Lt=dt.map(mt=>mt.id),ut=rp(q,$t,Lt),qt=_.getBounds(),Ht={north:qt.getNorth(),south:qt.getSouth(),east:qt.getEast(),west:qt.getWest()},Qt=up(ut,Ht),Ft=_.getCenter();hp(Qt,1e3,[Ft.lat,Ft.lng]).forEach(mt=>{if(mt.totalCount>0){const Mt=A(mt,"多物种聚合");st.addLayer(Mt)}})}st.addTo(_),x.current=rt,p.current=ge},[_,R,q,P,z,A,k]),et=jt.useCallback(()=>{if(!_)return;const st=_.getZoom(),rt=Sc(st);(Math.abs(st-x.current)>=1||rt!==p.current)&&W()},[_,W]),St=jt.useCallback(()=>{_&&p.current==="grid"&&W()},[_,W]);return jt.useEffect(()=>{if(_)return W(),_.on("zoomend",et),_.on("moveend",St),()=>{_.off("zoomend",et),_.off("moveend",St),z()}},[_,R,q,P,W,et,St,z]),jt.useEffect(()=>{_&&at!==x.current&&et()},[_,at,et]),null},gp=()=>{const[_,T]=jt.useState(!1),[x,p]=jt.useState(""),{species:R,toggleSpeciesVisibility:q}=xn(),P=R.filter(z=>z.name.toLowerCase().includes(x.toLowerCase())||z.scientificName.toLowerCase().includes(x.toLowerCase())),at=R.filter(z=>z.isVisible).length;return y.jsx("div",{className:"absolute top-20 left-4 z-[1000] max-w-xs sm:max-w-sm animate-slide-up",children:y.jsxs("div",{className:"bg-white/95 backdrop-blur-sm rounded-xl shadow-large border border-white/20 overflow-hidden",children:[y.jsxs("button",{onClick:()=>T(!_),className:"flex items-center justify-between w-full px-5 py-4 text-left hover:bg-gradient-to-r hover:from-primary-50 hover:to-primary-100/50 transition-all duration-200 group",children:[y.jsxs("div",{className:"flex items-center",children:[y.jsx("div",{className:"flex items-center justify-center w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg mr-3 group-hover:scale-105 transition-transform duration-200",children:y.jsx(Ed,{className:"h-4 w-4 text-white"})}),y.jsxs("span",{className:"text-sm font-semibold text-gray-900",children:["物种筛选 (",at,"/",R.length,")"]})]}),y.jsx(jd,{className:`h-5 w-5 text-gray-400 transition-all duration-300 group-hover:text-primary-600 ${_?"rotate-45 text-primary-600":""}`})]}),_&&y.jsxs("div",{className:"border-t border-gray-200/50 p-5 w-full sm:w-80 animate-fade-in",children:[y.jsxs("div",{className:"relative mb-5 group",children:[y.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl blur opacity-20 group-hover:opacity-30 transition-opacity duration-200"}),y.jsxs("div",{className:"relative",children:[y.jsx(Zd,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 group-hover:text-primary-500 transition-colors duration-200"}),y.jsx("input",{type:"text",placeholder:"搜索物种...",value:x,onChange:z=>p(z.target.value),className:"w-full pl-11 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm bg-white shadow-soft hover:shadow-medium transition-all duration-200"})]})]}),y.jsx("div",{className:"space-y-2 max-h-60 overflow-y-auto custom-scrollbar",children:P.map(z=>y.jsxs("div",{className:"flex items-center justify-between p-3 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100/50 rounded-lg cursor-pointer transition-all duration-200 group border border-transparent hover:border-gray-200/50",onClick:()=>q(z.id),children:[y.jsxs("div",{className:"flex items-center flex-1",children:[y.jsx("div",{className:"w-5 h-5 rounded-full mr-3 border-2 border-white shadow-sm flex-shrink-0 transition-all duration-200 group-hover:scale-110",style:{backgroundColor:z.isVisible?z.color:"transparent",borderColor:z.isVisible?z.color:"#d1d5db"}}),y.jsxs("div",{className:"flex-1 min-w-0",children:[y.jsx("div",{className:"text-sm font-semibold text-gray-900 truncate group-hover:text-primary-700 transition-colors duration-200",children:z.name}),y.jsx("div",{className:"text-xs text-gray-500 truncate",children:z.scientificName})]})]}),y.jsx("input",{type:"checkbox",checked:z.isVisible,onChange:()=>q(z.id),className:"ml-3 w-4 h-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded transition-all duration-200",onClick:A=>A.stopPropagation()})]},z.id))}),P.length===0&&y.jsxs("div",{className:"text-center py-6 text-gray-500 text-sm bg-gray-50/50 rounded-lg border border-gray-200/50",children:[y.jsx(Ed,{className:"h-8 w-8 text-gray-300 mx-auto mb-2"}),"未找到匹配的物种"]}),y.jsx("div",{className:"mt-5 pt-4 border-t border-gray-200/50",children:y.jsxs("div",{className:"flex space-x-3",children:[y.jsx("button",{onClick:()=>{R.forEach(z=>{z.isVisible||q(z.id)})},className:"flex-1 px-4 py-2 text-sm bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-lg hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-soft hover:shadow-medium font-medium",children:"全选"}),y.jsx("button",{onClick:()=>{R.forEach(z=>{z.isVisible&&q(z.id)})},className:"flex-1 px-4 py-2 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-lg hover:from-gray-200 hover:to-gray-300 transition-all duration-200 shadow-soft hover:shadow-medium font-medium",children:"全不选"})]})})]})]})})},yp=()=>{const{species:_,observations:T,searchQuery:x}=xn(),p=_.filter(z=>{if(!z.isVisible)return!1;if(x.trim()){const A=x.toLowerCase();return z.name.toLowerCase().includes(A)||z.scientificName.toLowerCase().includes(A)}return!0}),R=T.filter(z=>p.some(A=>A.id===z.speciesId)),q=R.length,P=R.reduce((z,A)=>z+A.count,0),at=q>0?Math.round(P/q):0;return y.jsx("div",{className:"absolute bottom-4 left-4 z-[1000] max-w-xs animate-slide-up",children:y.jsxs("div",{className:"bg-white/95 backdrop-blur-sm rounded-xl shadow-large border border-white/20 p-4 sm:p-5 hover:shadow-xl transition-all duration-300",children:[y.jsxs("div",{className:"flex items-center mb-4",children:[y.jsx("div",{className:"flex items-center justify-center w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg mr-3",children:y.jsx(g_,{className:"h-4 w-4 text-white"})}),y.jsx("h3",{className:"text-base font-bold text-gray-900",children:"观察统计"})]}),y.jsxs("div",{className:"space-y-3 text-sm",children:[y.jsxs("div",{className:"flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-blue-100/50 rounded-lg border border-blue-200/50",children:[y.jsxs("div",{className:"flex items-center",children:[y.jsx("div",{className:"flex items-center justify-center w-6 h-6 bg-blue-500 rounded-md mr-3",children:y.jsx(Bd,{className:"h-3 w-3 text-white"})}),y.jsx("span",{className:"text-gray-700 font-medium",children:"显示物种:"})]}),y.jsxs("span",{className:"font-bold text-blue-700 text-base",children:[p.length,"/",_.length]})]}),y.jsxs("div",{className:"flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-green-100/50 rounded-lg border border-green-200/50",children:[y.jsxs("div",{className:"flex items-center",children:[y.jsx("div",{className:"flex items-center justify-center w-6 h-6 bg-green-500 rounded-md mr-3",children:y.jsx(R_,{className:"h-3 w-3 text-white"})}),y.jsx("span",{className:"text-gray-700 font-medium",children:"观察点:"})]}),y.jsx("span",{className:"font-bold text-green-700 text-base",children:q.toLocaleString()})]}),y.jsxs("div",{className:"flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-purple-100/50 rounded-lg border border-purple-200/50",children:[y.jsx("span",{className:"text-gray-700 font-medium",children:"总观察数:"}),y.jsx("span",{className:"font-bold text-purple-700 text-base",children:P.toLocaleString()})]}),y.jsxs("div",{className:"flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-orange-100/50 rounded-lg border border-orange-200/50",children:[y.jsx("span",{className:"text-gray-700 font-medium",children:"平均数量:"}),y.jsx("span",{className:"font-bold text-orange-700 text-base",children:at})]})]}),p.length>0&&y.jsxs("div",{className:"mt-5 pt-4 border-t border-gray-200/50",children:[y.jsxs("div",{className:"text-sm font-semibold text-gray-700 mb-3 flex items-center",children:[y.jsx("div",{className:"w-1 h-4 bg-gradient-to-b from-primary-500 to-primary-600 rounded-full mr-2"}),"物种分布"]}),y.jsx("div",{className:"space-y-2",children:p.map(z=>{const k=T.filter(W=>W.speciesId===z.id).reduce((W,et)=>W+et.count,0);return y.jsxs("div",{className:"flex items-center justify-between p-2 bg-gray-50/80 rounded-lg hover:bg-gray-100/80 transition-colors duration-200",children:[y.jsxs("div",{className:"flex items-center",children:[y.jsx("div",{className:"w-3 h-3 rounded-full mr-3 shadow-sm border border-white",style:{backgroundColor:z.color}}),y.jsx("span",{className:"text-gray-700 font-medium text-sm truncate max-w-24",children:z.name})]}),y.jsx("span",{className:"text-gray-900 font-bold text-sm",children:k.toLocaleString()})]},z.id)})})]})]})})},xp=({message:_="加载中..."})=>y.jsx("div",{className:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-[2000]",children:y.jsxs("div",{className:"text-center",children:[y.jsx(C_,{className:"h-8 w-8 animate-spin text-blue-600 mx-auto mb-2"}),y.jsx("p",{className:"text-sm text-gray-600",children:_})]})}),bp=()=>{const[_,T]=jt.useState(!1),x=[{key:"1-5",description:"切换对应物种的显示/隐藏"},{key:"Ctrl+A",description:"全选/全不选物种"},{key:"Esc",description:"关闭面板"}];return y.jsxs(y.Fragment,{children:[y.jsx("button",{onClick:()=>T(!0),className:"absolute top-4 left-1/2 transform -translate-x-1/2 z-[1000] bg-white hover:bg-gray-50 rounded-full p-2 shadow-lg border border-gray-200",title:"帮助和快捷键",children:y.jsx(x_,{className:"h-5 w-5 text-gray-600"})}),_&&y.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[3000] p-4",children:y.jsxs("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto",children:[y.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[y.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"帮助和快捷键"}),y.jsx("button",{onClick:()=>T(!1),className:"p-1 hover:bg-gray-100 rounded",children:y.jsx(jd,{className:"h-5 w-5 text-gray-500"})})]}),y.jsxs("div",{className:"p-4 space-y-6",children:[y.jsxs("div",{children:[y.jsx("h3",{className:"text-md font-medium text-gray-900 mb-2",children:"关于此应用"}),y.jsx("p",{className:"text-sm text-gray-600",children:"这是一个基于 eBird 的观鸟热点地图应用，展示了不同物种在各地区的分布情况。 您可以通过筛选和搜索功能来探索不同物种的观察数据。"})]}),y.jsxs("div",{children:[y.jsx("h3",{className:"text-md font-medium text-gray-900 mb-2",children:"主要功能"}),y.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[y.jsx("li",{children:"• 交互式地图显示物种分布"}),y.jsx("li",{children:"• 多物种图层叠加"}),y.jsx("li",{children:"• 物种筛选和搜索"}),y.jsx("li",{children:"• 地图类型切换"}),y.jsx("li",{children:"• 实时统计信息"})]})]}),y.jsxs("div",{children:[y.jsxs("div",{className:"flex items-center mb-2",children:[y.jsx(A_,{className:"h-4 w-4 text-gray-600 mr-2"}),y.jsx("h3",{className:"text-md font-medium text-gray-900",children:"键盘快捷键"})]}),y.jsx("div",{className:"space-y-2",children:x.map((p,R)=>y.jsxs("div",{className:"flex items-center justify-between text-sm",children:[y.jsx("span",{className:"font-mono bg-gray-100 px-2 py-1 rounded text-gray-800",children:p.key}),y.jsx("span",{className:"text-gray-600 flex-1 ml-3",children:p.description})]},R))})]}),y.jsxs("div",{children:[y.jsx("h3",{className:"text-md font-medium text-gray-900 mb-2",children:"地图操作"}),y.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[y.jsx("li",{children:"• 拖拽移动地图"}),y.jsx("li",{children:"• 滚轮缩放"}),y.jsx("li",{children:"• 点击标记查看详情"}),y.jsx("li",{children:"• 使用右侧控制面板切换图层"})]})]})]}),y.jsx("div",{className:"p-4 border-t border-gray-200 bg-gray-50",children:y.jsx("p",{className:"text-xs text-gray-500 text-center",children:"基于 React + Leaflet 构建 | 数据为模拟数据"})})]})})]})},Sp=({colorRanges:_=Mc,title:T="观察数量图例",className:x=""})=>y.jsxs("div",{className:`bg-white/95 backdrop-blur-sm rounded-xl shadow-large border border-white/20 p-4 ${x}`,children:[y.jsxs("div",{className:"flex items-center mb-3",children:[y.jsx("div",{className:"flex items-center justify-center w-6 h-6 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg mr-2",children:y.jsx(H_,{className:"h-3 w-3 text-white"})}),y.jsx("h3",{className:"text-sm font-bold text-gray-900",children:T})]}),y.jsx("div",{className:"space-y-2",children:_.map((p,R)=>y.jsx("div",{className:"flex items-center justify-between text-xs",children:y.jsxs("div",{className:"flex items-center",children:[y.jsx("div",{className:"w-4 h-4 rounded-sm border border-white/80 shadow-sm mr-2",style:{backgroundColor:p.color}}),y.jsx("span",{className:"text-gray-700 font-medium",children:p.label})]})},R))}),y.jsx("div",{className:"mt-3 pt-2 border-t border-gray-200/50",children:y.jsx("p",{className:"text-xs text-gray-600",children:"方块大小和颜色深度反映观察数量"})})]}),Tp=({className:_=""})=>{const{mapZoom:T,mapCenter:x}=xn(),p=ap(T),R=Sc(T,x[0]),q=wc(T,x[0]),P=(et,St)=>{if(St==="grid")return"网格聚合模式";switch(et){case"ultra":return"超高精度模式";case"high":return"高精度模式";case"points":return"坐标点模式";default:return"网格聚合模式"}},at=(et,St)=>{if(St==="grid")return"bg-blue-100 text-blue-800 border-blue-200";switch(et){case"ultra":return"bg-purple-100 text-purple-800 border-purple-200";case"high":return"bg-green-100 text-green-800 border-green-200";case"points":return"bg-orange-100 text-orange-800 border-orange-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},z=()=>[{level:Me.POINTS_THRESHOLD,name:"坐标点显示"},{level:Me.HIGH_DETAIL_THRESHOLD,name:"高精度模式"},{level:Me.ULTRA_DETAIL_THRESHOLD,name:"超高精度模式"}].map(St=>({...St,active:T>=St.level,distance:Math.abs(T-St.level)})),A=P(p,R),k=at(p,R),W=z();return y.jsx("div",{className:`bg-white rounded-lg shadow-md border p-3 ${_}`,children:y.jsxs("div",{className:"space-y-2",children:[y.jsxs("div",{className:"flex items-center justify-between",children:[y.jsx("span",{className:"text-xs text-gray-600",children:"缩放级别:"}),y.jsx("span",{className:"font-mono text-sm font-bold text-gray-800",children:T.toFixed(1)})]}),y.jsxs("div",{className:"flex items-center justify-between",children:[y.jsx("span",{className:"text-xs text-gray-600",children:"比例尺:"}),y.jsxs("span",{className:"font-mono text-xs text-gray-800",children:["1:",q.toLocaleString()]})]}),y.jsx("div",{className:"pt-2 border-t",children:y.jsxs("div",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${k}`,children:[y.jsx("div",{className:"w-2 h-2 rounded-full bg-current mr-1.5 opacity-60"}),A]})}),y.jsxs("div",{className:"pt-2 border-t",children:[y.jsx("div",{className:"text-xs text-gray-500 mb-1",children:"缩放阈值:"}),y.jsx("div",{className:"space-y-1",children:W.map((et,St)=>y.jsxs("div",{className:"flex items-center justify-between text-xs",children:[y.jsx("span",{className:`${et.active?"text-green-600 font-medium":"text-gray-400"}`,children:et.name}),y.jsxs("div",{className:"flex items-center",children:[y.jsx("span",{className:"font-mono text-xs mr-1",children:et.level}),y.jsx("div",{className:`w-2 h-2 rounded-full ${et.active?"bg-green-500":"bg-gray-300"}`})]})]},St))})]}),y.jsxs("div",{className:"pt-2 border-t",children:[y.jsx("div",{className:"text-xs text-gray-500 mb-1",children:"性能限制:"}),y.jsx("div",{className:"text-xs text-gray-600",children:R==="points"?y.jsxs(y.Fragment,{children:["最大点数: ",p==="ultra"?Me.MAX_POINTS_ULTRA_DETAIL.toLocaleString():p==="high"?Me.MAX_POINTS_HIGH_DETAIL.toLocaleString():Me.MAX_POINTS_DISPLAY.toLocaleString()]}):"网格聚合显示"})]})]})})};delete zi.Icon.Default.prototype._getIconUrl;zi.Icon.Default.mergeOptions({iconRetinaUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",iconUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",shadowUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png"});const wp=()=>{const _=jt.useRef(null),T=jt.useRef(null),[x,p]=jt.useState(!0),{mapCenter:R,mapZoom:q,setMapCenter:P,setMapZoom:at}=xn();return jt.useEffect(()=>{if(!_.current||T.current)return;const z=zi.map(_.current,{center:R,zoom:q,zoomControl:!1}),A=zi.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"© OpenStreetMap contributors",maxZoom:18});return A.on("load",()=>{p(!1)}),A.addTo(z),zi.control.zoom({position:"bottomright"}).addTo(z),z.on("moveend",()=>{const k=z.getCenter();P([k.lat,k.lng])}),z.on("zoomend",()=>{at(z.getZoom())}),T.current=z,()=>{T.current&&(T.current.remove(),T.current=null)}},[P,at]),y.jsxs("div",{className:"relative w-full h-full",children:[y.jsx("div",{ref:_,className:"w-full h-full"}),x&&y.jsx(xp,{message:"正在加载地图..."}),y.jsx(vp,{map:T.current}),y.jsx(gp,{}),y.jsx(yp,{}),y.jsx(bp,{}),y.jsx(Sp,{className:"absolute bottom-4 right-4 z-[1000] max-w-xs animate-slide-up"}),y.jsx(Tp,{className:"absolute bottom-4 left-4 z-[1000] max-w-xs animate-slide-up"}),y.jsx(ep,{map:T.current})]})},Mp=()=>{const{species:_,toggleSpeciesVisibility:T}=xn();jt.useEffect(()=>{const x=p=>{if(!(p.target instanceof HTMLInputElement||p.target instanceof HTMLTextAreaElement))switch(p.key.toLowerCase()){case"1":case"2":case"3":case"4":case"5":p.preventDefault();const R=parseInt(p.key)-1;_[R]&&T(_[R].id);break;case"a":if(p.ctrlKey||p.metaKey){p.preventDefault();const q=_.every(P=>P.isVisible);_.forEach(P=>{(q&&P.isVisible||!q&&!P.isVisible)&&T(P.id)})}break;case"escape":p.preventDefault();break}};return document.addEventListener("keydown",x),()=>{document.removeEventListener("keydown",x)}},[_,T])};function Ep(){const _=xn(T=>T.initializeData);return Mp(),jt.useEffect(()=>{_()},[_]),y.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-slate-100 animate-fade-in",children:[y.jsx(V_,{}),y.jsxs("main",{className:"flex flex-col h-screen",children:[y.jsx(I_,{}),y.jsx("div",{className:"flex-1 relative",children:y.jsx(wp,{})})]})]})}u_.createRoot(document.getElementById("root")).render(y.jsx(jt.StrictMode,{children:y.jsx(Ep,{})}));
