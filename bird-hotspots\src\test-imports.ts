// 测试导入是否正常工作
import { COLOR_RANGES, getColorByCount } from './utils/colorUtils';
import type { ColorRange } from './utils/colorUtils';

// 测试类型
const testRange: ColorRange = {
  min: 0,
  max: 10,
  color: '#ffffff',
  label: 'test'
};

// 测试函数
const testColor = getColorByCount(50);

// 测试常量
const testRanges = COLOR_RANGES;

console.log('Import test successful:', {
  testRange,
  testColor,
  rangesLength: testRanges.length
});

export { testRange, testColor, testRanges };
